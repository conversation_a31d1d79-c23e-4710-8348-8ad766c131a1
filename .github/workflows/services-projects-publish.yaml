name: services-projects-publish

on:
  release:
    types: [ published ]
  push:
    paths:
      - services/projects/**
    branches:
      - 'main'

jobs:
  publish-latest:
    if: ${{ github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}
    uses: fathom-io/gh-workflows/.github/workflows/java-publish.yaml@main
    with:
      ARTIFACT_ID: 'projects'
      IMAGE_NAME: 'projects'
      TAG: 'latest'
      JAVA_VERSION: '17'
      WORKING_DIRECTORY: './services/projects'
    secrets: inherit

  publish-stable:
    if: ${{ (github.event_name == 'release' && contains(github.event.release.body, 'service:projects'))}}
    uses: fathom-io/gh-workflows/.github/workflows/java-publish.yaml@main
    with:
      ARTIFACT_ID: 'projects'
      IMAGE_NAME: 'projects'
      TAG: 'stable'
      JAVA_VERSION: '17'
      WORKING_DIRECTORY: './services/projects'
    secrets: inherit
