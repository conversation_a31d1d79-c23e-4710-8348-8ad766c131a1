name: services-comments-publish

on:
  release:
    types: [ published ]
  push:
    paths:
      - services/comments/**
    branches:
      - 'main'

jobs:
  publish-latest:
    if: ${{ github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}
    uses: fathom-io/gh-workflows/.github/workflows/java-publish.yaml@main
    with:
      ARTIFACT_ID: 'comments'
      IMAGE_NAME: 'comments'
      TAG: 'latest'
      JAVA_VERSION: '17'
      WORKING_DIRECTORY: './services/comments'
    secrets: inherit

  publish-stable:
    if: ${{ (github.event_name == 'release' && contains(github.event.release.body, 'service:comments'))}}
    uses: fathom-io/gh-workflows/.github/workflows/java-publish.yaml@main
    with:
      ARTIFACT_ID: 'comments'
      IMAGE_NAME: 'comments'
      TAG: 'stable'
      JAVA_VERSION: '17'
      WORKING_DIRECTORY: './services/comments'
    secrets: inherit
