name: services-userprofile-ci

on:
  workflow_dispatch:
  pull_request:
    branches:
      - 'main'
    paths:
      - services/userprofile/**

jobs:
  build-and-test:
    uses: fathom-io/gh-workflows/.github/workflows/java-ci.yaml@main
    with:
      ARTIFACT_ID: 'userprofile'
      CONTINUE_ON_TEST_FAILURE: true
      JAVA_VERSION: '17'
      WORKING_DIRECTORY: './services/userprofile'
    secrets: inherit