name: lib-persistent-event-publisher-publish

on:
  release:
    types: [ published ]
  push:
    paths:
      - libs/persistent-event-publisher/**
    branches:
      - 'main'

jobs:
  publish-latest:
    if: ${{ github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}
    uses: fathom-io/gh-workflows/.github/workflows/java-library-publish.yaml@main
    secrets: inherit
    with:
      WORKING_DIRECTORY: './libs/persistent-event-publisher'

  publish-stable:
    if: ${{ (github.event_name == 'release' && contains(github.event.release.body, 'lib:persistent-event-publisher'))}}
    uses: fathom-io/gh-workflows/.github/workflows/java-library-publish.yaml@main
    with:
      WORKING_DIRECTORY: './libs/persistent-event-publisher'
      VERSION: ${{ github.ref_name }}
    secrets: inherit
