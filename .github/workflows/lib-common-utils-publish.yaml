name: lib-common-utils-publish

on:
  release:
    types: [ published ]
  push:
    paths:
      - libs/common-utils/**
    branches:
      - 'main'

jobs:
  publish-latest:
    if: ${{ github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}
    uses: fathom-io/gh-workflows/.github/workflows/java-library-publish.yaml@main
    secrets: inherit
    with:
      WORKING_DIRECTORY: './libs/common-utils'

  publish-stable:
    if: ${{ (github.event_name == 'release' && contains(github.event.release.body, 'lib:common-utils'))}}
    uses: fathom-io/gh-workflows/.github/workflows/java-library-publish.yaml@main
    with:
      WORKING_DIRECTORY: './libs/common-utils'
      VERSION: ${{ github.ref_name }}
    secrets: inherit
