name: lib-composition-publish

on:
  release:
    types: [ published ]
  push:
    paths:
      - libs/composition/**
    branches:
      - 'main'

jobs:
  publish-latest:
    if: ${{ github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}
    uses: fathom-io/gh-workflows/.github/workflows/java-library-publish.yaml@main
    secrets: inherit
    with:
      WORKING_DIRECTORY: './libs/composition'

  publish-stable:
    if: ${{ (github.event_name == 'release' && contains(github.event.release.body, 'lib:composition'))}}
    uses: fathom-io/gh-workflows/.github/workflows/java-library-publish.yaml@main
    with:
      WORKING_DIRECTORY: './libs/composition'
      VERSION: ${{ github.ref_name }}
    secrets: inherit
