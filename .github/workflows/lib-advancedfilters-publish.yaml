name: lib-advancedfilters-publish

on:
  release:
    types: [ published ]
  push:
    paths:
      - libs/advancedfilters/**
    branches:
      - 'main'

jobs:
  publish-latest:
    if: ${{ github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}
    uses: fathom-io/gh-workflows/.github/workflows/java-library-publish.yaml@main
    secrets: inherit
    with:
      WORKING_DIRECTORY: './libs/advancedfilters'

  publish-stable:
    if: ${{ (github.event_name == 'release' && contains(github.event.release.body, 'lib:advancedfilters'))}}
    uses: fathom-io/gh-workflows/.github/workflows/java-library-publish.yaml@main
    with:
      WORKING_DIRECTORY: './libs/advancedfilters'
      VERSION: ${{ github.ref_name }}
    secrets: inherit
