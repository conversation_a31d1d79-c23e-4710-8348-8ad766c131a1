name: services-usermanagement-publish

on:
  release:
    types: [ published ]
  push:
    paths:
      - services/usermanagement/**
    branches:
      - 'main'

jobs:
  publish-latest:
    if: ${{ github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}
    uses: fathom-io/gh-workflows/.github/workflows/java-publish.yaml@main
    with:
      ARTIFACT_ID: 'usermanagement'
      IMAGE_NAME: 'usermanagement'
      TAG: 'latest'
      JAVA_VERSION: '17'
      WORKING_DIRECTORY: './services/usermanagement'
    secrets: inherit

  publish-stable:
    if: ${{ (github.event_name == 'release' && contains(github.event.release.body, 'service:usermanagement'))}}
    uses: fathom-io/gh-workflows/.github/workflows/java-publish.yaml@main
    with:
      ARTIFACT_ID: 'usermanagement'
      IMAGE_NAME: 'usermanagement'
      TAG: 'stable'
      JAVA_VERSION: '17'
      WORKING_DIRECTORY: './services/usermanagement'
    secrets: inherit
