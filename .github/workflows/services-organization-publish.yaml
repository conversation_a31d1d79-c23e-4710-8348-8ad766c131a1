name: services-organization-publish

on:
  release:
    types: [ published ]
  push:
    paths:
      - services/organization/**
    branches:
      - 'main'

jobs:
  publish-latest:
    if: ${{ github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}
    uses: fathom-io/gh-workflows/.github/workflows/java-publish.yaml@main
    with:
      ARTIFACT_ID: 'organization'
      IMAGE_NAME: 'organization'
      TAG: 'latest'
      JAVA_VERSION: '17'
      WORKING_DIRECTORY: './services/organization'
    secrets: inherit

  publish-stable:
    if: ${{ (github.event_name == 'release' && contains(github.event.release.body, 'service:organization'))}}
    uses: fathom-io/gh-workflows/.github/workflows/java-publish.yaml@main
    with:
      ARTIFACT_ID: 'organization'
      IMAGE_NAME: 'organization'
      TAG: 'stable'
      JAVA_VERSION: '17'
      WORKING_DIRECTORY: './services/organization'
    secrets: inherit
