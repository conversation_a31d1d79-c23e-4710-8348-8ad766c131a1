name: services-notifications-publish

on:
  release:
    types: [ published ]
  push:
    paths:
      - services/notifications/**
    branches:
      - 'main'

jobs:
  publish-latest:
    if: ${{ github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}
    uses: fathom-io/gh-workflows/.github/workflows/java-publish.yaml@main
    with:
      ARTIFACT_ID: 'notifications'
      IMAGE_NAME: 'notifications'
      TAG: 'latest'
      JAVA_VERSION: '17'
      WORKING_DIRECTORY: './services/notifications'
    secrets: inherit

  publish-stable:
    if: ${{ (github.event_name == 'release' && contains(github.event.release.body, 'service:notifications'))}}
    uses: fathom-io/gh-workflows/.github/workflows/java-publish.yaml@main
    with:
      ARTIFACT_ID: 'notifications'
      IMAGE_NAME: 'notifications'
      TAG: 'stable'
      JAVA_VERSION: '17'
      WORKING_DIRECTORY: './services/notifications'
    secrets: inherit
