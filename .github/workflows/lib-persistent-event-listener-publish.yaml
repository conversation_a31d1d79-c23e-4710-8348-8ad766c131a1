name: lib-persistent-event-listener-publish

on:
  release:
    types: [ published ]
  push:
    paths:
      - libs/persistent-event-listener/**
    branches:
      - 'main'

jobs:
  publish-latest:
    if: ${{ github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}
    uses: fathom-io/gh-workflows/.github/workflows/java-library-publish.yaml@main
    secrets: inherit
    with:
      WORKING_DIRECTORY: './libs/persistent-event-listener'

  publish-stable:
    if: ${{ (github.event_name == 'release' && contains(github.event.release.body, 'lib:persistent-event-listener'))}}
    uses: fathom-io/gh-workflows/.github/workflows/java-library-publish.yaml@main
    with:
      WORKING_DIRECTORY: './libs/persistent-event-listener'
      VERSION: ${{ github.ref_name }}
    secrets: inherit
