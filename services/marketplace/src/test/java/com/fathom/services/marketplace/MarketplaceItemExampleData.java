package com.fathom.services.marketplace;

import com.fathom.services.marketplace.model.dto.MarketplaceItemCreateUpdateDto;
import com.fathom.services.marketplace.model.enums.Industry;
import com.fathom.services.marketplace.model.enums.PricingModel;
import com.fathom.services.marketplace.model.enums.ProductType;
import com.fathom.services.marketplace.model.enums.Publisher;

import java.time.LocalDateTime;

public interface MarketplaceItemExampleData {
    default MarketplaceItemCreateUpdateDto.MarketplaceItemCreateUpdateDtoBuilder aMarketplaceCreateUpdateDto() {
        return MarketplaceItemCreateUpdateDto.builder()
                .name("Test Marketplace Item")
                .subtitle("Test Subtitle")
                .details("Detailed description for testing")
                .author("Test Author")
                .type(ProductType.APPLICATION)
                .industry(Industry.FINANCE)
                .publisher(Publisher.FATHOM)
                .pricingModel("FREE")
                .organizationName("Test Organization")
                .projectId("project-123")
                .features("Feature 1, Feature 2")
                .releaseDate(LocalDateTime.now())
                .technicalInformation("Technical details here")
                .isDraft(false)
                .supportAndDocumentation("Support information")
                .appUrl("https://example.com/app");
    }
}
