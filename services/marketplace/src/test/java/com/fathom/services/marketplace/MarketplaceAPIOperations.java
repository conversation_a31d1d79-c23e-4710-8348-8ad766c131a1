package com.fathom.services.marketplace;

import com.fathom.services.marketplace.model.dto.MarketplaceItemCreateUpdateDto;
import com.fathom.services.marketplace.model.dto.MarketplaceItemDto;
import io.restassured.http.ContentType;
import org.springframework.http.HttpStatus;

import java.util.List;

import static com.fathom.services.marketplace.util.StaticProperties.EMAIL_HEADER;
import static io.restassured.RestAssured.given;

public interface MarketplaceAPIOperations {
    default List<MarketplaceItemDto> getMarketplaceItemsFilteredAsserting200(
            String jsonFilter, int pageNumber, int pageSize) {
        return given()
                .contentType(ContentType.JSON)
                .queryParam("pageNumber", pageNumber)
                .queryParam("pageSize", pageSize)
                .body(jsonFilter)
                .when()
                .post("marketplace-item/advanced-filters")
                .then()
                .log()
                .ifError()
                .statusCode(HttpStatus.OK.value())
                .extract()
                .body()
                .jsonPath()
                .getList("content", MarketplaceItemDto.class);
    }

    default MarketplaceItemDto createMarketplaceItemAssert200(String email, MarketplaceItemCreateUpdateDto item) {
        return given()
                .contentType(ContentType.JSON)
                .header(EMAIL_HEADER, email)
                .body(item)
                .when()
                .post("/marketplace-item")
                .then()
                .log()
                .ifError()
                .statusCode(HttpStatus.OK.value())
                .extract()
                .body()
                .as(MarketplaceItemDto.class);
    }

    default void createMarketplaceItemAssert400(String email, MarketplaceItemCreateUpdateDto item) {
        given()
                .contentType(ContentType.JSON)
                .header(EMAIL_HEADER, email)
                .body(item)
                .when()
                .post("/marketplace-item")
                .then()
                .log()
                .ifError()
                .statusCode(HttpStatus.BAD_REQUEST.value());
    }

    default void deleteMarketplaceItemAssert200(String email, long id, boolean force) {
        given()
                .contentType(ContentType.JSON)
                .header(EMAIL_HEADER, email)
                .when()
                .queryParam("force", force)
                .delete("/marketplace-item/" + id)
                .then()
                .log()
                .ifError()
                .statusCode(HttpStatus.OK.value());
    }

    default void putArchiveMarketplaceItemAssert200(String email, long id) {
        given()
                .contentType(ContentType.JSON)
                .header(EMAIL_HEADER, email)
                .when()
                .put("/marketplace-item/archive/" + id)
                .then()
                .log()
                .ifError()
                .statusCode(HttpStatus.OK.value());
    }

    // this is tech debt, it should be 404
    default void getMarketplaceItemByIdAssert400(long id) {
        given()
                .contentType(ContentType.JSON)
                .when()
                .get("/marketplace-item/" + id)
                .then()
                .log()
                .ifError()
                .statusCode(HttpStatus.BAD_REQUEST.value());
    }
}
