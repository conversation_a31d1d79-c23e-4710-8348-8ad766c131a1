package com.fathom.services.marketplace.controllers.impl;

import com.fathom.services.marketplace.IntegrationTestBase;
import io.restassured.RestAssured;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static io.restassured.RestAssured.when;

class OpenApiTest extends IntegrationTestBase {

  @BeforeEach
  public void setUp() {
    RestAssured.port = port;
  }

  @Test
  void testSwaggerUIReturns200() {
    when().get("/v3/api-docs").then().log().ifError().statusCode(200);
  }
}
