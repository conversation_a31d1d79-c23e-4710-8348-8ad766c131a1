package com.fathom.services.marketplace.controllers.impl;

import com.fathom.services.marketplace.IntegrationTestBase;
import com.fathom.services.marketplace.MarketplaceAPIOperations;
import com.fathom.services.marketplace.MarketplaceItemExampleData;
import io.restassured.RestAssured;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

class MarketplaceControllerTest extends IntegrationTestBase implements MarketplaceItemExampleData, MarketplaceAPIOperations {

    private static final String TEST_USER_EMAIL = "<EMAIL>";

    @BeforeEach
    void setUp() {
        RestAssured.port = port;
    }

    @Test
    void testCreateMarketplaceSucceedsOnValidRequest() {
        // given a marketplace create update dto
        var dto = aMarketplaceCreateUpdateDto().build();

        // when request to create marketplace item is made
        var response = createMarketplaceItemAssert200(TEST_USER_EMAIL, dto);

        assertThat(response).isNotNull();
        assertThat(response.getId()).isNotNull();
        assertThat(response.getName()).isEqualTo(dto.getName());
        assertThat(response.getIndustry()).isEqualTo(dto.getIndustry());
    }

    @Test
    void testCreateMarketplaceItemFailsWhenAppUrlIsEmpty() {
        // given a marketplace create update dto
        var dto = aMarketplaceCreateUpdateDto()
                .appUrl("")
                .build();

        // when request to create marketplace item is made
        createMarketplaceItemAssert400(TEST_USER_EMAIL, dto);
    }

    @Test
    void testCreateMarketplaceItemFailsWhenAppUrlIsNull() {
        // given a marketplace create update dto
        var dto = aMarketplaceCreateUpdateDto()
                .appUrl(null)
                .build();

        // when request to create marketplace item is made
        createMarketplaceItemAssert400(TEST_USER_EMAIL, dto);
    }

    @Test
    void testArchiveDeleteMarketplaceItemSucceedsOnValidRequest() {
        // given a marketplace item
        var dto = aMarketplaceCreateUpdateDto().build();

        var item = createMarketplaceItemAssert200(TEST_USER_EMAIL, dto);
        assertThat(item).isNotNull();
        assertThat(item.getId()).isNotNull();
        assertThat(item.getName()).isEqualTo(dto.getName());

        // when request to delete marketplace item is made
        putArchiveMarketplaceItemAssert200(TEST_USER_EMAIL, item.getId());
        deleteMarketplaceItemAssert200(TEST_USER_EMAIL, item.getId(), false);

        // then the item should no longer exist
        getMarketplaceItemByIdAssert400(item.getId());
    }
}