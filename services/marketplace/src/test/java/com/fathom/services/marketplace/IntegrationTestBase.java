package com.fathom.services.marketplace;

import org.junit.jupiter.api.BeforeAll;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.kafka.ConfluentKafkaContainer;
import org.testcontainers.utility.DockerImageName;
import org.wiremock.integrations.testcontainers.WireMockContainer;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public abstract class IntegrationTestBase {
  @LocalServerPort protected int port;

  @BeforeAll
  public static void beforeAll() {
    postgres.start();
    kafka.start();
    wiremockFileservice.start();
  }

  protected static final PostgreSQLContainer<?> postgres =
      new PostgreSQLContainer<>("postgres:12")
          .withExposedPorts(5432)
          .withReuse(true)
          .withLabel("reuse-id", "pgusermanagement");

  protected static final ConfluentKafkaContainer kafka =
      new ConfluentKafkaContainer(DockerImageName.parse("confluentinc/cp-kafka:7.5.2"))
          .withReuse(true)
          .withLabel("reuse-id", "kafkausermanagement");


  protected static WireMockContainer wiremockFileservice =
      new WireMockContainer("wiremock/wiremock:3.6.0")
          .withMappingFromResource("mocks-config-fileservice.json")
          .withReuse(true)
          .withLabel("reuse-id", "wiremockfsusermanagement");

  @DynamicPropertySource
  static void configureProperties(DynamicPropertyRegistry registry) {
    registry.add("spring.flyway.url", postgres::getJdbcUrl);
    registry.add("spring.flyway.user", postgres::getUsername);
    registry.add("spring.flyway.password", postgres::getPassword);
    registry.add("spring.flyway.locations", () -> "classpath:db/test-migration,classpath:db/migration");

    registry.add("spring.datasource.url", postgres::getJdbcUrl);
    registry.add("spring.datasource.username", postgres::getUsername);
    registry.add("spring.datasource.password", postgres::getPassword);
    registry.add("spring.kafka.bootstrap-servers", kafka::getBootstrapServers);

    registry.add("services.fileservice.name", wiremockFileservice::getBaseUrl);

  }
}
