package com.fathom.services.marketplace.controllers.impl;

import com.fathom.services.marketplace.IntegrationTestBase;
import com.fathom.services.marketplace.MarketplaceAPIOperations;
import com.fathom.services.marketplace.MarketplaceItemExampleData;
import io.restassured.RestAssured;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

public class MarketplaceFilteringTest extends IntegrationTestBase implements MarketplaceAPIOperations, MarketplaceItemExampleData {
    @BeforeEach
    public void setUp() {
        RestAssured.port = port;
    }

    @Test
    void advancedFilteringWithoutFilterDoesNotReturnBadRequest() {
        var json = """
                {
                  "filter": {
                    "@and": [
                      {
                        "@containsignorecase": {
                          "path": "name",
                          "value": "test"
                        }
                      }
                    ]
                  }
                }
                """;

        var filtered = getMarketplaceItemsFilteredAsserting200(json, 0, 10);

        assertThat(filtered).isNotNull();
    }
}
