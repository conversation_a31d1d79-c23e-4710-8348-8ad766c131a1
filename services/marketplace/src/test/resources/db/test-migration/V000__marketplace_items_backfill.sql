CREATE TABLE IF NOT EXISTS marketplace_items (
    id BIGSERIAL PRIMARY KEY,
    app_url VARCHAR,
    author <PERSON><PERSON><PERSON><PERSON>,
    author_email VARCHAR,
    created_date TIMESTAMP WITHOUT TIME ZONE,
    details TEXT,
    file_service_uuid UUID,
    industry SMALLINT,
    is_archive BOOLEAN,
    is_draft BOOLEAN,
    logo_file_id VARCHAR,
    name VA<PERSON><PERSON><PERSON>,
    organization_name VA<PERSON><PERSON><PERSON>,
    pricing_model SMALLINT,
    project_id VARCHAR,
    publisher SMALLINT,
    release_date TIMESTAMP WITHOUT TIME ZONE,
    subtitle VA<PERSON>HAR,
    support_and_documentation TEXT,
    technical_information TEXT
);