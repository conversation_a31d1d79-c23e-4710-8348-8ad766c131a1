-- Step 1: Drop the old column if it exists and is of type TEXT[]
DO $$
DECLARE column_type TEXT;
BEGIN
    -- Get the column type
    SELECT data_type INTO column_type
    FROM information_schema.columns
    WHERE table_name = 'marketplace_items'
      AND column_name = 'features';

    -- Check if it's an array (TEXT[])
    IF column_type = 'ARRAY' THEN
        ALTER TABLE marketplace_items DROP COLUMN features;
    END IF;
END $$;

-- Step 2: Add the new column as TEXT (if it doesn’t already exist)
ALTER TABLE marketplace_items ADD COLUMN IF NOT EXISTS features TEXT;
