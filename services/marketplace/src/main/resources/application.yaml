server:
  port: ${MARKETPLACE_SERVICE_PORT:8077}

springdoc:
  api-docs:
    version: "openapi_3_0"
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
logging:
  level:
    root: INFO
    org:
      apache:
        kafka: OFF
      springframework:
        boot:
          autoconfigure:
            web:
              reactive: WARN
        cloud:
          gateway: WARN
        hibernate:
          SQL: DEBUG
          type:
            descriptor:
              sql:
                BasicBinder: INFO
spring:
  application:
    name: ${MARKETPLACE_SERVICE_NAME:marketplace}
  flyway:
    enabled: ${FLYWAY_MIGRATION_ENABLED:true}
    locations: classpath:db/migration
    url: jdbc:postgresql://${POSTGRES_HOST:localhost}:${POSTGRES_PORT:5432}/${POSTGRES_DATABASE:fathom}?currentSchema=${AM_SCHEMA:public}
    user: ${POSTGRES_USER:postgres}
    password: ${POSTGRES_PASSWORD:postgres}
    baseline-on-migrate: true
  datasource:
    url: jdbc:postgresql://${POSTGRES_HOST:localhost}:${POSTGRES_PORT:5432}/${POSTGRES_DATABASE:fathom}?currentSchema=${AM_SCHEMA:public}
    username: ${POSTGRES_USER:postgres}
    password: ${POSTGRES_PASSWORD:postgres}
  jpa:
    open-in-view: false
    show-sql: ${JPA_SHOW_SQL:true}
    properties:
      hibernate:
        format_sql: true
    database: postgresql
    hibernate:
      ddl-auto:  ${JPA_DDL_AUTO:update}
  cloud:
    loadbalancer:
      ribbon:
        enabled: false
    kubernetes:
      loadbalancer:
        mode: service
      discovery:
        all-namespaces: false
  sql.init.mode: always

services:
  fileservice:
    name: ${FILE_SERVICE_NAME:fileservice:8015}

hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 10000
      circuitBreaker:
        requestVolumeThreshold: 5
        errorThresholdPercentage: 50
        sleepWindowInMilliseconds: 10000
      metrics:
        rollingStats:
          timeInMilliseconds: 10000

fathom:
  persistent:
    events:
      organization:
        kafka-topic-name: ${PERSISTENT_EVENTS_ORGANIZATION_KAFKA_TOPIC_NAME:fathom.organization.persistent.events}

diagnostics:
  service-name: "marketplace"
  version: ${VERSION:0.0.1-dev}
  build-timestamp: ${BUILD_TIMESTAMP:1970-01-01T00:00:00Z}
  git-hash: ${GIT_HASH:unknown}
  git-branch: ${GIT_BRANCH:main}
  git-commit-timestamp: ${GIT_COMMIT_TIMESTAMP:1970-01-01T00:00:00Z}
