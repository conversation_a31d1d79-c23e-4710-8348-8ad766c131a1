package com.fathom.services.marketplace.model.enums;

import lombok.Getter;

@Getter
public enum PricingModel {
  FREE(0, "Free"),
  FREE_TRIAL(1, "Free Trial"),
  USAGE_BASE(2, "Usage Base"),
  SUBSCRIPTION(3, "Subscription");

  private final int value;
  private final String displayName;

  PricingModel(int value, String displayName) {
    this.value = value;
    this.displayName = displayName;
  }

  public static PricingModel fromValue(String value) {
    try {
      int intValue = Integer.parseInt(value);
      for (PricingModel type : values()) {
        if (type.getValue() == intValue) {
          return type;
        }
      }
    } catch (NumberFormatException e) {
      // Handle invalid number format
      throw new RuntimeException("Invalid value for ProductType: " + value);
    }
    return null; // Return null if no matching enum is found
  }
}
