package com.fathom.services.marketplace.model;

import com.fathom.services.marketplace.model.enums.Industry;
import com.fathom.services.marketplace.model.enums.PricingModel;
import com.fathom.services.marketplace.model.enums.ProductType;
import com.fathom.services.marketplace.model.enums.Publisher;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "marketplace_items")
public class MarketplaceItem {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", updatable = false, nullable = false)
  Long id;

  @Column(nullable = false)
  String name;

  String subtitle;
  String author;
  ProductType type;
  Industry industry;
  Publisher publisher;
  PricingModel pricingModel;
  String organizationName;
  String projectId;

  @OneToMany(mappedBy = "marketplaceItem", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  private List<MarketplaceComment> marketplaceComments = new ArrayList<>();

  @Column(nullable = false)
  String authorEmail;

  LocalDateTime createdDate;
  LocalDateTime updatedDate;

  String logoFileId;
  UUID fileServiceUuid;

  boolean isArchive;

  @Column(nullable = false, columnDefinition = "BOOLEAN DEFAULT false")
  boolean isDraft = true;

  String appUrl;

  @Lob
  @Basic(fetch = FetchType.LAZY)
  @Column(columnDefinition = "TEXT")
  private String details;

  @Lob
  @Basic(fetch = FetchType.LAZY)
  @Column(columnDefinition = "TEXT")
  private String features;

  @Column(name = "release_date")
  private LocalDateTime releaseDate;

  @Lob
  @Basic(fetch = FetchType.LAZY)
  @Column(columnDefinition = "TEXT")
  private String technicalInformation;

  @Column(name = "versions")
  private Set<String> versions = new HashSet<>();

  @Lob
  @Basic(fetch = FetchType.LAZY)
  @Column(columnDefinition = "TEXT")
  private String supportAndDocumentation;

  @PrePersist
  private void prePersist() {
    LocalDateTime now = LocalDateTime.now(ZoneId.of("UTC"));
    this.createdDate = now;
    this.updatedDate = now;
  }

  @PreUpdate
  private void preUpdate() {
    LocalDateTime now = LocalDateTime.now(ZoneId.of("UTC"));
    if (this.createdDate == null) {
      this.createdDate = now;
    }
    this.updatedDate = now;
  }
}
