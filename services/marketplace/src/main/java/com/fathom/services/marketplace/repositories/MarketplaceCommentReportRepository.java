package com.fathom.services.marketplace.repositories;

import com.fathom.services.marketplace.model.MarketplaceCommentReport;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MarketplaceCommentReportRepository
    extends JpaRepository<MarketplaceCommentReport, Long> {
  boolean existsByUserEmailAndCommentId(String userEmail, Long commentId);

  List<MarketplaceCommentReport> findByCommentId(Long commentId);
}
