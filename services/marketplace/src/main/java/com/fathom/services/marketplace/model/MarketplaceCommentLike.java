package com.fathom.services.marketplace.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(
    name = "marketplace_comment_likes",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"user_email", "comment_id"})})
public class MarketplaceCommentLike {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "user_email", nullable = false)
  private String userEmail;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "comment_id", nullable = false)
  private MarketplaceComment comment;
}
