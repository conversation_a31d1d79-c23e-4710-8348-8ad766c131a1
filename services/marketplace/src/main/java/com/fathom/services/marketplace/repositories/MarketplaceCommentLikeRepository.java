package com.fathom.services.marketplace.repositories;

import com.fathom.services.marketplace.model.MarketplaceCommentLike;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MarketplaceCommentLikeRepository
    extends JpaRepository<MarketplaceCommentLike, Long> {
  boolean existsByUserEmailAndCommentId(String userEmail, Long commentId);

  void deleteByUserEmailAndCommentId(String userEmail, Long commentId);

  List<MarketplaceCommentLike> findByCommentId(Long commentId);
}
