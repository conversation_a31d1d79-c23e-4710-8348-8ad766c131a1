package com.fathom.services.marketplace.model.dto;

import com.fathom.services.marketplace.model.enums.Industry;
import com.fathom.services.marketplace.model.enums.PricingModel;
import com.fathom.services.marketplace.model.enums.ProductType;
import com.fathom.services.marketplace.model.enums.Publisher;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class MarketplaceItemCreateUpdateDto {

  @NotBlank(message = "name cannot be empty")
  String name;

  String subtitle;
  String details;

  @NotBlank(message = "author name cannot be empty")
  String author;

  @NotNull(message = "type cannot be empty")
  ProductType type;

  @NotNull(message = "industry cannot be empty")
  Industry industry;

  @NotNull(message = "publisher cannot be empty")
  Publisher publisher;

  @NotNull(message = "pricing model cannot be empty")
  String pricingModel;

  String organizationName;
  String projectId;

  String features;
  LocalDateTime releaseDate;
  String technicalInformation;
  Set<String> versions = new HashSet<>();
  String supportAndDocumentation;

  boolean isDraft = true;

  @NotBlank(message = "appUrl cannot be empty")
  String appUrl;
}
