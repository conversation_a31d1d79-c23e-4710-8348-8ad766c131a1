package com.fathom.services.marketplace.services;

import com.fathom.services.marketplace.model.dto.*;
import java.util.List;
import org.springframework.data.domain.Page;

public interface MarketplaceCommentService {

  Page<MarketplaceCommentDto> getMarketplaceCommentByMarketplaceId(
      Long marketplaceId, int pageNumber, int pageSize, boolean pageable);

  MarketplaceCommentDto createMarketplaceComment(
      Long marketplaceId,
      String userEmail,
      MarketplaceCommentCreateUpdateDto marketplaceCreateUpdateDto);

  void deleteMarketplaceComment(String userEmail, Long id);

  void likeComment(String userEmail, Long id);

  void unlikeComment(String userEmail, Long id);

  List<MarketplaceCommentLikeDTO> getLikesByCommentId(Long commentId);

  MarketplaceCommentReportDto reportComment(
      String userEmail, Long commentId, MarketplaceCommentReportCreateDto request);

  List<MarketplaceCommentReportDto> getReportsForComment(Long commentId);
}
