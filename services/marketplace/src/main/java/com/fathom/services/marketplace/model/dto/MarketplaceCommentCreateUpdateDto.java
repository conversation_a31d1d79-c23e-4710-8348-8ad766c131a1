package com.fathom.services.marketplace.model.dto;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class MarketplaceCommentCreateUpdateDto {
  String title;

  @NotBlank(message = "Comment cannot be empty")
  String comment;

  @Min(value = 0, message = "Rating must be at least 0")
  @Max(value = 5, message = "Rating must be at most 5")
  float rating;
}
