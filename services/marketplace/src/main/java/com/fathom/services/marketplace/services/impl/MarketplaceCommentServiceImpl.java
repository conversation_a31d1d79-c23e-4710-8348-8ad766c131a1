package com.fathom.services.marketplace.services.impl;

import com.fathom.lib.common.exception.RestException;
import com.fathom.services.marketplace.mapper.MarketplaceCommentLikeMapper;
import com.fathom.services.marketplace.mapper.MarketplaceCommentMapper;
import com.fathom.services.marketplace.mapper.MarketplaceCommentReportMapper;
import com.fathom.services.marketplace.model.MarketplaceComment;
import com.fathom.services.marketplace.model.MarketplaceCommentLike;
import com.fathom.services.marketplace.model.MarketplaceCommentReport;
import com.fathom.services.marketplace.model.MarketplaceItem;
import com.fathom.services.marketplace.model.dto.*;
import com.fathom.services.marketplace.repositories.MarketplaceCommentLikeRepository;
import com.fathom.services.marketplace.repositories.MarketplaceCommentReportRepository;
import com.fathom.services.marketplace.repositories.MarketplaceCommentRepository;
import com.fathom.services.marketplace.services.MarketplaceCommentService;
import com.fathom.services.marketplace.services.MarketplaceService;
import jakarta.transaction.Transactional;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class MarketplaceCommentServiceImpl implements MarketplaceCommentService {
  private final MarketplaceCommentRepository marketplaceCommentRepository;
  private final MarketplaceCommentMapper marketplaceCommentMapper;
  private final MarketplaceService marketplaceService;
  private final MarketplaceCommentLikeRepository likeRepository;
  private final MarketplaceCommentReportRepository reportRepository;
  private final MarketplaceCommentLikeMapper likeMapper;
  private final MarketplaceCommentReportMapper reportMapper;

  @Override
  public Page<MarketplaceCommentDto> getMarketplaceCommentByMarketplaceId(
      Long marketplaceId, int pageNumber, int pageSize, boolean pageable) {

    return pageable
        ? marketplaceCommentRepository.findCommentsWithLikeCount(
            marketplaceId, PageRequest.of(pageNumber, pageSize))
        : new PageImpl<>(
            marketplaceCommentRepository.findCommentsWithLikeCount(marketplaceId),
            PageRequest.of(0, pageSize),
            pageSize);
  }

  @Override
  public MarketplaceCommentDto createMarketplaceComment(
      Long marketplaceId,
      String userEmail,
      MarketplaceCommentCreateUpdateDto marketplaceCreateUpdateDto) {

    MarketplaceItem marketplaceItem = marketplaceService.getMarketplaceById(marketplaceId);
    MarketplaceComment marketplaceComment =
        marketplaceCommentMapper.toEntity(userEmail, marketplaceCreateUpdateDto);
    marketplaceComment.setMarketplaceItem(marketplaceItem);

    return marketplaceCommentMapper.toDto(marketplaceCommentRepository.save(marketplaceComment));
  }

  @Override
  public void deleteMarketplaceComment(String userEmail, Long id) {

    MarketplaceComment marketplaceComment = getMarketplaceComment(id);
    marketplaceCommentRepository.delete(marketplaceComment);
  }

  @Override
  @Transactional
  public void likeComment(String userEmail, Long id) {
    if (likeRepository.existsByUserEmailAndCommentId(userEmail, id)) {
      throw new RestException(HttpStatus.BAD_REQUEST, "User already liked this comment.");
    }

    MarketplaceComment comment =
        marketplaceCommentRepository
            .findById(id)
            .orElseThrow(() -> new RestException(HttpStatus.NOT_FOUND, "Comment not found"));

    MarketplaceCommentLike like = new MarketplaceCommentLike();
    like.setUserEmail(userEmail);
    like.setComment(comment);
    likeRepository.save(like);
  }

  @Override
  @Transactional
  public void unlikeComment(String userEmail, Long id) {
    if (!likeRepository.existsByUserEmailAndCommentId(userEmail, id)) {
      throw new RestException(HttpStatus.BAD_REQUEST, "User has not liked this comment.");
    }

    likeRepository.deleteByUserEmailAndCommentId(userEmail, id);
  }

  @Override
  public List<MarketplaceCommentLikeDTO> getLikesByCommentId(Long commentId) {

    List<MarketplaceCommentLike> likes = likeRepository.findByCommentId(commentId);
    return likeMapper.toDtoList(likes);
  }

  @Override
  @Transactional
  public MarketplaceCommentReportDto reportComment(
      String userEmail, Long commentId, MarketplaceCommentReportCreateDto request) {

    if (reportRepository.existsByUserEmailAndCommentId(userEmail, commentId)) {
      throw new RestException(HttpStatus.BAD_REQUEST, "User has already reported this comment.");
    }

    MarketplaceComment comment =
        marketplaceCommentRepository
            .findById(commentId)
            .orElseThrow(() -> new RuntimeException("Comment not found"));

    MarketplaceCommentReport report = new MarketplaceCommentReport();
    report.setComment(comment);
    report.setUserEmail(userEmail);
    report.setReportReason(request.getReportReason());

    return reportMapper.toDto(reportRepository.save(report));
  }

  @Override
  @Transactional
  public List<MarketplaceCommentReportDto> getReportsForComment(Long commentId) {
    List<MarketplaceCommentReport> reports = reportRepository.findByCommentId(commentId);
    return reportMapper.toDtoList(reports);
  }

  private MarketplaceComment getMarketplaceComment(Long id) {
    return marketplaceCommentRepository
        .findById(id)
        .orElseThrow(
            () -> new RestException(HttpStatus.NOT_FOUND, "Comment with id: %s doesn't exist", id));
  }
}
