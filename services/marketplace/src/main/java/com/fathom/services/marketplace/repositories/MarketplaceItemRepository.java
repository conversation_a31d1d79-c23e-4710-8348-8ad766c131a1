package com.fathom.services.marketplace.repositories;

import com.fathom.services.marketplace.model.MarketplaceItem;
import com.fathom.services.marketplace.model.dto.MarketplaceItemDto;
import java.util.*;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface MarketplaceItemRepository
    extends JpaRepository<MarketplaceItem, Long>, JpaSpecificationExecutor<MarketplaceItem> {

  // Common query fragment for fetching MarketplaceItemDto
  String MARKETPLACE_DTO_QUERY =
      """
          SELECT new com.fathom.services.marketplace.model.dto.MarketplaceItemDto(
              m.id,
              m.name,
              m.subtitle,
              m.author,
              m.type,
              m.industry,
              m.publisher,
              m.pricingModel,
              m.organizationName,
              m.projectId,
              m.authorEmail,
              m.isArchive,
              m.isDraft,
              m.appUrl,
              m.createdDate,
              COALESCE(MAX(c.updatedDate), m.createdDate),
              m.logoFileId,
              m.fileServiceUuid,
              COUNT(c.id),
              COALESCE(AVG(c.rating), 0),
              m.details,
              m.features,
              m.releaseDate,
              m.technicalInformation,
              m.supportAndDocumentation)
          FROM MarketplaceItem m
          LEFT JOIN m.marketplaceComments c
      """;

  @Query("SELECT m FROM MarketplaceItem m WHERE m.id IN ?1")
  Set<MarketplaceItem> findByOrganizationIdAndIdIn(Collection<Long> ids);

  @Query(MARKETPLACE_DTO_QUERY + "GROUP BY m.id")
  Page<MarketplaceItemDto> findWithCommentCountAndAverageRating(Pageable pageable);

  @Query(MARKETPLACE_DTO_QUERY + "WHERE m.id = :id " + "GROUP BY m.id")
  Optional<MarketplaceItemDto> findWithCommentCountAndAverageRating(long id);

  @Query(
      value =
          """
        SELECT DISTINCT CAST(type AS TEXT) AS field, 'type' AS field_type FROM marketplace_items
        UNION ALL
        SELECT DISTINCT CAST(industry AS TEXT) AS field, 'industry' AS field_type FROM marketplace_items
        UNION ALL
        SELECT DISTINCT CAST(publisher AS TEXT) AS field, 'publisher' AS field_type FROM marketplace_items
        UNION ALL
        SELECT DISTINCT CAST(pricing_model AS TEXT) AS field, 'pricingModel' AS field_type FROM marketplace_items
        """,
      nativeQuery = true)
  List<Object[]> findDistinctFields();

  List<MarketplaceItem> findAllByIsDraftNull();

  @Query(
      "SELECT mi.id AS string, "
          + "       COALESCE(COUNT(mc), 0), "
          + "       COALESCE(AVG(mc.rating), 0.0) "
          + "FROM MarketplaceItem mi "
          + "LEFT JOIN MarketplaceComment mc ON mc.marketplaceItem.id = mi.id "
          + "WHERE mi.id IN :itemIds "
          + "GROUP BY mi.id")
  List<Object[]> findAverageRatingAndCountByItemIds(@Param("itemIds") List<Long> itemIds);

  default Map<Long, Pair<Integer, Double>> getAverageRatingsAndCounts(List<Long> itemIds) {
    List<Object[]> results = findAverageRatingAndCountByItemIds(itemIds);

    Map<Long, Pair<Integer, Double>> response = new HashMap<>();
    for (Object[] result : results) {
      Long itemId = (Long) result[0]; // ID as String
      Long count = ((Number) result[1]).longValue(); // Convert to Long for safety
      Double averageRating = (Double) result[2];

      response.put(itemId, Pair.of(count.intValue(), averageRating));
    }

    return response;
  }
}
