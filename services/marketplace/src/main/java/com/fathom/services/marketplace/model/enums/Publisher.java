package com.fathom.services.marketplace.model.enums;

import lombok.Getter;

@Getter
public enum Publisher {
  FATHOM(0, "Fathom"),
  PARTNER(1, "Partner");

  private final int value;
  private final String displayName;

  Publisher(int value, String displayName) {
    this.value = value;
    this.displayName = displayName;
  }

  public static Publisher fromValue(String value) {
    try {
      int intValue = Integer.parseInt(value);
      for (Publisher type : values()) {
        if (type.getValue() == intValue) {
          return type;
        }
      }
    } catch (NumberFormatException e) {
      // Handle invalid number format
      throw new RuntimeException("Invalid value for ProductType: " + value);
    }
    return null; // Return null if no matching enum is found
  }
}
