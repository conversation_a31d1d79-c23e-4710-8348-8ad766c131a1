package com.fathom.services.marketplace.controllers.impl;

import static com.fathom.services.marketplace.util.StaticProperties.EMAIL_HEADER;

import com.fathom.services.marketplace.controllers.MarketplaceCommentControllerV1;
import com.fathom.services.marketplace.model.dto.*;
import com.fathom.services.marketplace.services.MarketplaceCommentService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
@RequiredArgsConstructor
public class MarketplaceCommentController implements MarketplaceCommentControllerV1 {
  private final MarketplaceCommentService marketplaceCommentService;

  @Override
  @GetMapping("marketplace-item/comment/{marketplaceId}")
  public ResponseEntity<Page<MarketplaceCommentDto>> getMarketplaceCommentByMarketplaceId(
      @PathVariable long marketplaceId,
      @RequestParam(defaultValue = "0", required = false) int pageNumber,
      @RequestParam(defaultValue = "10", required = false) int pageSize,
      @RequestParam(defaultValue = "false", required = false) boolean pageable) {
    return ResponseEntity.ok(
        marketplaceCommentService.getMarketplaceCommentByMarketplaceId(
            marketplaceId, pageNumber, pageSize, pageable));
  }

  @Override
  @PostMapping("marketplace-item/comment/{marketplaceId}")
  public ResponseEntity<MarketplaceCommentDto> createMarketplaceComment(
      @PathVariable long marketplaceId,
      @RequestHeader(EMAIL_HEADER) String userEmail,
      @RequestBody @Valid MarketplaceCommentCreateUpdateDto marketplaceCreateUpdateDto) {

    return ResponseEntity.ok(
        marketplaceCommentService.createMarketplaceComment(
            marketplaceId, userEmail, marketplaceCreateUpdateDto));
  }

  @Override
  @DeleteMapping("marketplace-item/comment/{id}")
  public ResponseEntity<MarketplaceCommentDto> deleteMarketplaceComment(
      @RequestHeader(EMAIL_HEADER) String userEmail, @PathVariable long id) {
    marketplaceCommentService.deleteMarketplaceComment(userEmail, id);
    return ResponseEntity.ok().build();
  }

  @Override
  @PostMapping("marketplace-item/comment/{id}/like")
  public ResponseEntity<String> likeComment(
      @RequestHeader(EMAIL_HEADER) String userEmail, @PathVariable Long id) {
    marketplaceCommentService.likeComment(userEmail, id);
    return ResponseEntity.ok("Comment liked!");
  }

  @Override
  @PostMapping("marketplace-item/comment/{id}/unlike")
  public ResponseEntity<String> unlikeComment(
      @RequestHeader(EMAIL_HEADER) String userEmail, @PathVariable Long id) {

    marketplaceCommentService.unlikeComment(userEmail, id);
    return ResponseEntity.ok("Comment unliked!");
  }

  @Override
  @GetMapping("marketplace-item/comment/{id}/likes")
  public ResponseEntity<List<MarketplaceCommentLikeDTO>> getLikesByCommentId(
      @PathVariable Long id) {
    List<MarketplaceCommentLikeDTO> likes = marketplaceCommentService.getLikesByCommentId(id);
    return ResponseEntity.ok(likes);
  }

  @Override
  @PostMapping("marketplace-item/comment/{id}/report")
  public ResponseEntity<MarketplaceCommentReportDto> reportComment(
      @RequestHeader(EMAIL_HEADER) String userEmail,
      @PathVariable Long id,
      @RequestBody MarketplaceCommentReportCreateDto requestDto) {

    return ResponseEntity.ok(marketplaceCommentService.reportComment(userEmail, id, requestDto));
  }

  @Override
  @GetMapping("marketplace-item/comment/{id}/reports")
  public ResponseEntity<List<MarketplaceCommentReportDto>> getReportsForComment(
      @PathVariable Long id) {
    return ResponseEntity.ok(marketplaceCommentService.getReportsForComment(id));
  }
}
