package com.fathom.services.marketplace.model.dto.sorting;

import com.fathom.services.marketplace.model.dto.MarketplaceItemDto;

import java.time.LocalDateTime;
import java.util.Comparator;

public class MarketplaceItemDtoComparatorFactory {
    public static Comparator<MarketplaceItemDto> getComparator(String orderBy) {
        return switch (orderBy) {
            case "id" -> Comparator.comparing(MarketplaceItemDto::getId, Comparator.nullsLast(Comparator.naturalOrder()));
            case "name" -> Comparator.comparing(MarketplaceItemDto::getName, Comparator.nullsLast(Comparator.naturalOrder()));
            case "subtitle" -> Comparator.comparing(MarketplaceItemDto::getSubtitle, Comparator.nullsLast(Comparator.naturalOrder()));
            case "details" ->
                    Comparator.comparing(MarketplaceItemDto::getDetails, Comparator.nullsLast(Comparator.naturalOrder()));
            case "author" -> Comparator.comparing(MarketplaceItemDto::getAuthor, Comparator.nullsLast(Comparator.naturalOrder()));
            case "type" -> Comparator.comparing(MarketplaceItemDto::getType, Comparator.nullsLast(Comparator.naturalOrder()));
            case "industry" ->
                    Comparator.comparing(MarketplaceItemDto::getIndustry, Comparator.nullsLast(Comparator.naturalOrder()));
            case "publisher" ->
                    Comparator.comparing(MarketplaceItemDto::getPublisher, Comparator.nullsLast(Comparator.naturalOrder()));
            case "pricingModel" ->
                    Comparator.comparing(MarketplaceItemDto::getPricingModel, Comparator.nullsLast(Comparator.naturalOrder()));
            case "organizationName" ->
                    Comparator.comparing(MarketplaceItemDto::getOrganizationName, Comparator.nullsLast(Comparator.naturalOrder()));
            case "projectId" -> Comparator.comparing(MarketplaceItemDto::getProjectId, Comparator.nullsLast(Comparator.naturalOrder()));
            case "authorEmail" ->
                    Comparator.comparing(MarketplaceItemDto::getAuthorEmail, Comparator.nullsLast(Comparator.naturalOrder()));
            case "isArchive" ->
                    Comparator.comparing(MarketplaceItemDto::isArchive, Comparator.nullsLast(Boolean::compare));
            case "isDraft" -> Comparator.comparing(MarketplaceItemDto::isDraft, Comparator.nullsLast(Boolean::compare));
            case "appUrl" -> Comparator.comparing(MarketplaceItemDto::getAppUrl, Comparator.nullsLast(Comparator.naturalOrder()));
            case "commentsCount" ->
                    Comparator.comparing(MarketplaceItemDto::getCommentsCount, Comparator.nullsLast(Long::compare));
            case "averageRating" ->
                    Comparator.comparing(MarketplaceItemDto::getAverageRating, Comparator.nullsLast(Double::compare));
            case "features" ->
                    Comparator.comparing(MarketplaceItemDto::getFeatures, Comparator.nullsLast(Comparator.naturalOrder()));
            case "releaseDate" ->
                    Comparator.comparing(MarketplaceItemDto::getReleaseDate, Comparator.nullsLast(LocalDateTime::compareTo));
            case "technicalInformation" ->
                    Comparator.comparing(MarketplaceItemDto::getTechnicalInformation, Comparator.nullsLast(Comparator.naturalOrder()));
            case "supportAndDocumentation" ->
                    Comparator.comparing(MarketplaceItemDto::getSupportAndDocumentation, Comparator.nullsLast(Comparator.naturalOrder()));
            case "createdDate" ->
                    Comparator.comparing(MarketplaceItemDto::getCreatedDate, Comparator.nullsLast(LocalDateTime::compareTo));
            case "updatedDate" ->
                    Comparator.comparing(MarketplaceItemDto::getUpdatedDate, Comparator.nullsLast(LocalDateTime::compareTo));
            default -> throw new IllegalArgumentException("Invalid order by field: " + orderBy);
        };
    }
}
