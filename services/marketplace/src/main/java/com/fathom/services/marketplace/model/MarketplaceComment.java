package com.fathom.services.marketplace.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "marketplace_comment")
public class MarketplaceComment {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "marketplace_item_id", referencedColumnName = "id")
  MarketplaceItem marketplaceItem;

  String title;

  @Column(length = 10000)
  String comment;

  String commentAuthor;
  float rating;

  LocalDateTime createdDate;
  LocalDateTime updatedDate;

  @OneToMany(mappedBy = "comment", cascade = CascadeType.ALL, orphanRemoval = true)
  private Set<MarketplaceCommentLike> likes;

  @OneToMany(mappedBy = "comment", cascade = CascadeType.ALL, orphanRemoval = true)
  private Set<MarketplaceCommentReport> reports;

  @PrePersist
  private void prePersist() {
    LocalDateTime now = LocalDateTime.now(ZoneId.of("UTC"));
    this.createdDate = now;
    this.updatedDate = now;
  }

  @PreUpdate
  private void preUpdate() {
    this.updatedDate = LocalDateTime.now(ZoneId.of("UTC"));
  }

  public int getLikeCount() {
    return likes != null ? likes.size() : 0;
  }
}
