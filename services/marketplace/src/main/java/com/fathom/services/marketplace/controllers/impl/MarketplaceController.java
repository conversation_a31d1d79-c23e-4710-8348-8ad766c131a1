package com.fathom.services.marketplace.controllers.impl;

import static com.fathom.services.marketplace.util.StaticProperties.EMAIL_HEADER;

import com.fathom.lib.advancedfilters.model.CustomFilter;
import com.fathom.services.marketplace.controllers.MarketplaceControllerV1;
import com.fathom.services.marketplace.model.dto.MarketplaceItemCreateUpdateDto;
import com.fathom.services.marketplace.model.dto.MarketplaceItemDto;
import com.fathom.services.marketplace.services.MarketplaceService;
import java.util.Map;
import java.util.Set;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@Slf4j
@RequiredArgsConstructor
public class MarketplaceController implements MarketplaceControllerV1 {
  private final MarketplaceService marketplaceService;

  @Override
  @GetMapping("marketplace-item")
  public ResponseEntity<Page<MarketplaceItemDto>> getMarketplaces(
      @RequestParam(defaultValue = "0", required = false) int pageNumber,
      @RequestParam(defaultValue = "10", required = false) int pageSize,
      @RequestParam(defaultValue = "false", required = false) boolean pageable) {

    return ResponseEntity.ok(
        marketplaceService.getMarketplaceByOrganizationId(pageNumber, pageSize, pageable));
  }

  @Override
  @GetMapping("marketplace-item/{id}")
  public ResponseEntity<MarketplaceItemDto> getMarketplaceById(@PathVariable long id) {

    return ResponseEntity.ok(marketplaceService.getMarketplaceDtoById(id));
  }

  @Override
  @PostMapping("marketplace-item")
  public ResponseEntity<MarketplaceItemDto> createMarketplace(
      @RequestHeader(EMAIL_HEADER) String userEmail,
      @RequestBody @Valid MarketplaceItemCreateUpdateDto marketplaceItemCreateUpdateDto) {
    return ResponseEntity.ok(
        marketplaceService.createMarketplace(userEmail, marketplaceItemCreateUpdateDto));
  }

  @Override
  @PutMapping("marketplace-item/{id}")
  public ResponseEntity<MarketplaceItemDto> updateMarketplace(
      @RequestHeader(EMAIL_HEADER) String userEmail,
      @PathVariable long id,
      @RequestBody MarketplaceItemCreateUpdateDto marketplaceItemCreateUpdateDto) {

    return ResponseEntity.ok(
        marketplaceService.updateMarketplace(userEmail, id, marketplaceItemCreateUpdateDto));
  }

  @Override
  @PutMapping("marketplace-item/archive/{id}")
  public ResponseEntity<MarketplaceItemDto> updateMarketplace(
      @RequestHeader(EMAIL_HEADER) String userEmail, @PathVariable long id) {

    return ResponseEntity.ok(marketplaceService.archiveMarketplace(userEmail, id));
  }

  @Override
  @DeleteMapping("marketplace-item/{id}")
  public ResponseEntity<MarketplaceItemDto> deleteMarketplace(
      @RequestHeader(EMAIL_HEADER) String userEmail,
      @PathVariable long id,
      @RequestParam(defaultValue = "false", required = false) boolean force) {

    marketplaceService.deleteMarketplace(userEmail, id, force);
    return ResponseEntity.ok().build();
  }

  @Override
  @PostMapping("marketplace-item/delete")
  public ResponseEntity<MarketplaceItemDto> deleteMarketplace(
      @RequestHeader(EMAIL_HEADER) String userEmail,
      @RequestBody Set<Long> ids,
      @RequestParam(defaultValue = "false", required = false) boolean force) {
    marketplaceService.deleteMarketplaces(userEmail, ids, force);
    return ResponseEntity.ok().build();
  }

  @Override
  @PostMapping(path = "marketplace-item/logo/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResponseEntity<MarketplaceItemDto> uploadMarketplaceLogo(
      @PathVariable long id, @RequestPart MultipartFile file) {

    marketplaceService.uploadMarketplaceLogo(id, file);
    return null;
  }

  @Override
  @PostMapping(path = "marketplace-item/image/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResponseEntity<MarketplaceItemDto> uploadProfileImage(
      @PathVariable long id, @RequestPart MultipartFile file) {

    marketplaceService.uploadMarketplaceImage(id, file);
    return null;
  }

  @Override
  @GetMapping("marketplace-item/filtering-values")
  public ResponseEntity<Map<String, Map<String, Object>>> getDistinctFields() {
    return ResponseEntity.ok(marketplaceService.getDistinctFieldsWithoutQuerying());
  }

  @Override
  @PostMapping("marketplace-item/advanced-filters")
  public ResponseEntity<Page<MarketplaceItemDto>> getFilteredJsonql(
      @RequestBody CustomFilter customFilter,
      @RequestParam int pageNumber,
      @RequestParam int pageSize) {

    return ResponseEntity.ok(
        marketplaceService.getFilteredJsonql(customFilter, pageNumber, pageSize));
  }
}
