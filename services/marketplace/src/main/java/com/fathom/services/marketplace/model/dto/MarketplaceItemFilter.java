package com.fathom.services.marketplace.model.dto;

import com.fathom.services.marketplace.model.enums.Industry;
import com.fathom.services.marketplace.model.enums.PricingModel;
import com.fathom.services.marketplace.model.enums.ProductType;
import com.fathom.services.marketplace.model.enums.Publisher;
import lombok.Data;

@Data
public class MarketplaceItemFilter {
  String name;
  String author;
  ProductType type;
  Industry industry;
  Publisher publisher;
  PricingModel pricingModel;
  Long rating;
  Boolean isDraft;
}
