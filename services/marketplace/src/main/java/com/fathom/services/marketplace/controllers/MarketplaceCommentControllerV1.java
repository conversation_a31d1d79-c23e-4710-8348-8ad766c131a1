package com.fathom.services.marketplace.controllers;

import com.fathom.services.marketplace.model.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;

public interface MarketplaceCommentControllerV1 {

  @Operation(
      summary = "Endpoint for getting all marketplace comments by marketplace id. ",
      description = "Endpoint for getting all marketplace comments by marketplace id .")
  ResponseEntity<Page<MarketplaceCommentDto>> getMarketplaceCommentByMarketplaceId(
      long marketplaceId, int pageNumber, int pageSize, boolean pageable);

  @Operation(
      summary = "Endpoint for creating a marketplace comment. ",
      description = "Endpoint for creating a marketplace comment. ")
  ResponseEntity<MarketplaceCommentDto> createMarketplaceComment(
      long marketplaceId,
      String userEmail,
      MarketplaceCommentCreateUpdateDto marketplaceCreateUpdateDto);

  @Operation(
      summary = "Endpoint for deleting a marketplace comment by id. ",
      description = "Endpoint for deleting a marketplace comment by id. ")
  ResponseEntity<MarketplaceCommentDto> deleteMarketplaceComment(String userEmail, long id);

  @Operation(
      summary = "like a comment",
      description = "Allows a user to like a marketplace comment.")
  ResponseEntity<String> likeComment(String userEmail, Long commentId);

  @Operation(
      summary = "Unlike a comment",
      description = "Allows a user to remove their like from a marketplace comment.")
  ResponseEntity<String> unlikeComment(String userEmail, Long commentId);

  @Operation(
      summary = "Get likes for a comment",
      description = "Retrieves a list of users who liked a specific marketplace comment.")
  ResponseEntity<List<MarketplaceCommentLikeDTO>> getLikesByCommentId(Long commentId);

  @Operation(summary = "Report a comment", description = "Allows a user to report a comment.")
  ResponseEntity<MarketplaceCommentReportDto> reportComment(
      String userEmail, Long commentId, MarketplaceCommentReportCreateDto requestDto);

  @Operation(
      summary = "Get reports for a comment",
      description = "Retrieves all reports for a specific comment.")
  ResponseEntity<List<MarketplaceCommentReportDto>> getReportsForComment(Long commentId);
}
