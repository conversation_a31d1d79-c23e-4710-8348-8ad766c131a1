package com.fathom.services.marketplace.services;

import com.fathom.services.marketplace.feign.FileClient;
import com.fathom.services.marketplace.model.dto.MarketplaceItemDto;
import com.fathom.services.marketplace.model.dto.feign.FileInfoDto;
import com.fathom.services.marketplace.model.dto.feign.TokenFileIdDto;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class MarketplaceExtraInformationService {
  private final FileClient fileClient;

  public void loadExtraInformation(List<MarketplaceItemDto> marketplaceList) {
    List<FileInfoDto> fileInfoList =
        fileClient.findFileInformationByUUIDs(
            marketplaceList.stream()
                .map(MarketplaceItemDto::getFileServiceUuid)
                .collect(Collectors.toSet()));

    Set<String> idSet = fileInfoList.stream().map(FileInfoDto::getId).collect(Collectors.toSet());

    log.info(
        "Fetching download token for files with ids: {}",
        idSet.stream().collect(Collectors.joining(",", "[", "]")));

    List<TokenFileIdDto> tokenFileIdList =
        fileClient.getFilesDownloadTokens(
            fileInfoList.stream().map(FileInfoDto::getId).collect(Collectors.toSet()));

    for (MarketplaceItemDto marketplace : marketplaceList) {

      TokenFileIdDto tokenFileIdDto =
          tokenFileIdList.stream()
              .filter(x -> x.getId().equals(marketplace.getLogoFileId()))
              .findFirst()
              .orElse(null);

      if (Objects.nonNull(tokenFileIdDto)) {
        marketplace.setLogo(tokenFileIdDto.getToken());
      }

      List<String> tmp0 =
          fileInfoList.stream()
              .filter(
                  x ->
                      x.getClassification().equals("marketplace-image")
                          && x.getObjectUUID().equals(marketplace.getFileServiceUuid()))
              .map(FileInfoDto::getId)
              .toList();

      List<TokenFileIdDto> tokenFileIdDto1 =
          tokenFileIdList.stream().filter(x -> tmp0.contains(x.getId())).toList();

      marketplace.setImages(tokenFileIdDto1.stream().map(TokenFileIdDto::getToken).toList());
    }
  }
}
