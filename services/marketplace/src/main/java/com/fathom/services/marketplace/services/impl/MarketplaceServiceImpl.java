package com.fathom.services.marketplace.services.impl;

import com.fathom.lib.advancedfilters.filters.CustomFilterSpecification;
import com.fathom.lib.advancedfilters.filters.FilterAndSortInMemoryUtil;
import com.fathom.lib.advancedfilters.model.CustomFilter;
import com.fathom.lib.advancedfilters.util.FilterCleaner;
import com.fathom.lib.common.exception.RestException;
import com.fathom.services.marketplace.feign.FileClient;
import com.fathom.services.marketplace.mapper.MarketplaceItemMapper;
import com.fathom.services.marketplace.model.MarketplaceItem;
import com.fathom.services.marketplace.model.dto.MarketplaceItemCreateUpdateDto;
import com.fathom.services.marketplace.model.dto.MarketplaceItemDto;
import com.fathom.services.marketplace.model.dto.feign.FileUploadResponseDto;
import com.fathom.services.marketplace.model.dto.sorting.MarketplaceItemDtoComparatorFactory;
import com.fathom.services.marketplace.model.enums.Industry;
import com.fathom.services.marketplace.model.enums.PricingModel;
import com.fathom.services.marketplace.model.enums.ProductType;
import com.fathom.services.marketplace.model.enums.Publisher;
import com.fathom.services.marketplace.repositories.MarketplaceItemRepository;
import com.fathom.services.marketplace.services.MarketplaceExtraInformationService;
import com.fathom.services.marketplace.services.MarketplaceService;
import jakarta.transaction.Transactional;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class MarketplaceServiceImpl implements MarketplaceService {
  private final MarketplaceItemRepository marketplaceItemRepository;
  private final MarketplaceItemMapper marketplaceItemMapper;
  private final FileClient fileClient;
  private final MarketplaceExtraInformationService marketplaceExtraInformationService;
  private static final List<String> ALLOWED_IMAGE_EXTENSIONS =
      Arrays.asList("jpg", "jpeg", "png", "gif");

  @Override
  @Transactional
  public MarketplaceItemDto createMarketplace(
      String userEmail, MarketplaceItemCreateUpdateDto marketplaceItemCreateUpdateDto) {

    return marketplaceItemMapper.toDto(
        marketplaceItemRepository.save(
            marketplaceItemMapper.toEntity(userEmail, marketplaceItemCreateUpdateDto)));
  }

  @Override
  @Transactional
  public MarketplaceItemDto updateMarketplace(
      String userEmail, long id, MarketplaceItemCreateUpdateDto marketplaceItemCreateUpdateDto) {

    return marketplaceItemMapper.toDto(
        marketplaceItemRepository.save(
            marketplaceItemMapper.updateDtoToEntity(
                getMarketplace(id), marketplaceItemCreateUpdateDto)));
  }

  @Override
  @Transactional
  public Page<MarketplaceItemDto> getMarketplaceByOrganizationId(
      int pageNumber, int pageSize, boolean pageable) {

    Page<MarketplaceItemDto> dtoPage =
        marketplaceItemRepository.findWithCommentCountAndAverageRating(
            PageRequest.of(pageNumber, pageSize));

    marketplaceExtraInformationService.loadExtraInformation(dtoPage.getContent());
    return dtoPage;
  }

  @Override
  @Transactional
  public MarketplaceItemDto getMarketplaceDtoById(long id) {

    MarketplaceItemDto marketplaceItemDto =
        marketplaceItemRepository
            .findWithCommentCountAndAverageRating(id)
            .orElseThrow(
                () ->
                    new RestException(
                        HttpStatus.NOT_FOUND, "marketplace item with id: %s doesn't exist", id));

    marketplaceExtraInformationService.loadExtraInformation(List.of(marketplaceItemDto));
    return marketplaceItemDto;
  }

  @Override
  public MarketplaceItem getMarketplaceById(long id) {
    return getMarketplace(id);
  }

  @Override
  @Transactional
  public void deleteMarketplace(String userEmail, long id, boolean force) {

    MarketplaceItem marketplaceItem =
        marketplaceItemRepository
            .findById(id)
            .orElseThrow(
                () ->
                    new RestException(
                        HttpStatus.NOT_FOUND, "marketplace item with id: %s doesn't exist", id));

    if (!marketplaceItem.isArchive() && !force) {
      throw new RestException(
          HttpStatus.BAD_REQUEST,
          "marketplace item with id: %s is not archived either archive it or use option force",
          id);
    }

    marketplaceItemRepository.delete(marketplaceItem);
  }

  @Override
  @Transactional
  public void deleteMarketplaces(String userEmail, Set<Long> ids, boolean force) {
    Set<MarketplaceItem> marketplaceItems =
        marketplaceItemRepository.findByOrganizationIdAndIdIn(ids);

    Set<Long> longs =
        marketplaceItems.stream().map(MarketplaceItem::getId).collect(Collectors.toSet());

    if (longs.size() != ids.size()) {
      ids.removeAll(longs);
      throw new RestException(
          HttpStatus.NOT_FOUND,
          "marketplace item with ids: %s doesn't exist",
          ids.stream().map(Object::toString).collect(Collectors.joining(", ", "[", "]")));
    }

    var notArchiveMarketplace = marketplaceItems.stream().filter(x -> !x.isArchive()).toList();

    if (!notArchiveMarketplace.isEmpty() && !force) {
      throw new RestException(
          HttpStatus.BAD_REQUEST,
          "marketplace items with ids: %s is not archived either archive it or use option force",
          notArchiveMarketplace.stream()
              .map(x -> x.getId().toString())
              .collect(Collectors.joining(", ", "[", "]")));
    }

    marketplaceItemRepository.deleteAll(marketplaceItems);
  }

  @Override
  @Transactional
  public MarketplaceItemDto archiveMarketplace(String userEmail, long id) {

    MarketplaceItem marketplaceItem = getMarketplace(id);
    marketplaceItem.setArchive(true);

    return marketplaceItemMapper.toDto(marketplaceItemRepository.save(marketplaceItem));
  }

  @Override
  @Transactional
  public void uploadMarketplaceLogo(long id, MultipartFile file) {

    String fileName = file.getOriginalFilename();

    if (fileName != null) {
      String fileExtension = getFileExtension(fileName);
      if (!ALLOWED_IMAGE_EXTENSIONS.contains(fileExtension.toLowerCase())) {
        throw new RestException(
            HttpStatus.BAD_REQUEST,
            "File type is not of image extension of the following %s",
            ALLOWED_IMAGE_EXTENSIONS.stream().collect(Collectors.joining(", ", "[", "]")));
      }
    }

    MarketplaceItem marketplaceItem = getMarketplace(id);

    if (Objects.nonNull(marketplaceItem.getLogoFileId())) {
      fileClient.deleteById(marketplaceItem.getLogoFileId());
    }

    UUID uuid =
        Objects.isNull(marketplaceItem.getFileServiceUuid())
            ? UUID.randomUUID()
            : marketplaceItem.getFileServiceUuid();

    FileUploadResponseDto fileUploadResponseDto =
        fileClient.addFile(
            uuid,
            file,
            String.format("%s marketplaceItem logo", marketplaceItem.getName()),
            "marketplaceItem-logo",
            String.format("%s marketplaceItem logo", marketplaceItem.getName()));

    marketplaceItem.setFileServiceUuid(uuid);
    marketplaceItem.setLogoFileId(fileUploadResponseDto.getId());
    marketplaceItemRepository.save(marketplaceItem);
  }

  @Override
  @Transactional
  public void uploadMarketplaceImage(long id, MultipartFile file) {

    String fileName = file.getOriginalFilename();

    if (fileName != null) {
      String fileExtension = getFileExtension(fileName);
      if (!ALLOWED_IMAGE_EXTENSIONS.contains(fileExtension.toLowerCase())) {
        throw new RestException(
            HttpStatus.BAD_REQUEST,
            "File type is not of image extension of the following %s",
            ALLOWED_IMAGE_EXTENSIONS.stream().collect(Collectors.joining(", ", "[", "]")));
      }
    }

    MarketplaceItem marketplaceItem = getMarketplace(id);

    UUID uuid =
        Objects.isNull(marketplaceItem.getFileServiceUuid())
            ? UUID.randomUUID()
            : marketplaceItem.getFileServiceUuid();

    fileClient.addFile(
        uuid,
        file,
        String.format("%s marketplaceItem image", marketplaceItem.getName()),
        "marketplace-image",
        String.format("%s marketplaceItem image", marketplaceItem.getName()));

    marketplaceItem.setFileServiceUuid(uuid);
    marketplaceItemRepository.save(marketplaceItem);
  }

  @Override
  public Map<String, Map<String, Object>> getDistinctFields() {
    List<Object[]> results = marketplaceItemRepository.findDistinctFields();
    final String typeFieldName = "type";
    final String industryFieldName = "industry";
    final String publisherFieldName = "publisher";
    final String pricingModelFieldName = "pricingModel";

    // Create the response map with the desired structure
    Map<String, Map<String, Object>> distinctFields = new LinkedHashMap<>();

    // Initialize containers for each field
    distinctFields.put(typeFieldName, createFieldContainer("Product type"));
    distinctFields.put(industryFieldName, createFieldContainer("Industry"));
    distinctFields.put(publisherFieldName, createFieldContainer("Publisher"));
    distinctFields.put(pricingModelFieldName, createFieldContainer("Pricing Model"));

    // Iterate over results and populate values
    for (Object[] row : results) {
      String value = row[0] != null ? row[0].toString() : null;
      String fieldType = row[1].toString();

      if (value == null) continue;

      try {
        switch (fieldType) {
          case typeFieldName:
            ProductType type = ProductType.fromValue(value);
            if (type != null) {
              addValueToFieldContainer(
                  distinctFields, typeFieldName, type.name(), type.getDisplayName());
            }
            break;
          case industryFieldName:
            Industry industry = Industry.fromValue(value);
            if (industry != null) {
              addValueToFieldContainer(
                  distinctFields, industryFieldName, industry.name(), industry.getDisplayName());
            }

            break;
          case publisherFieldName:
            Publisher publisher = Publisher.fromValue(value);
            if (publisher != null) {
              addValueToFieldContainer(
                  distinctFields, publisherFieldName, publisher.name(), publisher.getDisplayName());
            }
            break;
          case pricingModelFieldName:
            PricingModel pricingModel = PricingModel.fromValue(value);
            if (pricingModel != null) {
              addValueToFieldContainer(
                  distinctFields,
                  pricingModelFieldName,
                  pricingModel.name(),
                  pricingModel.getDisplayName());
            }
            break;
          default:
            throw new IllegalArgumentException("Unexpected field type: " + fieldType);
        }
      } catch (IllegalArgumentException e) {
        throw new RestException(
            HttpStatus.INTERNAL_SERVER_ERROR, "Invalid value for " + fieldType + ": " + value);
      }
    }

    return distinctFields;
  }

  @Override
  public Map<String, Map<String, Object>> getDistinctFieldsWithoutQuerying() {

    final String typeFieldName = "type";
    final String industryFieldName = "industry";
    final String publisherFieldName = "publisher";
    final String pricingModelFieldName = "pricingModel";

    // Create the response map with the desired structure
    Map<String, Map<String, Object>> distinctFields = new LinkedHashMap<>();

    // Initialize containers for each field
    distinctFields.put(typeFieldName, createFieldContainer("Product type"));
    distinctFields.put(industryFieldName, createFieldContainer("Industry"));
    distinctFields.put(publisherFieldName, createFieldContainer("Publisher"));
    distinctFields.put(pricingModelFieldName, createFieldContainer("Pricing Model"));

    // Add all enum values to the distinct fields map
    addEnumValuesToFieldContainer(distinctFields, typeFieldName, ProductType.class);
    addEnumValuesToFieldContainer(distinctFields, industryFieldName, Industry.class);
    addEnumValuesToFieldContainer(distinctFields, publisherFieldName, Publisher.class);
    addEnumValuesToFieldContainer(distinctFields, pricingModelFieldName, PricingModel.class);

    return distinctFields;
  }

  private <E extends Enum<E>> void addEnumValuesToFieldContainer(
      Map<String, Map<String, Object>> distinctFields, String fieldName, Class<E> enumClass) {

    for (E enumConstant : enumClass.getEnumConstants()) {
      String enumName = enumConstant.name();
      String displayName = getEnumDisplayName(enumName, enumClass);
      addValueToFieldContainer(distinctFields, fieldName, enumName, displayName);
    }
  }

  private <E extends Enum<E>> String getEnumDisplayName(String enumName, Class<E> enumClass) {
    try {
      Method method = enumClass.getDeclaredMethod("getDisplayName");
      E enumConstant = Enum.valueOf(enumClass, enumName);
      return (String) method.invoke(enumConstant);
    } catch (Exception e) {
      return enumName; // Return the enum name if displayName is not found
    }
  }

  private Map<String, Object> createFieldContainer(String displayName) {
    Map<String, Object> container = new LinkedHashMap<>();
    container.put("displayName", displayName);
    container.put("values", new ArrayList<Map<String, String>>());
    return container;
  }

  @SuppressWarnings("unchecked")
  private void addValueToFieldContainer(
      Map<String, Map<String, Object>> distinctFields,
      String fieldName,
      String name,
      String displayName) {
    Map<String, Object> container = distinctFields.get(fieldName);
    List<Map<String, String>> values = (List<Map<String, String>>) container.get("values");
    values.add(createFieldEntry(name, displayName));
  }

  @Override
  public Page<MarketplaceItemDto> getFilteredJsonql(
      CustomFilter customFilter, int pageNumber, int pageSize) {

    // Apply filter on database-level properties
    CustomFilter dbFilter = new CustomFilter();
    Map<String, Object> objectMap = FilterCleaner.deepCopy(customFilter.getFilter());
    FilterCleaner.removeParentIfPathEquals(objectMap, Set.of("commentsCount", "averageRating"));
    dbFilter.setFilter(objectMap);

    Specification<MarketplaceItem> spec = new CustomFilterSpecification<>(dbFilter);
    Page<MarketplaceItem> page =
        marketplaceItemRepository.findAll(spec, PageRequest.of(pageNumber, pageSize));

    Page<MarketplaceItemDto> marketplaceItemDto = getPagedDto(page);

    // Apply in-memory filtering
    FilterAndSortInMemoryUtil<MarketplaceItemDto> filterSortUtil =
        new FilterAndSortInMemoryUtil<>();

    Comparator<MarketplaceItemDto> comparator = MarketplaceItemDtoComparatorFactory.getComparator("name");
    if (customFilter.getSort() != null && customFilter.getSort().getBy() != null) {
      comparator = MarketplaceItemDtoComparatorFactory.getComparator(customFilter.getSort().getBy());
    }

    Page<MarketplaceItemDto> filteredList =
        filterSortUtil.applyFiltersAndSort(
            marketplaceItemDto.getContent(), customFilter, comparator, 0, marketplaceItemDto.getSize());

    long totalElementsAfterFiltering = getTotalElementsAfterFiltering(filteredList, page);

    return new PageImpl<>(
        filteredList.getContent(),
        PageRequest.of(pageNumber, pageSize),
        totalElementsAfterFiltering);
  }

  private long getTotalElementsAfterFiltering(
      Page<MarketplaceItemDto> filteredList, Page<MarketplaceItem> page) {
    long totalElementsAfterFiltering;

    if (filteredList.getContent().isEmpty()) {
      totalElementsAfterFiltering = 0;
    } else if (filteredList.getContent().size() < page.getContent().size()) {
      totalElementsAfterFiltering = filteredList.getTotalElements();
    } else {
      totalElementsAfterFiltering = page.getTotalElements();
    }
    return totalElementsAfterFiltering;
  }

  private Map<String, String> createFieldEntry(String name, String displayName) {
    Map<String, String> fieldEntry = new LinkedHashMap<>();
    fieldEntry.put("name", name);
    fieldEntry.put("displayName", displayName);
    return fieldEntry;
  }

  private MarketplaceItem getMarketplace(long id) {
    return marketplaceItemRepository
        .findById(id)
        .orElseThrow(
            () ->
                new RestException(
                    HttpStatus.NOT_FOUND, "MarketplaceItem with id: %s is not found", id));
  }

  private String getFileExtension(String fileName) {
    int lastDotIndex = fileName.lastIndexOf(".");
    if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
      return fileName.substring(lastDotIndex + 1);
    }
    return "";
  }

  private Page<MarketplaceItemDto> getPagedDto(Page<MarketplaceItem> entitiesPaged) {

    PageImpl<MarketplaceItemDto> page =
        new PageImpl<>(
            entitiesPaged.getContent().stream().map(marketplaceItemMapper::toDto).toList(),
            entitiesPaged.getPageable(),
            entitiesPaged.getTotalElements());

    // Load extra information for the DTOs
    marketplaceExtraInformationService.loadExtraInformation(page.getContent());

    // Get the comment summary (count and average rating)
    Map<Long, Pair<Integer, Double>> marketplaceCommentSummary =
        marketplaceItemRepository.getAverageRatingsAndCounts(
            page.getContent().stream().map(MarketplaceItemDto::getId).toList());

    // Map the comment summary to the DTOs
    page.getContent()
        .forEach(
            dto -> {
              Pair<Integer, Double> commentSummary = marketplaceCommentSummary.get(dto.getId());
              if (commentSummary != null) {
                dto.setCommentsCount(commentSummary.getLeft());
                dto.setAverageRating(commentSummary.getRight());
              } else {
                dto.setCommentsCount(0);
                dto.setAverageRating(0.0);
              }
            });

    return page;
  }
}
