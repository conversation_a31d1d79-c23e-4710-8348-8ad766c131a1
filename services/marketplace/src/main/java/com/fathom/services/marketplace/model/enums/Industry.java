package com.fathom.services.marketplace.model.enums;

import lombok.Getter;

@Getter
public enum Industry {
  ENERGY(0, "Energy"),
  MANUFACTURING(1, "Manufacturing"),
  SUPPLY_CHAIN(2, "Supply Chain"),
  SMART_CITIES(3, "Smart Cities"),
  FINANCE(4, "Finance"),
  HEALTHCARE(5, "Healthcare"),
  RETAIL(6, "Retail"),
  EDUCATION(7, "Education"),
  GOVERNMENT(8, "Government"),
  TELECOM(9, "Telecom"),
  LOGISTICS(10, "Logistics"),
  REAL_ESTATE(11, "Real Estate"),
  HR_RECRUITMENT(12, "HR & Recruitment"),
  LEGAL(13, "Legal"),
  MEDIA(14, "Media"),
  HOSPITALITY(15, "Hospitality"),
  AGRICULTURE(16, "Agriculture");

  private final int value;
  private final String displayName;

  Industry(int value, String displayName) {
    this.value = value;
    this.displayName = displayName;
  }

  // Method to convert string integer value to ProductType enum
  public static Industry fromValue(String value) {
    try {
      int intValue = Integer.parseInt(value);
      for (Industry type : values()) {
        if (type.getValue() == intValue) {
          return type;
        }
      }
    } catch (NumberFormatException e) {
      // Handle invalid number format
      throw new RuntimeException("Invalid value for ProductType: " + value);
    }
    return null; // Return null if no matching enum is found
  }

  public static String getDisplayNameFromValue(String value) {
    for (Industry industry : values()) {
      if (industry.name().equalsIgnoreCase(value)) {
        return industry.getDisplayName();
      }
    }
    throw new IllegalArgumentException("No matching Industry for value: " + value);
  }
}
