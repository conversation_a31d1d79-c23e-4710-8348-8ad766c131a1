package com.fathom.services.marketplace.model.enums;

import lombok.Getter;

@Getter
public enum ProductType {
  APPLICATION(0, "Application"),
  DATA_PRODUCT(1, "Data Product"),
  DOCUMENTS(2, "Documents"),
  FORMS(3, "Forms"),
  FUNCTION(4, "Function"),
  ML_MODEL(5, "ML Model"),
  PROCESSES(6, "Processes");

  private final int value;
  private final String displayName;

  ProductType(int value, String displayName) {
    this.value = value;
    this.displayName = displayName;
  }

  // Method to convert string integer value to ProductType enum
  public static ProductType fromValue(String value) {
    try {
      int intValue = Integer.parseInt(value);
      for (ProductType type : values()) {
        if (type.getValue() == intValue) {
          return type;
        }
      }
    } catch (NumberFormatException e) {
      // Handle invalid number format
      throw new RuntimeException("Invalid value for ProductType: " + value);
    }
    return null; // Return null if no matching enum is found
  }
}
