package com.fathom.services.marketplace.services;

import com.fathom.lib.advancedfilters.model.CustomFilter;
import com.fathom.services.marketplace.model.MarketplaceItem;
import com.fathom.services.marketplace.model.dto.MarketplaceItemCreateUpdateDto;
import com.fathom.services.marketplace.model.dto.MarketplaceItemDto;
import java.util.Map;
import java.util.Set;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

public interface MarketplaceService {

  MarketplaceItemDto createMarketplace(
      String userEmail, MarketplaceItemCreateUpdateDto marketplaceItemCreateUpdateDto);

  MarketplaceItemDto updateMarketplace(
      String userEmail, long id, MarketplaceItemCreateUpdateDto marketplaceItemCreateUpdateDto);

  Page<MarketplaceItemDto> getMarketplaceByOrganizationId(
      int pageNumber, int pageSize, boolean pageable);

  MarketplaceItemDto getMarketplaceDtoById(long id);

  MarketplaceItem getMarketplaceById(long id);

  void deleteMarketplace(String userEmail, long id, boolean force);

  void deleteMarketplaces(String userEmail, Set<Long> ids, boolean force);

  MarketplaceItemDto archiveMarketplace(String userEmail, long id);

  void uploadMarketplaceLogo(long id, MultipartFile file);

  void uploadMarketplaceImage(long id, MultipartFile file);

  Map<String, Map<String, Object>> getDistinctFields();

  Map<String, Map<String, Object>> getDistinctFieldsWithoutQuerying();

  Page<MarketplaceItemDto> getFilteredJsonql(
      CustomFilter customFilter, int pageNumber, int pageSize);
}
