package com.fathom.services.marketplace.repositories;

import com.fathom.services.marketplace.model.MarketplaceComment;
import com.fathom.services.marketplace.model.dto.MarketplaceCommentDto;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface MarketplaceCommentRepository extends JpaRepository<MarketplaceComment, Long> {

  // Query using JPQL Constructor Expression for DTO Projection
  String MARKETPLACE_COMMENT_DTO_QUERY =
      """
          SELECT new com.fathom.services.marketplace.model.dto.MarketplaceCommentDto(
              c.id,
              c.title,
              c.comment,
              c.commentAuthor,
              c.rating,
              c.createdDate,
              c.updatedDate,
              COUNT(l.id),
              CASE WHEN EXISTS (
                  SELECT 1 FROM MarketplaceCommentReport r WHERE r.comment.id = c.id
              ) THEN true ELSE false END
          )
          FROM MarketplaceComment c
          LEFT JOIN MarketplaceCommentLike l ON c.id = l.comment.id
          WHERE c.marketplaceItem.id = :marketplaceItemId
          GROUP BY c.id
          """;

  @Query(MARKETPLACE_COMMENT_DTO_QUERY)
  Page<MarketplaceCommentDto> findCommentsWithLikeCount(
      @Param("marketplaceItemId") long marketplaceItemId, Pageable pageable);

  @Query(MARKETPLACE_COMMENT_DTO_QUERY)
  List<MarketplaceCommentDto> findCommentsWithLikeCount(
      @Param("marketplaceItemId") long marketplaceItemId);
}
