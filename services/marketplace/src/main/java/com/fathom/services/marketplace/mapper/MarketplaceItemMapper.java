package com.fathom.services.marketplace.mapper;

import com.fathom.services.marketplace.model.MarketplaceItem;
import com.fathom.services.marketplace.model.dto.MarketplaceItemCreateUpdateDto;
import com.fathom.services.marketplace.model.dto.MarketplaceItemDto;
import org.mapstruct.*;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class MarketplaceItemMapper {
  @Mapping(target = "authorEmail", source = "userEmail")
  public abstract MarketplaceItem toEntity(String userEmail, MarketplaceItemCreateUpdateDto source);

  public abstract MarketplaceItemDto toDto(MarketplaceItem source);

  public abstract MarketplaceItem updateDtoToEntity(
      @MappingTarget MarketplaceItem marketplaceItem, MarketplaceItemCreateUpdateDto source);
}
