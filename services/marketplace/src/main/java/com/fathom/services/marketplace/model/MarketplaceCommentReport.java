package com.fathom.services.marketplace.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(
    name = "marketplace_comment_report",
    uniqueConstraints = {@UniqueConstraint(columnNames = {"user_email", "comment_id"})})
public class MarketplaceCommentReport {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "comment_id", nullable = false)
  private MarketplaceComment comment;

  @Column(name = "user_email", nullable = false)
  private String userEmail;

  @Column(name = "report_reason")
  private String reportReason;

  private LocalDateTime reportedAt = LocalDateTime.now(ZoneId.of("UTC"));
}
