package com.fathom.services.marketplace.controllers;

import com.fathom.lib.advancedfilters.model.CustomFilter;
import com.fathom.services.marketplace.model.dto.MarketplaceItemCreateUpdateDto;
import com.fathom.services.marketplace.model.dto.MarketplaceItemDto;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Map;
import java.util.Set;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

public interface MarketplaceControllerV1 {

  @Operation(
      summary = "Endpoint for getting all marketplaces.",
      description = "Endpoint for getting all marketplaces.")
  ResponseEntity<Page<MarketplaceItemDto>> getMarketplaces(
      int pageNumber, int pageSize, boolean pageable);

  @Operation(
      summary = "Endpoint for getting a marketplace by id. ",
      description = "Endpoint for getting a marketplace by id. ")
  ResponseEntity<MarketplaceItemDto> getMarketplaceById(long id);

  @Operation(
      summary = "Endpoint for creating a marketplace.",
      description =
          """
                  Endpoint for creating a marketplace. Possible enum values:
                  - **Industry**: ENERGY, MANUFACTURING, SUPPLY_CHAIN, SMART_CITIES, FINANCE, HEALTHCARE, RETAIL,
                    EDUCATION, GOVERNMENT, TELECOM, LOGISTICS, REAL_ESTATE, HR_RECRUITMENT, LEGAL, MEDIA,
                    HOSPITALITY, AGRICULTURE
                  - **Publisher**: FATHOM, PARTNER
                  - **Pricing Model**: FREE, FREE_TRIAL, USAGE_BASE, SUBSCRIPTION
                  """)
  ResponseEntity<MarketplaceItemDto> createMarketplace(
      String userEmail, MarketplaceItemCreateUpdateDto marketplaceItemCreateUpdateDto);

  @Operation(
      summary = "Endpoint for updating a marketplace.",
      description =
          """
                  Endpoint for updating a marketplace. Possible enum values:
                  - **Industry**: ENERGY, MANUFACTURING, SUPPLY_CHAIN, SMART_CITIES, FINANCE, HEALTHCARE, RETAIL,
                    EDUCATION, GOVERNMENT, TELECOM, LOGISTICS, REAL_ESTATE, HR_RECRUITMENT, LEGAL, MEDIA,
                    HOSPITALITY, AGRICULTURE
                  - **Publisher**: FATHOM, PARTNER
                  - **Pricing Model**: FREE, FREE_TRIAL, USAGE_BASE, SUBSCRIPTION
                  """)
  ResponseEntity<MarketplaceItemDto> updateMarketplace(
      String userEmail, long id, MarketplaceItemCreateUpdateDto marketplaceItemCreateUpdateDto);

  @Operation(
      summary = "Endpoint for a a archiving a marketplace. ",
      description = "Endpoint for a a archiving a marketplace ")
  ResponseEntity<MarketplaceItemDto> updateMarketplace(String userEmail, long id);

  @Operation(
      summary = "Endpoint for deleting a marketplace by id. ",
      description = "Endpoint for deleting a marketplace by id. ")
  ResponseEntity<MarketplaceItemDto> deleteMarketplace(String userEmail, long id, boolean force);

  @Operation(
      summary = "Endpoint for deleting a marketplace by ids. ",
      description = "Endpoint for deleting a marketplace by ids. ")
  ResponseEntity<MarketplaceItemDto> deleteMarketplace(
      String userEmail, Set<Long> ids, boolean force);

  @Operation(
      summary = "Endpoint for a uploading the marketplace logo image through the file service. ",
      description = "Endpoint for the marketplace logo image though the file service. ")
  ResponseEntity<MarketplaceItemDto> uploadMarketplaceLogo(long id, MultipartFile file);

  @Operation(
      summary = "Endpoint for a uploading the marketplace image through the file service. ",
      description = "Endpoint for the marketplace logo though the file service. ")
  ResponseEntity<MarketplaceItemDto> uploadProfileImage(long id, MultipartFile file);

  @Operation(
      summary = "Endpoint to get distinct available filter options. ",
      description = "Endpoint to get distinct available filter options. ")
  ResponseEntity<Map<String, Map<String, Object>>> getDistinctFields();

  @Operation(
      summary = "Endpoint for getting users by advanced filtering options.",
      description =
          """
                This endpoint retrieves a list of users based on advanced filtering options specified in the `Filter` object. The filters can include conditions like equality, range (greater than, less than), and logical combinations (AND, OR).

                You can specify the following filtering criteria:
                   - **@and**: Combines multiple filters that all must be satisfied.
                   - **@or**: Combines multiple filters where at least one must be satisfied.
                   - **@gt**: Checks if a value is greater than a specified value.
                   - **@lt**: Checks if a value is less than a specified value.
                   - **@eq**: Checks if a value is equal to a specified value.
                   - **@noteq**: Checks if a value is not equal to a specified value.
                   - **@gte**: Checks if a value is greater than or equal to a specified value.
                   - **@lte**: Checks if a value is less than or equal to a specified value.
                   - **@in**: Checks if a value is present in a list of specified values.
                   - **@not**: Checks if a value is not present in a specified list.
                   - **@contains**: Checks if a string contains a specified substring.
                   - **@between**: Checks if a value lies within a specified range.
                   - **@regex**: Checks if a string matches a specified regular expression.
                   - **@eqignorecase**: Checks if a value is equal to a specified value ignoring case.
                   - **@containsignorecase**: Checks if a string contains a specified substring ignoring case.
                ### Example Request:
                To filter users with a price greater than 10 and less than 99, you can use the following JSON structure:
                ```json
                {
                  "filter": {
                    "@and": [
                      {
                        "@gt": {
                          "path": "price",
                          "value": 10
                        }
                      },
                      {
                        "@lt": {
                          "path": "price",
                          "value": 99
                        }
                      }
                    ]
                  },
                  "sort": {
                    "by": "columnName",
                    "ord": "ASC|DESC"
                   }
                }
                ```

                ### Pagination:
                You can also specify pagination by using `Pageable` parameters. Make sure to include `pageNumber` and `pageSize` in your request for efficient data retrieval.

                ### Response:
                The response will contain a list of `UserDto` objects that match the filtering criteria. If no users are found, an empty list will be returned.

                ### Usage:
                This endpoint is useful for retrieving specific user information based on various attributes, which can be beneficial for building user management features in your application.
                """)
  ResponseEntity<Page<MarketplaceItemDto>> getFilteredJsonql(
      CustomFilter customFilter, int pageNumber, int pageSize);
}
