package com.fathom.services.marketplace.mapper;

import com.fathom.services.marketplace.model.MarketplaceCommentLike;
import com.fathom.services.marketplace.model.dto.MarketplaceCommentLikeDTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface MarketplaceCommentLikeMapper {
  MarketplaceCommentLikeMapper INSTANCE = Mappers.getMapper(MarketplaceCommentLikeMapper.class);

  MarketplaceCommentLikeDTO toDto(MarketplaceCommentLike like);

  List<MarketplaceCommentLikeDTO> toDtoList(List<MarketplaceCommentLike> likes);
}
