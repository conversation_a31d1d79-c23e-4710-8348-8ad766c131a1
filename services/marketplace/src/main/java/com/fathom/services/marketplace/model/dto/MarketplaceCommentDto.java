package com.fathom.services.marketplace.model.dto;

import java.time.LocalDateTime;
import lombok.Data;

@Data
public class MarketplaceCommentDto {

  long id;
  String title;
  String comment;
  String commentAuthor;
  String commentAuthorName;
  float rating;
  LocalDateTime createdDate;
  LocalDateTime updatedDate;
  int likeCount;
  boolean isReported;

  public MarketplaceCommentDto(
      long id,
      String title,
      String comment,
      String commentAuthor,
      float rating,
      LocalDateTime createdDate,
      LocalDateTime updatedDate,
      long likeCount,
      boolean isReported) {
    this.id = id;
    this.title = title;
    this.comment = comment;
    this.commentAuthor = commentAuthor;
    this.rating = rating;
    this.createdDate = createdDate;
    this.updatedDate = updatedDate;
    this.likeCount = (int) likeCount;
    this.isReported = isReported;
    this.commentAuthorName = extractNameFromEmail(commentAuthor); // Extract the name
  }

  // Function to extract a clean name from an email
  private static String extractNameFromEmail(String email) {
    if (email == null || !email.contains("@")) {
      return "Unknown"; // Default if email is invalid
    }

    // Extract part before '@'
    String namePart = email.split("@")[0];

    // Remove numbers and special characters (except dots and underscores)
    namePart = namePart.replaceAll("[0-9]", ""); // Remove numbers
    namePart = namePart.replaceAll("[._]", " "); // Replace '.' and '_' with spaces

    // Capitalize each word
    String[] words = namePart.split("\\s+");
    StringBuilder cleanedName = new StringBuilder();
    for (String word : words) {
      if (!word.isEmpty()) {
        cleanedName
            .append(Character.toUpperCase(word.charAt(0)))
            .append(word.substring(1))
            .append(" ");
      }
    }

    return cleanedName.toString().trim(); // Remove trailing spaces
  }
}
