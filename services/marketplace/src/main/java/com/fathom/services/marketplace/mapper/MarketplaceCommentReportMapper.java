package com.fathom.services.marketplace.mapper;

import com.fathom.services.marketplace.model.MarketplaceCommentReport;
import com.fathom.services.marketplace.model.dto.MarketplaceCommentReportDto;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface MarketplaceCommentReportMapper {

  @Mapping(source = "comment.id", target = "commentId")
  MarketplaceCommentReportDto toDto(MarketplaceCommentReport report);

  List<MarketplaceCommentReportDto> toDtoList(List<MarketplaceCommentReport> reports);
}
