package com.fathom.services.marketplace.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fathom.services.marketplace.model.enums.Industry;
import com.fathom.services.marketplace.model.enums.PricingModel;
import com.fathom.services.marketplace.model.enums.ProductType;
import com.fathom.services.marketplace.model.enums.Publisher;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class MarketplaceItemDto {

  long id;
  String name;
  String subtitle;
  String details;
  String author;
  ProductType type;
  Industry industry;
  Publisher publisher;
  PricingModel pricingModel;
  String organizationName;
  String projectId;
  String authorEmail;
  String logo;
  List<String> images = new ArrayList<>();

  @JsonProperty("isArchive")
  boolean isArchive;

  @JsonProperty("isDraft")
  boolean isDraft;

  String appUrl;

  long commentsCount;
  double averageRating;
  @JsonIgnore String logoFileId;
  UUID fileServiceUuid;
  String features;
  LocalDateTime releaseDate;
  String technicalInformation;
  List<String> versions = new ArrayList<>();
  String supportAndDocumentation;
  LocalDateTime createdDate;
  LocalDateTime updatedDate;

  // Constructor including all fields except images and logo
  public MarketplaceItemDto(
      long id,
      String name,
      String subtitle,
      String author,
      ProductType type,
      Industry industry,
      Publisher publisher,
      PricingModel pricingModel,
      String organizationName,
      String projectId,
      String authorEmail,
      boolean isArchive,
      boolean isDraft,
      String appUrl,
      LocalDateTime createdDate,
      LocalDateTime updatedDate,
      String logoFileId,
      UUID fileServiceUuid,
      long commentsCount,
      double averageRating,
      String details,
      String features,
      LocalDateTime releaseDate,
      String technicalInformation,
      String supportAndDocumentation) {

    this.id = id;
    this.name = name;
    this.subtitle = subtitle;
    this.type = type;
    this.industry = industry;
    this.publisher = publisher;
    this.pricingModel = pricingModel;
    this.organizationName = organizationName;
    this.projectId = projectId;
    this.author = author;
    this.authorEmail = authorEmail;
    this.isArchive = isArchive;
    this.isDraft = isDraft;
    this.appUrl = appUrl;
    this.commentsCount = commentsCount;
    this.averageRating = averageRating;
    this.logoFileId = logoFileId;
    this.fileServiceUuid = fileServiceUuid;
    this.createdDate = createdDate;
    this.updatedDate = updatedDate;
    this.details = details;
    this.features = features;
    this.releaseDate = releaseDate;
    this.technicalInformation = technicalInformation;
    this.supportAndDocumentation = supportAndDocumentation;
  }
}
