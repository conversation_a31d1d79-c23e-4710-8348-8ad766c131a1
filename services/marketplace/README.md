# Marketplace Service

This application is used to create marketplace items and comments.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Tech Stack](#tech-stack)
- [Setup](#setup)
- [Configuration](#configuration)
- [Running the Application](#running-the-application)
- [Testing](#testing)
- [Endpoints](#endpoints)
- [Contributing](#contributing)
- [License](#license)

## Prerequisites

List of tools and versions required to run the project:

- Java (version 17 or higher)
- <PERSON><PERSON> (version 3.x or higher)
- Docker
- PostgreSQL/MySQL

## Tech Stack

- Spring Boot for application development
- Spring Data JPA for data access
- Hibernate as the ORM
- MySQL/PostgreSQL for the database
- Docker for containerization (if applicable)

## Setup

Steps to set up the project locally:

1. Clone the repository:

    ```bash
    git clone https://github.com/fathom-io/com.fathom.services.marketplace.git
    cd project-name
    ```

2. Install dependencies:

    ```bash
    mvn clean install
    ```

3. Build Docker image (if applicable):

    ```bash
    docker build -t project-name .
    ```

## Configuration

The application can be configured with the following properties:

- `spring.datasource.url`: The database URL.
- `spring.datasource.username`: The database username.
- `spring.datasource.password`: The database password.
- `server.port`: The port on which the application runs.

Example for `application.yml`:

```yaml
spring:
  datasource:
    url: ***********************************
    username: root
    password: password
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
server:
  port: 8080 
  ```

## Running the Application

To run the application locally:

```bash
mvn spring-boot:run
```

