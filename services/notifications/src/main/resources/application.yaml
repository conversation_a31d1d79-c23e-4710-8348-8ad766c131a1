server:
  port: ${NOTIFICATIONS_SERVICE_PORT:8057}

logging:
  level:
    root: INFO
    org:
      springframework:
        boot:
          autoconfigure:
            web:
              reactive: WARN
        cloud:
          gateway: WARN

default-settings:
  project-type-color:
  - project-type: metaverse
    color: "#F59700"
  - project-type: data
    color: "#7AC05F"
  - project-type: intelligence
    color: "#1F78FF"
  - project-type: application
    color: "#B252DD"
  - project-type: digitaltwin
    color: "#757575"

spring:
  application:
    name: ${NOTIFICATIONS_SERVICE_NAME:notifications}
  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017}
      database: ${MONGODB_DATABASE:dev}
      auto-index-creation: true
  cloud:
    kubernetes:
      discovery:
        all-namespaces: false
services:
  usermanagement:
    name: ${USER_MANAGEMENT_SERVICE_NAME:usermanagement:8059}

springdoc:
  api-docs:
    version: "openapi_3_0"

diagnostics:
  service-name: "notifications"
  version: ${VERSION:0.0.1-dev}
  build-timestamp: ${BUILD_TIMESTAMP:1970-01-01T00:00:00Z}
  git-hash: ${GIT_HASH:unknown}
  git-branch: ${GIT_BRANCH:main}
  git-commit-timestamp: ${GIT_COMMIT_TIMESTAMP:1970-01-01T00:00:00Z}