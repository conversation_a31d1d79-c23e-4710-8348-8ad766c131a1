package com.fathom.services.notifications.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;

public enum ProjectType {
  METAVERSE("metaverse"),
  DATA("data"),
  INTELLIGENCE("intelligence"),
  APPLICATION("application"),
  DIGITALTWIN("digitaltwin");

  private final String value;

  ProjectType(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return this.value;
  }

  public static ProjectType fromValue(String value) {
    for (ProjectType type : ProjectType.values()) {
      if (type.value.equals(value)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid ProjectType value: " + value);
  }
}
