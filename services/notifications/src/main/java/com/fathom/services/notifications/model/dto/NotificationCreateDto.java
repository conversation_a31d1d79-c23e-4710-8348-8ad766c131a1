package com.fathom.services.notifications.model.dto;

import com.fathom.services.notifications.model.enums.ProjectType;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import java.util.Map;
import lombok.Data;

@Data
public class NotificationCreateDto {

  @Pattern(regexp = "^[A-Za-z0-9+_.-]+@(.+)$", message = "Invalid email format: ${validatedValue}")
  String userEmail;

  ProjectType projectType;

  @NotEmpty(message = "Notification title cannot be null")
  String title;

  String message;
  Map<String, String> metadata;

  boolean actionNeeded = false;
}
