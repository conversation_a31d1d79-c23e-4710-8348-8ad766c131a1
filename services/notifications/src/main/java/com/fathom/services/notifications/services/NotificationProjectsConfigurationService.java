package com.fathom.services.notifications.services;

import com.fathom.services.notifications.model.dto.NotificationProjectsConfigurationCreateUpdateDto;
import com.fathom.services.notifications.model.dto.NotificationProjectsConfigurationDto;
import java.util.UUID;

public interface NotificationProjectsConfigurationService {
  void createNotificationProjectsConfigurationUsingDefaultValues(
      UUID organizationId, String projectId);

  NotificationProjectsConfigurationDto getNotificationProjectsConfiguration(UUID organizationId);

  NotificationProjectsConfigurationDto create(
      UUID organizationId,
      NotificationProjectsConfigurationCreateUpdateDto notificationProjectsConfigurationCreateDto);

  NotificationProjectsConfigurationDto update(
      UUID organizationId,
      NotificationProjectsConfigurationCreateUpdateDto notificationProjectsConfigurationCreateDto);

  void delete(UUID organizationId);
}
