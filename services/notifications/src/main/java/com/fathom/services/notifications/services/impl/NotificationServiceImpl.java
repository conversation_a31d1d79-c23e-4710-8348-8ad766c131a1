package com.fathom.services.notifications.services.impl;

import com.fathom.services.notifications.feign.UserManagementClient;
import com.fathom.services.notifications.mapper.NotificationMapper;
import com.fathom.services.notifications.model.Notification;
import com.fathom.services.notifications.model.dto.*;
import com.fathom.services.notifications.model.enums.ProjectType;
import com.fathom.services.notifications.model.feign.GroupDto;
import com.fathom.services.notifications.model.filter.NotificationFilter;
import com.fathom.services.notifications.model.filter.NotificationFilterCount;
import com.fathom.services.notifications.repositories.NotificationRepository;
import com.fathom.services.notifications.services.NotificationProjectsConfigurationService;
import com.fathom.services.notifications.services.NotificationService;
import jakarta.validation.Valid;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationServiceImpl implements NotificationService {

  private final NotificationMapper notificationMapper;
  private final NotificationRepository notificationRepository;
  private final NotificationProjectsConfigurationService notificationProjectTypeService;
  private final CustomWebSocketHandler customWebSocketHandler;
  private final UserManagementClient userManagementClient;
  private static final String EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@(.+)$";

  @Override
  public Page<NotificationDto> getNotificationByOrganizationIdAndProjectId(
      UUID organizationId, String projectId, int pageNumber, int pageSize, boolean pageable) {

    var notificationPage =
        Objects.isNull(projectId)
            ? notificationRepository.findByOrganizationId(
                organizationId, PageRequest.of(pageNumber - 1, pageSize))
            : notificationRepository.findByOrganizationIdAndProjectId(
                organizationId, projectId, PageRequest.of(pageNumber - 1, pageSize));

    var notificationDtoList =
        notificationPage.stream()
            .map(notificationMapper::toDto)
            .sorted(Comparator.comparing(NotificationDto::getCreatedDate).reversed())
            .toList();

    return new PageImpl<>(
        notificationDtoList,
        PageRequest.of(pageNumber - 1, pageSize),
        notificationPage.getTotalElements());
  }

  @Override
  public List<NotificationDto> createAndSendNotification(
      UUID organizationId,
      String projectId,
      @Valid List<NotificationCreateDto> notificationCreateDtoList) {

    List<Notification> notifications =
        notificationCreateDtoList.stream()
            .map(x -> notificationMapper.toEntity(organizationId, projectId, x))
            .toList();

    notificationProjectTypeService.createNotificationProjectsConfigurationUsingDefaultValues(
        organizationId, projectId);

    notifications = notificationRepository.saveAll(notifications);

    customWebSocketHandler.notifySubscribers(organizationId, notifications);
    return notifications.stream().map(notificationMapper::toDto).toList();
  }

  @Override
  public List<NotificationDto> markNotificationsAsRead(
      UUID organizationId, String projectId, Set<String> ids) {

    List<Notification> notifications =
        notificationRepository.findByOrganizationIdAndProjectIdAndIdInAnReadIgnoreNull(
            organizationId, projectId, false, ids);

    if (notifications.isEmpty()) {
      return List.of();
    }

    checkForActionNNeeded(notifications);

    logReadChangeStatus(organizationId, projectId, notifications);

    notifications.forEach(notification -> notification.setRead(true));
    notifications = notificationRepository.saveAll(notifications);

    customWebSocketHandler.notifySubscribers(organizationId, notifications);
    return notifications.stream().map(notificationMapper::toDto).toList();
  }

  @Override
  public List<NotificationDto> markNotificationsAsReadByUserEmailAndProjectType(
      UUID organizationId, String projectId, String userEmail, String projectType) {

    validateEmailAndProjectType(userEmail, projectType);

    String projectTypes =
        Objects.nonNull(projectType) ? ProjectType.fromValue(projectType).toString() : null;

    List<Notification> notifications =
        notificationRepository
            .findByOrganizationIdAndProjectIdAndUserEmailAndProjectTypeAndReadIgnoreNull(
                organizationId, projectId, userEmail, false, projectTypes);

    checkForActionNNeeded(notifications);

    if (notifications.isEmpty()) {

      if (Objects.isNull(projectType))
        log.info("no unread notifications are found for email: {}", userEmail);
      else {
        log.info(
            "no unread notifications are found for email: {} and project type: {}",
            userEmail,
            projectType);
      }
      return List.of();
    }

    logReadChangeStatus(organizationId, projectId, notifications);

    notifications.forEach(notification -> notification.setRead(true));
    notifications = notificationRepository.saveAll(notifications);
    customWebSocketHandler.notifySubscribers(organizationId, notifications);

    return notifications.stream().map(notificationMapper::toDto).toList();
  }

  @Override
  public List<NotificationDto> markNotificationsAsActionNeededCompleted(
      UUID organizationId, String projectId, Set<String> ids) {

    List<Notification> notifications =
        notificationRepository.findByOrganizationIdAndProjectIdAndIdInAnReadIgnoreNull(
            organizationId, projectId, false, ids);

    if (notifications.isEmpty()) {
      return List.of();
    }

    notifications.forEach(notification -> notification.setActionNeeded(false));
    notifications = notificationRepository.saveAll(notifications);

    customWebSocketHandler.notifySubscribers(organizationId, notifications);
    return notifications.stream().map(notificationMapper::toDto).toList();
  }

  @Override
  public Page<NotificationDto> getNotificationByFilterObject(
      UUID organizationId, NotificationFilter filterObject) {

    var notificationPage =
        notificationRepository.getNotificationByFilterObject(organizationId, filterObject);

    var notificationDtoList =
        notificationPage.stream()
            .map(notificationMapper::toDto)
            .sorted(Comparator.comparing(NotificationDto::getCreatedDate).reversed())
            .toList();

    return new PageImpl<>(
        notificationDtoList, notificationPage.getPageable(), notificationPage.getTotalElements());
  }

  @Override
  public Integer getNotificationCountByFilterObject(
      UUID organizationId, NotificationFilterCount notificationFilterCount) {

    return notificationRepository.getNotificationCountByFilterObject(
        organizationId, notificationFilterCount);
  }

  @Override
  public ProjectTypes getSupportedProjectTypes() {

    ProjectTypes projectTypes = new ProjectTypes();
    projectTypes.setSupportedProjectTypes(
        Arrays.stream(ProjectType.values()).map(ProjectType::getValue).toList());

    return projectTypes;
  }

  @Override
  public void deleteManyNotifications(UUID organizationId, String projectId, Set<String> idsSet) {

    List<Notification> notifications =
        notificationRepository.findByOrganizationIdAndProjectIdAndIds(
            organizationId, projectId, idsSet);

    if (notifications.isEmpty()) {
      throw new NoSuchElementException("No matching record is fond from the given Ids");
    }

    notificationRepository.deleteByOrganizationIdAndProjectIdAndIdIn(
        organizationId, projectId, idsSet);

    customWebSocketHandler.notifySubscribers(organizationId, notifications);
  }

  @Override
  public ReadCounts getNotificationsReadCounts(
      UUID organizationId, String projectId, String userEmail, String projectType) {

    validateEmailAndProjectType(userEmail, projectType);

    String projectTypeValue = null;

    if (Objects.nonNull(projectType)) {

      projectTypeValue =
          !projectType.isBlank() ? ProjectType.fromValue(projectType).toString() : null;
    }

    return notificationRepository.getNotificationsReadCounts(
        organizationId, projectId, userEmail, projectTypeValue);
  }

  @Override
  public void deleteNotificationByUserEmailAndProjectType(
      UUID organizationId, String userEmail, String projectType) {

    validateEmailAndProjectType(userEmail, projectType);

    String projectTypes =
        Objects.nonNull(projectType) ? ProjectType.fromValue(projectType).toString() : null;

    notificationRepository.deleteByOrganizationIdAndUserEmailAndProjectType(
        organizationId, userEmail, projectTypes);
  }

  @Override
  public List<NotificationDto> createAndSendNotificationsFromGroup(
      UUID organizationId,
      String projectId,
      List<@Valid NotificationGroupCreateDto> notificationGroupCreateDto) {

    List<GroupDto> groups =
        userManagementClient.getGroupsByOrganizationIdAndNames(
            organizationId,
            notificationGroupCreateDto.stream()
                .map(NotificationGroupCreateDto::getGroup)
                .collect(Collectors.toSet()));

    if (groups.isEmpty()) {
      throw new NoSuchElementException("No group found from the list provided");
    }

    log.info(
        "Received {} groups to notify from organization ID: {}", groups.size(), organizationId);

    List<Notification> notifications =
        notificationGroupCreateDto.stream()
            .flatMap(
                notification ->
                    groups.stream()
                        .filter(group -> group.getName().equals(notification.getGroup()))
                        .findFirst()
                        .stream()
                        .flatMap(
                            group ->
                                group.getMemberEmails().stream()
                                    .map(
                                        email ->
                                            notificationMapper.toEntity(
                                                organizationId, projectId, email, notification))))
            .toList();

    log.info("Generated {} notifications to save.", notifications.size());

    notificationRepository.saveAll(notifications);
    customWebSocketHandler.notifySubscribers(organizationId, notifications);

    return notifications.stream().map(notificationMapper::toDto).toList();
  }

  private void validateEmailAndProjectType(String userEmail, String projectType) {
    if (!isValidEmail(userEmail)) {
      throw new IllegalArgumentException(userEmail + "is not a valid email");
    }

    List<String> stringEnumList =
        Arrays.stream(ProjectType.values()).map(ProjectType::getValue).toList();

    if (Objects.nonNull(projectType)
        && !projectType.isBlank()
        && !stringEnumList.contains(projectType)) {
      throw new IllegalArgumentException(
          "Possible project type values are data, intelligence, application and digitaltwin");
    }
  }

  public boolean isValidEmail(String email) {
    Pattern pattern = Pattern.compile(EMAIL_REGEX);
    Matcher matcher = pattern.matcher(email);
    return matcher.matches();
  }

  public void logReadChangeStatus(
      UUID organizationId, String projectId, List<Notification> notifications) {

    log.info(
        "Changing the status: {} notifications to read for organizationId: {} and projectId {} ",
        notifications.size(),
        organizationId,
        projectId);
  }

  private void checkForActionNNeeded(List<Notification> notifications) {

    List<String> blockedNotificationIds =
        notifications.stream()
            .filter(Notification::isActionNeeded)
            .map(Notification::getId)
            .toList();

    if (!blockedNotificationIds.isEmpty()) {
      log.error(
          "No notifications were marked as read because actionNeeded is true for notifications: {}",
          blockedNotificationIds);
      throw new IllegalArgumentException(
          "Cannot mark notifications as read because actionNeeded is true for notifications: "
              + blockedNotificationIds);
    }
  }
}
