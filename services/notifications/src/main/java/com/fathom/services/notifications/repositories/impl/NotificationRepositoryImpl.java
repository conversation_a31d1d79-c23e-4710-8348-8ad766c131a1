package com.fathom.services.notifications.repositories.impl;

import com.fathom.services.notifications.model.Notification;
import com.fathom.services.notifications.model.dto.ReadCounts;
import com.fathom.services.notifications.model.filter.NotificationFilter;
import com.fathom.services.notifications.model.filter.NotificationFilterCount;
import com.fathom.services.notifications.model.filter.NotificationFilterObject;
import com.fathom.services.notifications.repositories.NotificationRepositoryCustom;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

@RequiredArgsConstructor
@Slf4j
public class NotificationRepositoryImpl implements NotificationRepositoryCustom {

  private static final String ORGANIZATION_ID_FIELD_NAME = "organizationId";
  private static final String PROJECT_ID_FIELD_NAME = "projectId";
  private static final String PROJECT_TYPE_FIELD_NAME = "projectType";
  private static final String READ_FIELD_NAME = "isRead";
  private static final String USER_EMAIL_FIELD_NAME = "userEmail";

  private final MongoTemplate mongoTemplate;

  @Override
  public Page<Notification> getNotificationByFilterObject(
      UUID organizationId, NotificationFilter filterObject) {

    filterObject.setPageNumber(filterObject.getPageNumber() - 1);

    if (filterObject.getPageNumber() < 0) {
      filterObject.setPageNumber(0);
    }

    if (filterObject.getPageSize() < 1) {
      filterObject.setPageSize(10);
    }

    Pageable pageable = PageRequest.of(filterObject.getPageNumber(), filterObject.getPageSize());
    Query query = filterObject.isPageable() ? new Query().with(pageable) : new Query();

    query.addCriteria(
        Criteria.where(ORGANIZATION_ID_FIELD_NAME).is(UUID.fromString(organizationId.toString())));

    if (Objects.nonNull(filterObject.getFilter())) {
      addFilterCriteria(filterObject.getFilter(), query);
    }

    var notificationList = mongoTemplate.find(query, Notification.class);
    return listToPage(notificationList, organizationId, filterObject);
  }

  @Override
  public Integer getNotificationCountByFilterObject(
      UUID organizationId, NotificationFilterCount filterObject) {

    Query query = new Query();

    query.addCriteria(
        Criteria.where(ORGANIZATION_ID_FIELD_NAME).is(UUID.fromString(organizationId.toString())));

    if (Objects.nonNull(filterObject.getFilter())) {
      addFilterCriteria(filterObject.getFilter(), query);
    }

    return (int) mongoTemplate.count(query, Notification.class);
  }

  @Override
  public ReadCounts getNotificationsReadCounts(
      UUID organizationId, String projectId, String userEmail, String projectType) {

    ReadCounts readCounts = new ReadCounts();

    Query query0 = getQuery(organizationId, projectId, userEmail, projectType);
    query0.addCriteria(Criteria.where(READ_FIELD_NAME).is(true));
    readCounts.setRead((int) mongoTemplate.count(query0, Notification.class));

    Query query1 = getQuery(organizationId, projectId, userEmail, projectType);
    query1.addCriteria(Criteria.where(READ_FIELD_NAME).is(false));
    readCounts.setUnread((int) mongoTemplate.count(query1, Notification.class));

    return readCounts;
  }

  @Override
  public List<Notification> findByOrganizationIdAndProjectIdAndIdInAnReadIgnoreNull(
      UUID organizationId, String projectId, boolean isRead, Set<String> ids) {
    Query query = new Query();

    query.addCriteria(
        Criteria.where(ORGANIZATION_ID_FIELD_NAME).is(UUID.fromString(organizationId.toString())));

    query.addCriteria(Criteria.where(READ_FIELD_NAME).is(isRead));

    query.addCriteria(Criteria.where("id").in(ids));

    if (Objects.nonNull(projectId)) {
      query.addCriteria(Criteria.where(PROJECT_ID_FIELD_NAME).is(projectId));
    }

    return mongoTemplate.find(query, Notification.class);
  }

  @Override
  public List<Notification>
      findByOrganizationIdAndProjectIdAndUserEmailAndProjectTypeAndReadIgnoreNull(
          UUID organizationId,
          String projectId,
          String userEmail,
          boolean isRead,
          String projectType) {

    Query query = new Query();

    query.addCriteria(
        Criteria.where(ORGANIZATION_ID_FIELD_NAME).is(UUID.fromString(organizationId.toString())));

    query.addCriteria(Criteria.where(USER_EMAIL_FIELD_NAME).is(userEmail));

    query.addCriteria(Criteria.where(READ_FIELD_NAME).is(isRead));

    if (Objects.nonNull(projectId)) {
      query.addCriteria(Criteria.where(PROJECT_ID_FIELD_NAME).is(projectId));
    }

    if (Objects.nonNull(projectType)) {
      query.addCriteria(Criteria.where(PROJECT_TYPE_FIELD_NAME).is(projectType));
    }

    return mongoTemplate.find(query, Notification.class);
  }

  @Override
  public List<Notification> findByOrganizationIdAndUserEmailAndOptionalProjectId(
      UUID organizationId, String userEmail, String projectId) {

    Criteria criteria =
        Criteria.where(ORGANIZATION_ID_FIELD_NAME)
            .is(organizationId)
            .and(USER_EMAIL_FIELD_NAME)
            .is(userEmail);

    if (projectId != null) {
      criteria = criteria.and(PROJECT_ID_FIELD_NAME).is(projectId);
    }

    Query query = new Query(criteria);
    return mongoTemplate.find(query, Notification.class);
  }

  private Query getQuery(
      UUID organizationId, String projectId, String userEmail, String projectType) {
    Query query = new Query();

    query.addCriteria(
        Criteria.where(ORGANIZATION_ID_FIELD_NAME).is(UUID.fromString(organizationId.toString())));

    query.addCriteria(Criteria.where(USER_EMAIL_FIELD_NAME).is(userEmail));

    if (Objects.nonNull(projectType)) {
      query.addCriteria(Criteria.where(PROJECT_TYPE_FIELD_NAME).is(projectType));
    }

    if (Objects.nonNull(projectId)) {
      query.addCriteria(Criteria.where(PROJECT_ID_FIELD_NAME).is(projectId));
    }

    return query;
  }

  private void addFilterCriteria(NotificationFilterObject filterObject, Query query) {

    if (Objects.nonNull(filterObject.getMetadata())) {

      Criteria[] criteriaArray =
          filterObject.getMetadata().entrySet().stream()
              .map(entry -> Criteria.where("metadata." + entry.getKey()).is(entry.getValue()))
              .toArray(Criteria[]::new);

      query.addCriteria(new Criteria().andOperator(criteriaArray));
    }

    if (Objects.nonNull(filterObject.getUserEmail())) {
      query.addCriteria(Criteria.where(USER_EMAIL_FIELD_NAME).is(filterObject.getUserEmail()));
    }
    if (Objects.nonNull(filterObject.getMessage())) {
      query.addCriteria(Criteria.where("message").regex(filterObject.getMessage(), "i"));
    }
    if (Objects.nonNull(filterObject.getProjectType())) {
      query.addCriteria(Criteria.where(PROJECT_TYPE_FIELD_NAME).is(filterObject.getProjectType()));
    }

    if (Objects.nonNull(filterObject.getIsRead())) {
      query.addCriteria(
          Criteria.where(READ_FIELD_NAME)
              .is(Boolean.parseBoolean(String.valueOf(filterObject.getIsRead()))));
    }

    if (Objects.nonNull(filterObject.getProjectId())) {
      query.addCriteria(Criteria.where(PROJECT_ID_FIELD_NAME).is(filterObject.getProjectId()));
    }

    if (Objects.nonNull(filterObject.getCreatedDate())) {

      if (Objects.isNull(filterObject.getCreatedDate().getMin())
          || Objects.isNull(filterObject.getCreatedDate().getMax())
          || !filterObject
              .getCreatedDate()
              .getMax()
              .isAfter(filterObject.getCreatedDate().getMin())) {
        log.error("Invalid date range");
      }

      query.addCriteria(
          Criteria.where("createdDate")
              .gte(filterObject.getCreatedDate().getMin())
              .lte(filterObject.getCreatedDate().getMax()));
    }
  }

  private Page<Notification> listToPage(
      List<Notification> entities, UUID organizationId, NotificationFilter filterObject) {

    var countFilterObject = new NotificationFilterCount();
    countFilterObject.setFilter(filterObject.getFilter());

    int count = getNotificationCountByFilterObject(organizationId, countFilterObject);

    Pageable pageable0;

    if (!entities.isEmpty()) {

      if (filterObject.isPageable()) {
        pageable0 = PageRequest.of(filterObject.getPageNumber(), filterObject.getPageSize());

      } else {
        pageable0 = PageRequest.of(0, filterObject.getPageSize());
      }

    } else {
      pageable0 = PageRequest.of(0, filterObject.getPageSize());
    }

    return new PageImpl<>(entities, pageable0, count);
  }
}
