package com.fathom.services.notifications.repositories;

import com.fathom.services.notifications.model.Notification;
import com.fathom.services.notifications.model.dto.ReadCounts;
import com.fathom.services.notifications.model.filter.NotificationFilter;
import com.fathom.services.notifications.model.filter.NotificationFilterCount;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;

public interface NotificationRepositoryCustom {
  Page<Notification> getNotificationByFilterObject(
      UUID organizationId, NotificationFilter filterObject);

  Integer getNotificationCountByFilterObject(
      UUID organizationId, NotificationFilterCount filterObject);

  ReadCounts getNotificationsReadCounts(
      UUID organizationId, String projectId, String userEmail, String projectType);

  List<Notification> findByOrganizationIdAndProjectIdAndIdInAnReadIgnoreNull(
      UUID organizationId, String projectId, boolean isRead, Set<String> ids);

  List<Notification> findByOrganizationIdAndProjectIdAndUserEmailAndProjectTypeAndReadIgnoreNull(
      UUID organizationId, String projectId, String userEmail, boolean isRead, String projectType);

  List<Notification> findByOrganizationIdAndUserEmailAndOptionalProjectId(
      UUID organizationId, String userEmail, String projectId);
}
