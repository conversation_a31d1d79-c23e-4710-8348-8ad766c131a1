package com.fathom.services.notifications.controllers.impl;

import static com.fathom.services.notifications.util.StaticProperties.*;

import com.fathom.services.notifications.controllers.NotificationControllerV1;
import com.fathom.services.notifications.model.dto.NotificationCreateDto;
import com.fathom.services.notifications.model.dto.NotificationDto;
import com.fathom.services.notifications.model.dto.NotificationGroupCreateDto;
import com.fathom.services.notifications.model.dto.ReadCounts;
import com.fathom.services.notifications.model.filter.NotificationFilter;
import com.fathom.services.notifications.model.filter.NotificationFilterCount;
import com.fathom.services.notifications.services.NotificationService;
import jakarta.validation.Valid;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@Validated
@RequiredArgsConstructor
public class NotificationController implements NotificationControllerV1 {

  private final NotificationService notificationService;

  @Override
  @GetMapping("notifications")
  public ResponseEntity<Page<NotificationDto>> getNotificationByOrganizationIdAndProjectId(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestParam(required = false) String projectId,
      @RequestParam(defaultValue = "1", required = false) int pageNumber,
      @RequestParam(defaultValue = "10", required = false) int pageSize,
      @RequestParam(defaultValue = "false", required = false) boolean pageable) {

    return ResponseEntity.ok(
        notificationService.getNotificationByOrganizationIdAndProjectId(
            organizationId, projectId, pageNumber, pageSize, pageable));
  }

  @Override
  @PostMapping("notifications")
  public ResponseEntity<List<NotificationDto>> createAndSendNotifications(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(PROJECT_HEADER) String projectId,
      @RequestBody @Valid List<@Valid NotificationCreateDto> notificationCreateDto) {

    return new ResponseEntity<>(
        notificationService.createAndSendNotification(
            organizationId, projectId, notificationCreateDto),
        HttpStatus.CREATED);
  }

  @Override
  @PostMapping("notifications/from-group")
  public ResponseEntity<List<NotificationDto>> createAndSendNotificationsFromGroup(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(PROJECT_HEADER) String projectId,
      @RequestBody @Valid List<@Valid NotificationGroupCreateDto> notificationGroupCreateDto) {

    return new ResponseEntity<>(
        notificationService.createAndSendNotificationsFromGroup(
            organizationId, projectId, notificationGroupCreateDto),
        HttpStatus.CREATED);
  }

  @Override
  @PutMapping("notifications/read")
  public ResponseEntity<List<NotificationDto>> markNotificationsAsRead(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestParam(required = false) String projectId,
      @RequestBody Set<String> ids) {

    return ResponseEntity.ok(
        notificationService.markNotificationsAsRead(organizationId, projectId, ids));
  }

  @Override
  @PutMapping("notifications/action-completed")
  public ResponseEntity<List<NotificationDto>> markNotificationsAsActionNeededCompleted(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestParam(required = false) String projectId,
      @RequestBody Set<String> ids) {
    return ResponseEntity.ok(
        notificationService.markNotificationsAsActionNeededCompleted(
            organizationId, projectId, ids));
  }

  @PutMapping("notifications/read/{userEmail}")
  public ResponseEntity<List<NotificationDto>> markNotificationsAsReadByUserEmailAndProjectType(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestParam(required = false) String projectId,
      @PathVariable String userEmail,
      @RequestParam(required = false) String projectType) {

    return ResponseEntity.ok(
        notificationService.markNotificationsAsReadByUserEmailAndProjectType(
            organizationId, projectId, userEmail, projectType));
  }

  @Override
  @PostMapping("notifications/filter")
  public ResponseEntity<Page<NotificationDto>> getNotificationByFilterObject(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestBody NotificationFilter filterObject) {

    return new ResponseEntity<>(
        notificationService.getNotificationByFilterObject(organizationId, filterObject),
        HttpStatus.OK);
  }

  @Override
  @PostMapping("notifications/filter/count")
  public ResponseEntity<Integer> getNotificationCountByFilterObject(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestBody NotificationFilterCount filterObject) {

    return new ResponseEntity<>(
        notificationService.getNotificationCountByFilterObject(organizationId, filterObject),
        HttpStatus.OK);
  }

  @Override
  @GetMapping("notifications/read/count/{userEmail}")
  public ResponseEntity<ReadCounts> getNotificationsReadCounts(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @PathVariable String userEmail,
      @RequestParam(required = false) String projectId,
      @RequestParam(required = false) String projectType) {

    return new ResponseEntity<>(
        notificationService.getNotificationsReadCounts(
            organizationId, projectId, userEmail, projectType),
        HttpStatus.OK);
  }

  @Override
  @PostMapping("notifications/delete")
  public ResponseEntity<NotificationDto> deleteManyNotifications(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(PROJECT_HEADER) String projectId,
      @RequestBody Set<String> idsSet) {
    notificationService.deleteManyNotifications(organizationId, projectId, idsSet);

    return ResponseEntity.ok().build();
  }

  @Override
  @DeleteMapping("notifications")
  public ResponseEntity<NotificationDto> deleteNotificationByUserEmailAndProjectType(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String userEmail,
      @RequestParam String projectType) {

    notificationService.deleteNotificationByUserEmailAndProjectType(
        organizationId, userEmail, projectType);
    return ResponseEntity.ok().build();
  }
}
