package com.fathom.services.notifications.model.filter;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fathom.services.notifications.model.enums.ProjectType;
import java.util.Map;
import lombok.Data;

@Data
public class NotificationFilterObject {

  String projectId;
  String userEmail;
  ProjectType projectType;
  Map<String, String> metadata;
  String message;
  DateRange createdDate;

  @JsonProperty("read")
  Boolean isRead;
}
