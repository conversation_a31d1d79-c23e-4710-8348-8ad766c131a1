package com.fathom.services.notifications.repositories;

import com.fathom.services.notifications.model.NotificationProjectsConfiguration;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.mongodb.repository.MongoRepository;

public interface NotificationProjectTypeRepository
    extends MongoRepository<NotificationProjectsConfiguration, String> {

  boolean existsByOrganizationId(UUID organizationId);

  Optional<NotificationProjectsConfiguration> findByOrganizationId(UUID organizationId);

  void deleteByOrganizationId(UUID organizationId);
}
