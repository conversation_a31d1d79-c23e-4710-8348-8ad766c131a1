package com.fathom.services.notifications.controllers.impl;

import static com.fathom.services.notifications.util.StaticProperties.ORGANIZATION_HEADER;

import com.fathom.services.notifications.controllers.NotificationProjectsConfigurationV1;
import com.fathom.services.notifications.model.dto.NotificationProjectsConfigurationCreateUpdateDto;
import com.fathom.services.notifications.model.dto.NotificationProjectsConfigurationDto;
import com.fathom.services.notifications.services.NotificationProjectsConfigurationService;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@Validated
@RequiredArgsConstructor
public class NotificationProjectsConfiguration implements NotificationProjectsConfigurationV1 {

  private final NotificationProjectsConfigurationService notificationProjectTypeService;

  @Override
  @GetMapping("notifications/supported-project-types")
  public ResponseEntity<NotificationProjectsConfigurationDto> get(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId) {

    return new ResponseEntity<>(
        notificationProjectTypeService.getNotificationProjectsConfiguration(organizationId),
        HttpStatus.OK);
  }

  @Override
  @PostMapping("notifications/supported-project-types")
  public ResponseEntity<NotificationProjectsConfigurationDto> create(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestBody
          NotificationProjectsConfigurationCreateUpdateDto
              notificationProjectsConfigurationCreateDto) {
    return new ResponseEntity<>(
        notificationProjectTypeService.create(
            organizationId, notificationProjectsConfigurationCreateDto),
        HttpStatus.CREATED);
  }

  @Override
  @PutMapping("notifications/supported-project-types")
  public ResponseEntity<NotificationProjectsConfigurationDto> update(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestBody
          NotificationProjectsConfigurationCreateUpdateDto
              notificationProjectsConfigurationCreateDto) {

    return new ResponseEntity<>(
        notificationProjectTypeService.update(
            organizationId, notificationProjectsConfigurationCreateDto),
        HttpStatus.OK);
  }

  @Override
  @DeleteMapping("notifications/supported-project-types")
  public ResponseEntity<NotificationProjectsConfigurationDto> delete(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId) {

    notificationProjectTypeService.delete(organizationId);
    return ResponseEntity.ok().build();
  }
}
