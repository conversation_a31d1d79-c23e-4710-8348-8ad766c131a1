package com.fathom.services.notifications.services.impl;

import com.fathom.services.notifications.mapper.NotificationProjectTypeMapper;
import com.fathom.services.notifications.model.NotificationProjectsConfiguration;
import com.fathom.services.notifications.model.dto.NotificationProjectsConfigurationCreateUpdateDto;
import com.fathom.services.notifications.model.dto.NotificationProjectsConfigurationDto;
import com.fathom.services.notifications.repositories.NotificationProjectTypeRepository;
import com.fathom.services.notifications.services.NotificationProjectsConfigurationService;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationProjectsConfigurationServiceImpl
    implements NotificationProjectsConfigurationService {

  private final NotificationProjectTypeRepository notificationProjectTypeRepository;
  private final NotificationProjectTypeMapper notificationProjectTypeMapper;

  @Override
  public void createNotificationProjectsConfigurationUsingDefaultValues(
      UUID organizationId, String projectId) {

    if (notificationProjectTypeRepository.existsByOrganizationId(organizationId)) {
      log.info(
          "Default configuration is already there for org {} will not create a duplicate record. Returning ...",
          organizationId);
      return;
    }

    notificationProjectTypeRepository.save(
        notificationProjectTypeMapper.toEntityUsingDefaultValues(organizationId));
  }

  @Override
  public NotificationProjectsConfigurationDto getNotificationProjectsConfiguration(
      UUID organizationId) {

    NotificationProjectsConfiguration notificationProjectsConfiguration =
        notificationProjectTypeRepository
            .findByOrganizationId(organizationId)
            .orElseGet(
                () -> {
                  log.warn(
                      "no notification projects configuration is found for organizationId create a new one with default values: {}",
                      organizationId);
                  return notificationProjectTypeRepository.save(
                      notificationProjectTypeMapper.toEntityUsingDefaultValues(organizationId));
                });

    return notificationProjectTypeMapper.toDto(notificationProjectsConfiguration);
  }

  @Override
  public NotificationProjectsConfigurationDto create(
      UUID organizationId,
      NotificationProjectsConfigurationCreateUpdateDto notificationProjectsConfigurationCreateDto) {

    NotificationProjectsConfiguration orgnizationNotificationProjectsConfiguration =
        notificationProjectTypeRepository.findByOrganizationId(organizationId).orElse(null);

    if (Objects.isNull(orgnizationNotificationProjectsConfiguration)) {
      return notificationProjectTypeMapper.toDto(
          notificationProjectTypeRepository.save(
              notificationProjectTypeMapper.toNewEntity(
                  organizationId, notificationProjectsConfigurationCreateDto)));
    } else {
      return notificationProjectTypeMapper.toDto(
          notificationProjectTypeRepository.save(
              notificationProjectTypeMapper.updateDtoToEntity(
                  orgnizationNotificationProjectsConfiguration,
                  notificationProjectsConfigurationCreateDto)));
    }
  }

  @Override
  public NotificationProjectsConfigurationDto update(
      UUID organizationId,
      NotificationProjectsConfigurationCreateUpdateDto notificationProjectsConfigurationCreateDto) {

    NotificationProjectsConfiguration orgnizationNotificationProjectsConfiguration =
        notificationProjectTypeRepository
            .findByOrganizationId(organizationId)
            .orElseThrow(
                () ->
                    new NoSuchElementException(
                        "no notification projects configuration is found for org: %s"
                            .formatted(organizationId)));

    return notificationProjectTypeMapper.toDto(
        notificationProjectTypeRepository.save(
            notificationProjectTypeMapper.updateDtoToEntity(
                orgnizationNotificationProjectsConfiguration,
                notificationProjectsConfigurationCreateDto)));
  }

  @Override
  public void delete(UUID organizationId) {
    notificationProjectTypeRepository.deleteByOrganizationId(organizationId);
  }
}
