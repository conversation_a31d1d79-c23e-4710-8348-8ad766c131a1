package com.fathom.services.notifications.services;

import com.fathom.services.notifications.model.dto.*;
import com.fathom.services.notifications.model.filter.NotificationFilter;
import com.fathom.services.notifications.model.filter.NotificationFilterCount;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;

public interface NotificationService {

  Page<NotificationDto> getNotificationByOrganizationIdAndProjectId(
      UUID organizationId, String projectId, int pageNumber, int pageSize, boolean pageable);

  List<NotificationDto> createAndSendNotification(
      UUID organizationId,
      String projectId,
      @Valid List<NotificationCreateDto> notificationCreateDtoList);

  List<NotificationDto> markNotificationsAsRead(
      UUID organizationId, String projectId, Set<String> ids);

  List<NotificationDto> markNotificationsAsReadByUserEmailAndProjectType(
      UUID organizationId, String projectId, String userEmail, String projectType);

  List<NotificationDto> markNotificationsAsActionNeededCompleted(
      UUID organizationId, String projectId, Set<String> ids);

  Page<NotificationDto> getNotificationByFilterObject(
      UUID organizationId, NotificationFilter filterObject);

  Integer getNotificationCountByFilterObject(
      UUID organizationId, NotificationFilterCount notificationFilterCount);

  ProjectTypes getSupportedProjectTypes();

  void deleteManyNotifications(UUID organizationId, String projectId, Set<String> idsSet);

  ReadCounts getNotificationsReadCounts(
      UUID organizationId, String projectId, String userEmail, String projectType);

  void deleteNotificationByUserEmailAndProjectType(
      UUID organizationId, String userEmail, String projectType);

  List<NotificationDto> createAndSendNotificationsFromGroup(
      UUID organizationId,
      String projectId,
      @Valid List<@Valid NotificationGroupCreateDto> notificationGroupCreateDto);
}
