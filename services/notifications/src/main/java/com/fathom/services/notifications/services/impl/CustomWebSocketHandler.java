package com.fathom.services.notifications.services.impl;

import static com.fathom.services.notifications.util.StaticProperties.PRIVATE_TOPIC_BASE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fathom.services.notifications.model.Notification;
import com.fathom.services.notifications.repositories.NotificationRepository;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomWebSocketHandler extends AbstractWebSocketHandler {

  private final Map<String, Set<WebSocketSession>> sessions;
  private final NotificationRepository notificationRepository;

  @Override
  public void afterConnectionEstablished(WebSocketSession session) throws IOException {

    String sessionKey = setSessionKey(session);

    if (Objects.isNull(sessionKey)) {
      return;
    }

    if (Objects.nonNull(sessions.get(sessionKey))) {
      sessions.get(sessionKey).add(session);
    } else {
      Set<WebSocketSession> socketSessions = new HashSet<>();
      socketSessions.add(session);
      sessions.put(sessionKey, socketSessions);
    }

    long s = Objects.nonNull(sessions.get(sessionKey)) ? (long) sessions.get(sessionKey).size() : 0;
    log.info("Session count is {} for key: {}", s, sessionKey);

    notifyAfterConnectionEstablished(session);
  }

  private void notifyAfterConnectionEstablished(WebSocketSession session) {

    var paramPair = getOrganizationIdUserEmailAndProjectId(session);

    List<Notification> notifications =
        notificationRepository
            .findByOrganizationIdAndUserEmailAndOptionalProjectId(
                UUID.fromString(paramPair.getLeft()), paramPair.getMiddle(), paramPair.getRight())
            .stream()
            .sorted(Comparator.comparing(Notification::getCreatedDate).reversed())
            .toList();

    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

    try {
      session.sendMessage(new TextMessage(objectMapper.writeValueAsString(notifications)));
    } catch (IOException e) {
      log.error(e.getMessage());
    }
  }

  public void sendNotifications(String destination, List<Notification> notifications) {

    Set<WebSocketSession> webSocketSessions = sessions.get(destination);

    if (Objects.isNull(webSocketSessions) || webSocketSessions.isEmpty()) {
      log.info(
          "No active session for for session with key {} noting will be send via websocket endpoint",
          destination);
      return;
    }

    ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.registerModule(new JavaTimeModule());
    objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

    webSocketSessions.forEach(
        session -> {
          try {
            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(notifications)));
          } catch (IOException e) {
            log.error(e.getMessage());
          }
        });
  }

  public void notifySubscribers(UUID organizationId, List<Notification> notifications) {

    List<String> userEmails =
        notifications.stream().map(Notification::getUserEmail).distinct().toList();

    List<Notification> allNotifications =
        notificationRepository.findByOrganizationIdAndUserEmailIn(organizationId, userEmails);

    // Group by userEmail first
    Map<String, List<Notification>> userGroupedNotifications =
        allNotifications.stream().collect(Collectors.groupingBy(Notification::getUserEmail));

    userGroupedNotifications.forEach(
        (userEmail, userNotifications) -> {
          // Step 1: Notify the general user channel (no project ID)
          String generalDestination =
              "%s_%s_%s"
                  .formatted(
                      Objects.requireNonNull(
                          PRIVATE_TOPIC_BASE, "PRIVATE_TOPIC_BASE cannot be null"),
                      Objects.requireNonNull(organizationId, "organizationId cannot be null"),
                      Objects.requireNonNull(userEmail, "userEmail cannot be null"));

          // Sort notifications by createdDate (newest first)
          userNotifications.sort(Comparator.comparing(Notification::getCreatedDate).reversed());

          // Send to general channel (all notifications)
          sendNotifications(generalDestination, userNotifications);

          // Step 2: Group by projectId and notify those channels separately
          Map<String, List<Notification>> projectGroupedNotifications =
              userNotifications.stream()
                  .filter(n -> n.getProjectId() != null)
                  .collect(Collectors.groupingBy(Notification::getProjectId));

          projectGroupedNotifications.forEach(
              (projectId, projectNotifications) -> {
                String projectDestination =
                    "%s_%s_%s_%s"
                        .formatted(
                            Objects.requireNonNull(
                                PRIVATE_TOPIC_BASE, "PRIVATE_TOPIC_BASE cannot be null"),
                            Objects.requireNonNull(organizationId, "organizationId cannot be null"),
                            Objects.requireNonNull(userEmail, "userEmail cannot be null"),
                            projectId);

                // Send notifications to project-specific channels
                sendNotifications(projectDestination, projectNotifications);
              });
        });
  }

  private String setSessionKey(WebSocketSession session) throws IOException {

    var paramPair = getOrganizationIdUserEmailAndProjectId(session);
    String organizationId = paramPair.getLeft();
    String userEmail = paramPair.getMiddle();
    String projectId = paramPair.getRight();

    if (Objects.isNull(organizationId) || Objects.isNull(userEmail)) {
      session.sendMessage(
          new TextMessage(
              "Connect closed due to missing mandatory query parameters: organizationId=%s, userEmail=%s"
                  .formatted(organizationId, userEmail)));
      session.close();
      return null;
    }

    return Objects.isNull(projectId)
        ? "%s_%s_%s".formatted(PRIVATE_TOPIC_BASE, organizationId, userEmail)
        : "%s_%s_%s_%s".formatted(PRIVATE_TOPIC_BASE, organizationId, userEmail, projectId);
  }

  @Override
  public void afterConnectionClosed(WebSocketSession session, CloseStatus status)
      throws IOException {

    if (Objects.nonNull(sessions.get(setSessionKey(session)))) {
      log.info(
          "Destroying session with id: {} for key: {}", session.getId(), setSessionKey(session));
      sessions.get(setSessionKey(session)).remove(session);
      session.close();
    }
  }

  private Triple<String, String, String> getOrganizationIdUserEmailAndProjectId(
      WebSocketSession session) {
    String uri = Objects.requireNonNull(session.getUri()).toString();
    UriComponents uriComponents = UriComponentsBuilder.fromUriString(uri).build();

    String organizationId = uriComponents.getQueryParams().getFirst("organizationId");
    String userEmail = uriComponents.getQueryParams().getFirst("userEmail");
    String projectId = uriComponents.getQueryParams().getFirst("projectId");

    return Triple.of(organizationId, userEmail, projectId);
  }
}
