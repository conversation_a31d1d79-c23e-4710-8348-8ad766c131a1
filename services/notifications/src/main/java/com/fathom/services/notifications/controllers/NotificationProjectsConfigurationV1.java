package com.fathom.services.notifications.controllers;

import com.fathom.services.notifications.model.dto.NotificationProjectsConfigurationCreateUpdateDto;
import com.fathom.services.notifications.model.dto.NotificationProjectsConfigurationDto;
import io.swagger.v3.oas.annotations.Operation;
import java.util.UUID;
import org.springframework.http.ResponseEntity;

public interface NotificationProjectsConfigurationV1 {

  @Operation(
      summary = "Endpoint for retrieving supported project types.",
      description = "Endpoint for retrieving supported project types.")
  ResponseEntity<NotificationProjectsConfigurationDto> get(UUID organizationId);

  @Operation(
      summary = "Endpoint for creating supported project types.",
      description = "Endpoint for creating supported project types.")
  ResponseEntity<NotificationProjectsConfigurationDto> create(
      UUID organizationId,
      NotificationProjectsConfigurationCreateUpdateDto notificationProjectsConfigurationCreateDto);

  @Operation(
      summary = "Endpoint for updating supported project types.",
      description = "Endpoint for updating supported project types.")
  ResponseEntity<NotificationProjectsConfigurationDto> update(
      UUID organizationId,
      NotificationProjectsConfigurationCreateUpdateDto notificationProjectsConfigurationCreateDto);

  @Operation(
      summary = "Endpoint for deleting supported project types.",
      description = "Endpoint for deleting supported project types.")
  ResponseEntity<NotificationProjectsConfigurationDto> delete(UUID organizationId);
}
