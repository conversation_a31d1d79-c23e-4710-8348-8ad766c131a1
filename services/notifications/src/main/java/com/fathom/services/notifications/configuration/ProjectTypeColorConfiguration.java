package com.fathom.services.notifications.configuration;

import com.fathom.services.notifications.model.config.ProjectTypeColor;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "default-settings")
public class ProjectTypeColorConfiguration {
  List<ProjectTypeColor> projectTypeColor = new ArrayList<>();
}
