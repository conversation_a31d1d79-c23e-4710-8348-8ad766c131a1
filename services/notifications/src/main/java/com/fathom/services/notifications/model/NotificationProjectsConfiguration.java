package com.fathom.services.notifications.model;

import java.util.Map;
import java.util.UUID;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document
public class NotificationProjectsConfiguration {
  @Id String id;

  @Indexed(unique = true)
  UUID organizationId;

  Map<String, String> supportedProjectTypes;
}
