package com.fathom.services.notifications.controllers.exception;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.NoSuchElementException;
import lombok.Builder;
import lombok.Data;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class ExceptionControllerAdvice {

  private static ErrorResponse of(HttpStatus status, String message) {
    return ErrorResponse.builder()
        .timestamp(LocalDateTime.now(ZoneId.of("UTC")))
        .status(status.getReasonPhrase())
        .message(message)
        .build();
  }

  @ExceptionHandler(ServletRequestBindingException.class)
  public ResponseEntity<ErrorResponse> handleServletRequestBindingException(
      ServletRequestBindingException ex) {
    ErrorResponse res = of(HttpStatus.BAD_REQUEST, ex.getMessage());
    return ResponseEntity.badRequest().body(res);
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<ErrorResponse> handleMethodArgumentNotValidException(
      MethodArgumentNotValidException ex) {
    ErrorResponse res = of(HttpStatus.BAD_REQUEST, ex.getMessage());
    return ResponseEntity.badRequest().body(res);
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<ErrorResponse> handleException(Exception ex) {
    ErrorResponse res = of(HttpStatus.BAD_REQUEST, ex.getMessage());
    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(res);
  }

  @ExceptionHandler(NoSuchElementException.class)
  public ResponseEntity<ErrorResponse> hadNoSuchElementException(Exception ex) {
    ErrorResponse res = of(HttpStatus.NOT_FOUND, ex.getMessage());
    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(res);
  }

  @Data
  @Builder
  private static class ErrorResponse {

    private final LocalDateTime timestamp;
    private final String status;
    private final String message;
  }
}
