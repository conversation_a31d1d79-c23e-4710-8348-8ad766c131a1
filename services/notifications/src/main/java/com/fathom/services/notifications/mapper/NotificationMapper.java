package com.fathom.services.notifications.mapper;

import com.fathom.services.notifications.model.Notification;
import com.fathom.services.notifications.model.dto.NotificationCreateDto;
import com.fathom.services.notifications.model.dto.NotificationDto;
import com.fathom.services.notifications.model.dto.NotificationGroupCreateDto;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class NotificationMapper {

  @Mapping(target = "organizationId", source = "organizationId")
  @Mapping(target = "projectId", source = "projectId")
  public abstract Notification toEntity(
      UUID organizationId, String projectId, NotificationCreateDto source);

  public abstract NotificationDto toDto(Notification source);

  @Mapping(target = "organizationId", source = "organizationId")
  @Mapping(target = "projectId", source = "projectId")
  @Mapping(target = "userEmail", source = "userEmail")
  public abstract Notification toEntity(
      UUID organizationId, String projectId, String userEmail, NotificationGroupCreateDto source);
}
