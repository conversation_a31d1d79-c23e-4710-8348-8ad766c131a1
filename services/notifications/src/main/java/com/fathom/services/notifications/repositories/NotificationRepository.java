package com.fathom.services.notifications.repositories;

import com.fathom.services.notifications.model.Notification;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface NotificationRepository
    extends MongoRepository<Notification, String>, NotificationRepositoryCustom {

  Page<Notification> findByOrganizationIdAndProjectId(
      UUID organizationId, String projectId, Pageable pageable);

  Page<Notification> findByOrganizationId(UUID organizationId, Pageable pageable);

  void deleteByOrganizationIdAndProjectIdAndIdIn(
      UUID organizationId, String projectId, Set<String> ids);

  @Query("{ 'organizationId': ?0, 'projectId': ?1, 'id': { '$in': ?2 } }")
  List<Notification> findByOrganizationIdAndProjectIdAndIds(
      UUID organizationId, String projectId, Set<String> ids);

  List<Notification> findByOrganizationIdAndUserEmailIn(
      UUID organizationId, List<String> userEmail);

  void deleteByOrganizationIdAndUserEmailAndProjectType(
      UUID organizationId, String userEmail, String projectType);
}
