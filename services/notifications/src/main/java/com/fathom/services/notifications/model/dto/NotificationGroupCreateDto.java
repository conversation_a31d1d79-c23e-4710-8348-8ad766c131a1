package com.fathom.services.notifications.model.dto;

import com.fathom.services.notifications.model.enums.ProjectType;
import jakarta.validation.constraints.NotEmpty;
import java.util.Map;
import lombok.Data;

@Data
public class NotificationGroupCreateDto {

  String group;

  ProjectType projectType;

  @NotEmpty(message = "Notification title cannot be null")
  String title;

  String message;
  Map<String, String> metadata;
  boolean actionNeeded = false;
}
