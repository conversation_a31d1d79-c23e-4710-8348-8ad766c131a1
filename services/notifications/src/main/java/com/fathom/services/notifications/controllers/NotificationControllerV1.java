package com.fathom.services.notifications.controllers;

import com.fathom.services.notifications.model.dto.NotificationCreateDto;
import com.fathom.services.notifications.model.dto.NotificationDto;
import com.fathom.services.notifications.model.dto.NotificationGroupCreateDto;
import com.fathom.services.notifications.model.dto.ReadCounts;
import com.fathom.services.notifications.model.filter.NotificationFilter;
import com.fathom.services.notifications.model.filter.NotificationFilterCount;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;

@OpenAPIDefinition(
    info =
        @Info(
            title = "Notification service",
            description = "This application used to create, produce and subscribe to notifications",
            license = @License(name = "Apache 2.0", url = "https://fathoms.io"),
            contact =
                @Contact(
                    name = "Fathom Solutions",
                    email = "<EMAIL>",
                    url = "https://fathom.io")))
public interface NotificationControllerV1 {

  @Operation(
      summary = "Endpoint for getting all resources by organization id and project id.",
      description = "Endpoint for getting all resources by organization id  and project id.")
  ResponseEntity<Page<NotificationDto>> getNotificationByOrganizationIdAndProjectId(
      UUID organizationId, String projectId, int pageNumber, int pageSize, boolean pageable);

  @Operation(
      summary = "Endpoint for creating many resources that will notify subscribers through ws.",
      description = "Endpoint for creating many resources that will notify subscribers through ws.")
  ResponseEntity<List<NotificationDto>> createAndSendNotifications(
      UUID organizationId,
      String projectId,
      @Valid List<@Valid NotificationCreateDto> notificationCreateDto);

  @Operation(
      summary = "Endpoint for creating many resources using a user groups. ",
      description = "Endpoint for creating many resources using a user groups. ")
  ResponseEntity<List<NotificationDto>> createAndSendNotificationsFromGroup(
      UUID organizationId,
      String projectId,
      @Valid List<@Valid NotificationGroupCreateDto> notificationGroupCreateDto);

  @Operation(
      summary = "Endpoint for marking many resource as read by a list of ids. ",
      description = "Endpoint for many resource as read by a list of ids.")
  ResponseEntity<List<NotificationDto>> markNotificationsAsRead(
      UUID organizationId, String projectId, Set<String> ids);

  @Operation(
      summary = "Endpoint for marking many resource as action needed = false by a list of ids. ",
      description =
          "Endpoint for marking many resource as action needed = false by a list of ids. ")
  ResponseEntity<List<NotificationDto>> markNotificationsAsActionNeededCompleted(
      UUID organizationId, String projectId, Set<String> ids);

  @Operation(
      summary =
          "Endpoint for marking many resource as read by user email and project type (optional). ",
      description =
          "Endpoint for many resource as read by user email and project type (optional). "
              + "Possible project type values are data, intelligence, application and digitaltwin")
  ResponseEntity<List<NotificationDto>> markNotificationsAsReadByUserEmailAndProjectType(
      UUID organizationId, String projectId, String userEmail, String projectType);

  @Operation(
      summary = "Endpoint for filter by advance filter object.",
      description = "Endpoint for filter by advance filter object.")
  ResponseEntity<Page<NotificationDto>> getNotificationByFilterObject(
      UUID organizationId, NotificationFilter filterObject);

  @Operation(
      summary = "Endpoint for to count by advance filter object.",
      description = "Endpoint to count by advance filter object.")
  ResponseEntity<Integer> getNotificationCountByFilterObject(
      UUID organizationId, NotificationFilterCount filterObject);

  @Operation(
      summary = "Endpoint for getting read status by user email and project type (optional).",
      description = "Endpoint for getting read status by user email and project type (optional).")
  ResponseEntity<ReadCounts> getNotificationsReadCounts(
      UUID organizationId, String userEmail, String projectId, String projectType);

  @Operation(
      summary = "Endpoint for deleting many notifications.",
      description = "Endpoint for deleting many notifications.")
  ResponseEntity<NotificationDto> deleteManyNotifications(
      UUID organizationId, String projectId, Set<String> idsSet);

  @Operation(
      summary = "Endpoint for deleting notification by user email and optional project type. ",
      description = "Endpoint for deleting notification by user email and optional project type. ")
  ResponseEntity<NotificationDto> deleteNotificationByUserEmailAndProjectType(
      UUID organizationId, String userEmail, String projectType);
}
