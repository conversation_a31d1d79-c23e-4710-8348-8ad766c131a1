package com.fathom.services.notifications.model;

import com.fathom.services.notifications.model.enums.ProjectType;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document
public class Notification {

  @Id String id;

  UUID organizationId;
  String projectId;
  String userEmail;
  ProjectType projectType;
  Map<String, String> metadata;
  String title;
  String message;
  LocalDateTime createdDate;
  boolean isRead;
  boolean actionNeeded;

  public Notification() {
    isRead = false;
    metadata = new HashMap<>();
    actionNeeded = false;

    ZonedDateTime gmtTime = ZonedDateTime.now(ZoneId.of("GMT"));
    createdDate = gmtTime.toLocalDateTime();
  }
}
