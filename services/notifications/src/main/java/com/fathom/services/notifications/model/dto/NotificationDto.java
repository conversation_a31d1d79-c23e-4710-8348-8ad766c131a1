package com.fathom.services.notifications.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fathom.services.notifications.model.enums.ProjectType;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import lombok.Data;

@Data
public class NotificationDto {

  String id;

  UUID organizationId;
  String projectId;

  String userEmail;
  ProjectType projectType;
  Map<String, String> metadata;
  String title;
  String message;
  LocalDateTime createdDate;

  @JsonProperty("read")
  boolean isRead;

  boolean actionNeeded;
}
