package com.fathom.services.notifications.configuration;

import com.fathom.services.notifications.repositories.NotificationRepository;
import com.fathom.services.notifications.services.impl.CustomWebSocketHandler;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@RequiredArgsConstructor
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

  private final Map<String, Set<WebSocketSession>> sessions = new HashMap<>();
  private final NotificationRepository notificationRepository;

  @Bean("sessions")
  Map<String, Set<WebSocketSession>> getSessions() {
    return sessions;
  }

  @Override
  public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
    registry
        .addHandler(new CustomWebSocketHandler(sessions, notificationRepository), "/websocket")
        .setAllowedOrigins("*");
    registry
        .addHandler(new CustomWebSocketHandler(sessions, notificationRepository), "/websocket")
        .setAllowedOrigins("*")
        .withSockJS();
  }
}
