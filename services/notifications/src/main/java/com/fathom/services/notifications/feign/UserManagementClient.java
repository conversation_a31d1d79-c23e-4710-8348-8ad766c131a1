package com.fathom.services.notifications.feign;

import static com.fathom.services.notifications.util.StaticProperties.ORGANIZATION_HEADER;

import com.fathom.services.notifications.model.feign.GroupDto;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${services.usermanagement.name}")
public interface UserManagementClient {

  @PostMapping("groups/byNames")
  List<GroupDto> getGroupsByOrganizationIdAndNames(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId, @RequestBody Set<String> name);
}
