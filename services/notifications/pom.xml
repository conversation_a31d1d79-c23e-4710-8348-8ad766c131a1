<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fathom</groupId>
    <artifactId>services</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <groupId>com.fathom.services</groupId>
  <artifactId>notifications</artifactId>
  <version>0.0.1</version>
  <name>notifications</name>
  <description>Fathom notifications service</description>

  <properties>
    <java.version>17</java.version>
    <lombok.version>1.18.36</lombok.version>
    <org-mapstruct.version>1.6.3</org-mapstruct.version>
  </properties>


    <!--		Spring-->
  <dependencies>
  <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <groupId>org.xmlunit</groupId>
          <artifactId>xmlunit-core</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
    </dependency>
    <dependency>
      <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
      <groupId>org.springdoc</groupId>
      <version>2.7.0</version>
    </dependency>
    <!--		Websocket-->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-websocket</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-amqp</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-reactor-netty</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.17.0</version>
    </dependency>
    <!-- Mapping -->
    <dependency>
      <artifactId>mapstruct</artifactId>
      <groupId>org.mapstruct</groupId>
      <version>${org-mapstruct.version}</version>
    </dependency>
    <!-- Cloud -->
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
      <exclusions>
        <exclusion>
          <groupId>commons-fileupload</groupId>
          <artifactId>commons-fileupload</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-kubernetes-fabric8-all</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-loadbalancer</artifactId>
    </dependency>
    <!-- Spring Data MongoDB -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-mongodb</artifactId>
    </dependency>
    <dependency>
      <groupId>jakarta.validation</groupId>
      <artifactId>jakarta.validation-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fathom</groupId>
      <artifactId>diagnostics</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.openrewrite.maven</groupId>
        <artifactId>rewrite-maven-plugin</artifactId>
        <version>5.47.3</version>
        <configuration>
          <activeRecipes>
            <recipe>org.openrewrite.java.spring.boot3.UpgradeSpringBoot_3_3</recipe>
          </activeRecipes>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.openrewrite.recipe</groupId>
            <artifactId>rewrite-spring</artifactId>
            <version>5.24.1</version>
            <scope>runtime</scope>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>com.spotify.fmt</groupId>
        <artifactId>fmt-maven-plugin</artifactId>
        <version>2.25</version>
      </plugin>
      <plugin>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <groupId>org.springframework.boot</groupId>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <annotationProcessorPaths>
            <path>
              <artifactId>lombok</artifactId>
              <groupId>org.projectlombok</groupId>
              <version>${lombok.version}</version>
            </path>
            <path>
              <artifactId>mapstruct-processor</artifactId>
              <groupId>org.mapstruct</groupId>
              <version>${org-mapstruct.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
        <groupId>org.apache.maven.plugins</groupId>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <id>nexus</id>
      <name>Nexus repository</name>
      <url>https://nexus.fathom-solutions.com/repository/maven-public/</url>
    </repository>
  </repositories>
</project>