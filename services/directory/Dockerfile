FROM eclipse-temurin:17-jre
ARG JAR_FILE=target/*.jar
COPY ${JAR_FILE} app.jar
ENV JAVA_OPTS=""
ENV OTHER_JAVA_OPTS=""

ARG VERSION="0.0.1-SNAPSHOT"
ARG BUILD_TIMESTAMP="unknown"
ARG GIT_HASH="unknown"
ARG GIT_BRANCH="main"
ARG GIT_COMMIT_TIMESTAMP="unknown"

ENV VERSION $VERSION
ENV BUILD_TIMESTAMP $BUILD_TIMESTAMP
ENV GIT_HASH $GIT_HASH
ENV GIT_BRANCH $GIT_BRANCH
ENV GIT_COMMIT_TIMESTAMP $GIT_COMMIT_TIMESTAMP

ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS $OTHER_JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar app.jar"]
