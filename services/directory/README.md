## Description

Directory service - service for managing the structure of policies, secured endpoints, permissions and resources, definitions for FRNs(Fathom resource name). If the service is present in the directory - the service is supposed to be secured.
For more information read [on Confluence](https://fathomsolutions.atlassian.net/wiki/spaces/FATDEV/pages/15040606/Directory+service).

## Deployment

This chapter describes service deployment details.

### Environment
It is recommended to turn on WSL2 configuration for Windows, to be able run linux commands in virtual Ubuntu.
You should have Docker Desktop(with WSL2 and default Kubernetes cluster turned on) and <PERSON><PERSON><PERSON>old installed.
All commands will be done in WSL2 virtual machine.

### Dependencies
To deploy this service properly you need to configure MongoDB on your server.

Preferred versions:
  - mongodb 4.4.1

### Service configuration

Below is a list of environment variables which can be consumed by this service.

(ENV_VAR_NAME: DEFAULT_VALUE - DESCRIPTION)

- DIRECTORY_SERVICE_PORT: directory service port;
- MONGO_HOST: host of mongodb;
- MONGO_PORT: mongodb's port;
- MONGO_DATABASE_NAME: mongodb's database name to use;

### Docker compose specific variables

```shell script
SPRING_PROFILES_ACTIVE=docker
```

### Build artifact

To build code and generate an artifact you need to run next command from project's root folder:
```
make build-artifact
```
or with sipping the tests:
```
make build-artifact-skip-tests
```
After that artifact will be generated and stored by the way below:

*./target/directory-x.x.x.jar*

### Build image

To build docker image you need to run next command from project's root folder:
```
make build-image
```

### Testing
You can run tests manually in your IDE or with the next command:
```
mvn clean install
```

### Run service

1. Build artifact (with or without running integration tests).
2. Build docker image.
3. If you have separate MongoDB on your machine, change host\port in configuration files ops/k8s/deployment.yaml and src/main/resources/application.yaml
 and run:
    ```
    make deploy-service
    ```
4. If you don't have separate MongoDB on your machine, run:
    ```
    make deploy-service-with-dependencies
    ```
    *Optional.* To access service outside of cluster (e.g. useful for testing on the local machine):
    ```
    kubectl expose deployment directory --type=LoadBalancer --name=directory-load-balancer
    ```
    Then run to find out **EXTERNAL-IP**:
    ```
    kubectl get services directory-load-balancer
    ```
    _NOTE._ If you are using **minikube** and see external IP as "**pending**" more than minute please try these [tips](https://minikube.sigs.k8s.io/docs/handbook/accessing/#check-external-ip).

    Now you can use your browser/cURL/Postman to query the Directory Service:
    ```
    http://REPLACE_WITH_EXTERNAL_IP:8086
    ```
    Delete LB service after testing:
    ```
    kubectl delete services directory-load-balancer
    ```
5. Stop service with dependencies:
    ```
    make delete-service-with-dependencies
    ```
6. Stop service:
    ```
    make delete-service
    ```

### Run service on local machine
To run service on the local machine you can repeat steps from the previous **Run service** section including the optional steps from _Step 4_ or use **docker-compose** (see the instruction in the _docker-compose.yml_ in the root of project.)

