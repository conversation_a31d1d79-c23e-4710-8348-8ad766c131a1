package com.fathom.services.directory.controller;

import static com.fathom.services.directory.controller.DirectoryController.DIR_ALREADY_EXIST_ERROR_MSG;
import static com.fathom.services.directory.controller.DirectoryController.NO_DIR_WITH_ID_ERROR_MSG;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.notNullValue;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.asyncDispatch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.request;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fathom.services.directory.BaseIntegrationTest;
import com.fathom.services.directory.dto.internal.InternalServicesContentDto;
import com.fathom.services.directory.models.DirectoryStream;
import com.fathom.services.directory.models.ServiceDirectory;
import com.fathom.services.directory.repository.DirectoryRepository;
import com.fathom.services.directory.service.DirectoryValidator;
import com.fathom.services.directory.service.InternalDirectoryService;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.util.stream.Collectors;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;

@WebMvcTest(controllers = DirectoryController.class)
class DirectoryControllerTest extends BaseIntegrationTest {

  private static final String INVALID_DIRECTORY_REQUEST =
      "directory/invalid/test_directory_with_duplicated_permissions.json";

  private static final String ENABLED_DIRECTORIES_REQUEST =
      "directory/enabled_service_response.json";

  @Test
  void testMappingDto() throws IOException, JSONException {
    final ServiceDirectory parsedContent =
        objectMapper.readValue(ASSET_DIRECTORY_RESOURCE.getFile(), ServiceDirectory.class);
    assertServiceDirectory(parsedContent, ASSET_DIRECTORY_RESOURCE);
    assertNotNull(parsedContent);
  }

  @Test
  void testMappingDtoWithStaticResources() throws IOException, JSONException {
    final ServiceDirectory parsedContent =
        objectMapper.readValue(WEP_APP_DIRECTORY_RESOURCE.getFile(), ServiceDirectory.class);
    assertServiceDirectory(parsedContent, WEP_APP_DIRECTORY_RESOURCE);
    assertNotNull(parsedContent);
    assertEquals(1, parsedContent.getStaticResources().size());
    assertTrue(parsedContent.isEnabled());
    assertEquals("Asset Widget", parsedContent.getStaticResources().get(0).getName());
    assertEquals(
        "Dashboard asset widget", parsedContent.getStaticResources().get(0).getDescription());
    assertEquals(
        "frn::wells-app:dashboard/asset", parsedContent.getStaticResources().get(0).getFrnFormat());
  }

  @Test
  void givenAlarmDirectoryWithStreamJson_whenJsonValid_thenMappingSuccessful()
      throws IOException, JSONException {
    final ServiceDirectory parsedContent =
        objectMapper.readValue(ALARM_DIRECTORY_RESOURCE.getFile(), ServiceDirectory.class);
    assertServiceDirectory(parsedContent, ALARM_DIRECTORY_RESOURCE);

    assertNotNull(parsedContent);
    assertEquals(1, parsedContent.getStream().size());
    final DirectoryStream alarmByAssetIdStream = parsedContent.getStream().get(0);
    assertEquals("/alarm/${assetId}", alarmByAssetIdStream.getTopic());
    assertArrayEquals(
        new String[] {"alarms:ViewAssetAlarm"}, alarmByAssetIdStream.getPermissions().toArray());
    assertEquals(1, alarmByAssetIdStream.getResourceVariables().size());
  }

  @Test
  void testGetAllDirectories_whenValidInput_thenReturns200() throws Exception {
    MvcResult mvcResult =
        mockMvc
            .perform(get("/directory").contentType(MediaType.APPLICATION_JSON))
            .andExpect(request().asyncStarted())
            .andExpect(request().asyncResult(notNullValue()))
            .andReturn();

    mockMvc
        .perform(asyncDispatch(mvcResult))
        .andDo(print())
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.content", hasSize(5)));
  }

  @Test
  void testGetProjectedServices_whenValidInput_thenReturns200() throws Exception {
    MvcResult mvcResult =
        mockMvc
            .perform(get("/directory/services").contentType(MediaType.APPLICATION_JSON))
            .andExpect(request().asyncStarted())
            .andExpect(request().asyncResult(notNullValue()))
            .andReturn();

    mockMvc
        .perform(asyncDispatch(mvcResult))
        .andDo(print())
        .andExpect(status().isOk())
        .andExpect(jsonPath("$", hasSize(4)));
  }

  @Test
  void testCreateDirectory_whenInvalidInput_thenReturns409() throws Exception {
    MvcResult mvcResult =
        mockMvc
            .perform(
                post("/directory")
                    .content(Files.readAllBytes(ASSET_DIRECTORY_RESOURCE.getFile().toPath()))
                    .contentType(MediaType.APPLICATION_JSON))
            .andExpect(request().asyncStarted())
            .andExpect(request().asyncResult(notNullValue()))
            .andReturn();

    mvcResult =
        mockMvc.perform(asyncDispatch(mvcResult)).andExpect(status().isConflict()).andReturn();

    assertEquals(
        DIR_ALREADY_EXIST_ERROR_MSG.formatted("assets"),
        mvcResult.getResponse().getContentAsString());
  }

  @Test
  void testUpdateDirectory_whenValidInput_thenReturns200() throws Exception {
    ServiceDirectory parsedContent =
        objectMapper.readValue(WEP_APP_DIRECTORY_RESOURCE.getFile(), ServiceDirectory.class);
    String expectedDescription = parsedContent.getDescription() + "!!!";
    parsedContent.setDescription(expectedDescription);

    MvcResult mvcResult =
        mockMvc
            .perform(
                put("/directory")
                    .content(objectMapper.writeValueAsString(parsedContent))
                    .contentType(MediaType.APPLICATION_JSON))
            .andExpect(request().asyncStarted())
            .andExpect(request().asyncResult(notNullValue()))
            .andReturn();

    mvcResult = mockMvc.perform(asyncDispatch(mvcResult)).andExpect(status().isOk()).andReturn();

    assertEquals(
        expectedDescription,
        objectMapper
            .readValue(mvcResult.getResponse().getContentAsString(), ServiceDirectory.class)
            .getDescription());
  }

  @Test
  void testUpdateDirectory_whenInvalidInput_thenReturns404() throws Exception {
    String badId = "___some_not_exiting_id___";
    ServiceDirectory parsedContent =
        objectMapper.readValue(WEP_APP_DIRECTORY_RESOURCE.getFile(), ServiceDirectory.class);
    parsedContent.setId(badId);

    MvcResult mvcResult =
        mockMvc
            .perform(
                put("/directory")
                    .content(objectMapper.writeValueAsString(parsedContent))
                    .contentType(MediaType.APPLICATION_JSON))
            .andExpect(request().asyncStarted())
            .andExpect(request().asyncResult(notNullValue()))
            .andReturn();

    mvcResult =
        mockMvc.perform(asyncDispatch(mvcResult)).andExpect(status().isNotFound()).andReturn();

    assertEquals(
        NO_DIR_WITH_ID_ERROR_MSG.formatted(badId), mvcResult.getResponse().getContentAsString());
  }

  @Test
  void testUpdateDirectory_whenDtoHasDuplicatedPermissions_thenReturn400() throws Exception {
    URL resource = this.getClass().getClassLoader().getResource(INVALID_DIRECTORY_REQUEST);
    ServiceDirectory request = objectMapper.readValue(resource, ServiceDirectory.class);
    mockMvc
        .perform(
            put("/directory")
                .content(objectMapper.writeValueAsString(request))
                .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  void testGetEnabledDirectories_whenDirectoryWithDependentExists_thenReturn200() throws Exception {
    MvcResult mvcResult =
        mockMvc
            .perform(get("/directory/enabled").contentType(MediaType.APPLICATION_JSON))
            .andExpect(request().asyncStarted())
            .andExpect(request().asyncResult(notNullValue()))
            .andReturn();

    mvcResult = mockMvc.perform(asyncDispatch(mvcResult)).andExpect(status().isOk()).andReturn();
    URL resource = this.getClass().getClassLoader().getResource(ENABLED_DIRECTORIES_REQUEST);
    InternalServicesContentDto response =
        objectMapper.readValue(resource, InternalServicesContentDto.class);
    String serializedJson = objectMapper.writeValueAsString(response);

    assertEquals(serializedJson, mvcResult.getResponse().getContentAsString());
  }

  private void assertServiceDirectory(ServiceDirectory parsedContent, Resource jsonResource)
      throws IOException, JSONException {
    final String serializedJson = objectMapper.writeValueAsString(parsedContent);
    final String expectedJson =
        Files.lines(jsonResource.getFile().toPath()).collect(Collectors.joining());
    JSONAssert.assertEquals(expectedJson, serializedJson, JSONCompareMode.LENIENT);
  }

  @TestConfiguration
  static class TestConfig {
    @Bean
    DirectoryValidator directoryValidator() {
      return new DirectoryValidator();
    }

    @Bean
    InternalDirectoryService internalDirectoryService(DirectoryRepository repository) {
      return new InternalDirectoryService(repository);
    }
  }
}
