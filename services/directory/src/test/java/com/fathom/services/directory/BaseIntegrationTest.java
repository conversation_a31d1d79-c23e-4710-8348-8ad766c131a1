package com.fathom.services.directory;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fathom.services.directory.models.ServiceDirectory;
import com.fathom.services.directory.repository.DirectoryRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.data.mongo.AutoConfigureDataMongo;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.context.WebApplicationContext;

@ExtendWith(SpringExtension.class)
@AutoConfigureDataMongo
@AutoConfigureWebMvc
@AutoConfigureMockMvc
@ActiveProfiles("component-test")
public abstract class BaseIntegrationTest {

  protected static final Resource ASSET_DIRECTORY_RESOURCE =
      new ClassPathResource("directory/test_asset_directory.json");
  protected static final Resource FAVORITES_DIRECTORY_RESOURCE =
      new ClassPathResource("directory/test_favorites_directory.json");
  protected static final Resource WEP_APP_DIRECTORY_RESOURCE =
      new ClassPathResource("directory/test_static_web_app_directory.json");
  protected static final Resource ALARM_DIRECTORY_RESOURCE =
      new ClassPathResource("directory/test_alarm_directory.json");
  protected static final Resource MAP_DIRECTORY_RESOURCE =
      new ClassPathResource("directory/test_map_directory.json");

  @Autowired protected ObjectMapper objectMapper;

  @Autowired private WebApplicationContext context;

  @Autowired protected MockMvc mockMvc;

  @Autowired private DirectoryRepository repository;

  @BeforeEach
  public void setup() throws Exception {
    MockitoAnnotations.initMocks(this);
    repository.deleteAll();
    repository.save(
        objectMapper.readValue(ASSET_DIRECTORY_RESOURCE.getFile(), ServiceDirectory.class));
    repository.save(
        objectMapper.readValue(FAVORITES_DIRECTORY_RESOURCE.getFile(), ServiceDirectory.class));
    repository.save(
        objectMapper.readValue(WEP_APP_DIRECTORY_RESOURCE.getFile(), ServiceDirectory.class));
    repository.save(
        objectMapper.readValue(ALARM_DIRECTORY_RESOURCE.getFile(), ServiceDirectory.class));
    repository.save(
        objectMapper.readValue(MAP_DIRECTORY_RESOURCE.getFile(), ServiceDirectory.class));

    assertEquals(5, repository.findAll().size());
  }
}
