{"id": "cbd8f792-f693-11ea-9367-37e575fc778f", "enabled": true, "endpointsOnly": false, "name": "assets", "serviceName": "assets", "serviceDisplayName": "Fathom Asset Service", "description": "Fathom Asset Manager Service", "frnFormat": "frn::assets:<org-id>:<resource-type>/<resource_name>", "frnRegex": "^frn::assets:.+", "permissions": {"read": [{"permission": "assets:ViewAsset", "description": "view asset details", "requiredResources": ["asset"]}, {"permission": "assets:ViewAssetPropertyValue", "description": "view asset property", "requiredResources": ["asset", "assetProperty"]}, {"permission": "assets:ViewTemplate", "description": "view template details", "requiredResources": ["template"]}, {"permission": "assets:ViewFormula", "description": "view formula details", "requiredResources": ["formula"]}], "write": [{"permission": "assets:CreateAsset", "description": "create a new asset", "requiredResources": []}, {"permission": "assets:DeleteAsset", "description": "delete asset", "requiredResources": ["asset"]}, {"permission": "assets:UpdateAsset", "description": "update asset", "requiredResources": ["asset"]}, {"permission": "assets:UpdateAssetPropertyValue", "description": "update asset property value", "requiredResources": ["asset", "assetProperty"]}, {"permission": "assets:CreateTemplate", "description": "create template", "requiredResources": []}, {"permission": "assets:UpdateTemplate", "description": "update template", "requiredResources": ["template"]}, {"permission": "assets:DeleteTemplate", "description": "delete template", "requiredResources": ["template"]}, {"permission": "assets:AddTemplateProperty", "description": "add a new template property", "requiredResources": ["template"]}, {"permission": "assets:CreateFormula", "description": "create formula", "requiredResources": []}, {"permission": "assets:UpdateFormula", "description": "update formula", "requiredResources": ["formula"]}, {"permission": "assets:DeleteFormula", "description": "delete formula", "requiredResources": ["formula"]}], "list": [{"permission": "assets:ListAssets", "description": "list assets", "requiredResources": []}, {"permission": "assets:ListTemplates", "description": "list templates", "requiredResources": []}, {"permission": "assets:ListFormulas", "description": "list formulas", "requiredResources": []}]}, "resources": {"asset": {"name": "<PERSON><PERSON>", "frnFormat": "frn::assets:<org-id>:asset/${asset_name:Asset Name}"}, "assetProperty": {"name": "Asset Property", "frnFormat": "frn::assets:<org-id>:asset/${asset_name:Asset Name}/property/${property_name:Property Name}"}, "template": {"name": "Template", "frnFormat": "frn::assets:<org-id>:template/${template_id:Template Id}"}, "formula": {"name": "Formula", "frnFormat": "frn::assets:<org-id>:formula/${formula_id:Formula Id}"}}, "endpoints": [{"url": "/asset", "methods": [{"method": "GET", "permissions": ["assets:ListAssets"]}, {"method": "POST", "permissions": ["assets:CreateAsset"]}]}, {"url": "/asset/${assetName}", "methods": [{"method": "GET", "permissions": ["assets:ViewAsset"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "asset_name"}], "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "action": "allow", "fields": ["frameId", "prevFrameId", "data"]}]}]}, {"method": "PUT", "permissions": ["assets:UpdateAsset"]}, {"method": "DELETE", "permissions": ["assets:DeleteAsset"]}]}, {"url": "/asset/${assetName}/property", "methods": [{"method": "PUT", "permissions": ["assets:UpdateAssetPropertyValue"], "resourceVariables": [{"origin": "body", "value": "$.newAssetProperty", "resourceVariable": "property_name"}, {"origin": "path", "value": "2", "resourceVariable": "asset_name"}]}]}, {"url": "/formula", "methods": [{"method": "GET", "permissions": ["assets:ListFormulas"]}, {"method": "POST", "permissions": ["assets:CreateFormula"]}]}, {"url": "/formula/${formulaId}", "methods": [{"method": "GET", "permissions": ["assets:ViewFormula"]}, {"method": "PUT", "permissions": ["assets:UpdateFormula"]}, {"method": "DELETE", "permissions": ["assets:DeleteFormula"]}]}, {"url": "/template", "methods": [{"method": "GET", "permissions": ["assets:ListTemplates"]}, {"method": "POST", "permissions": ["assets:CreateTemplate"]}]}, {"url": "/template/${templateId}", "methods": [{"method": "GET", "permissions": ["assets:ViewTemplate"]}, {"method": "PUT", "permissions": ["assets:UpdateTemplate"]}, {"method": "DELETE", "permissions": ["assets:DeleteTemplate"]}]}], "stream": []}