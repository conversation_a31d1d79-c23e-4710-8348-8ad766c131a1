{"content": [{"name": "favorites", "frnFormatByPermission": {"favorites:ListFavoriteGroups": "frn::favorites:<org-id>:/favorites", "favorites:DeleteFavoriteGroup": "frn::favorites:<org-id>:/favorites/${group_id:Group Id}", "favorites:UpdateFavoriteGroup": "frn::favorites:<org-id>:/favorites/${group_id:Group Id}", "favorites:ViewAssetNames": "frn::favorites:<org-id>:/favorites/${group_id:Group Id}"}, "endpointByUrl": {"/favorites/groups/search": {"url": "/favorites/groups/search", "methods": [{"method": "GET", "permissions": ["favorites:ListFavoriteGroups"], "resourceVariables": null, "postAuthorization": null}]}, "/favorites/groups/${group_id}": {"url": "/favorites/groups/${group_id}", "methods": [{"method": "PUT", "permissions": ["favorites:UpdateFavoriteGroup"], "resourceVariables": null, "postAuthorization": null}, {"method": "DELETE", "permissions": ["favorites:DeleteFavoriteGroup"], "resourceVariables": null, "postAuthorization": null}]}, "/favorites/groups/{group_id}/assets/names": {"url": "/favorites/groups/{group_id}/assets/names", "methods": [{"method": "GET", "permissions": ["favorites:ViewAssetNames"], "resourceVariables": null, "postAuthorization": null}]}, "/favorites/groups": {"url": "/favorites/groups", "methods": [{"method": "GET", "permissions": ["favorites:ListFavoriteGroups"], "resourceVariables": null, "postAuthorization": null}]}, "/favorites/groups}": {"url": "/favorites/groups}", "methods": [{"method": "POST", "permissions": ["favorites:CreateFavoriteGroup"], "resourceVariables": null, "postAuthorization": null}]}}, "streamByTopicName": {}}, {"name": "assets", "frnFormatByPermission": {"assets:UpdateFormula": "frn::assets:<org-id>:formula/${formula_id:Formula Id}", "assets:DeleteAsset": "frn::assets:<org-id>:asset/${asset_name:Asset Name}", "assets:AddTemplateProperty": "frn::assets:<org-id>:template/${template_id:Template Id}", "assets:ViewAsset": "frn::assets:<org-id>:asset/${asset_name:Asset Name}", "assets:ViewFormula": "frn::assets:<org-id>:formula/${formula_id:Formula Id}", "assets:DeleteTemplate": "frn::assets:<org-id>:template/${template_id:Template Id}", "assets:UpdateAssetPropertyValue": "frn::assets:<org-id>:asset/${asset_name:Asset Name}", "assets:DeleteFormula": "frn::assets:<org-id>:formula/${formula_id:Formula Id}", "assets:ViewAssetPropertyValue": "frn::assets:<org-id>:asset/${asset_name:Asset Name}", "assets:UpdateAsset": "frn::assets:<org-id>:asset/${asset_name:Asset Name}", "assets:ViewTemplate": "frn::assets:<org-id>:template/${template_id:Template Id}", "assets:UpdateTemplate": "frn::assets:<org-id>:template/${template_id:Template Id}"}, "endpointByUrl": {"/template/${templateId}": {"url": "/template/${templateId}", "methods": [{"method": "GET", "permissions": ["assets:ViewTemplate"], "resourceVariables": null, "postAuthorization": null}, {"method": "PUT", "permissions": ["assets:UpdateTemplate"], "resourceVariables": null, "postAuthorization": null}, {"method": "DELETE", "permissions": ["assets:DeleteTemplate"], "resourceVariables": null, "postAuthorization": null}]}, "/asset": {"url": "/asset", "methods": [{"method": "GET", "permissions": ["assets:ListAssets"], "resourceVariables": null, "postAuthorization": null}, {"method": "POST", "permissions": ["assets:CreateAsset"], "resourceVariables": null, "postAuthorization": null}]}, "/formula": {"url": "/formula", "methods": [{"method": "GET", "permissions": ["assets:ListFormulas"], "resourceVariables": null, "postAuthorization": null}, {"method": "POST", "permissions": ["assets:CreateFormula"], "resourceVariables": null, "postAuthorization": null}]}, "/template": {"url": "/template", "methods": [{"method": "GET", "permissions": ["assets:ListTemplates"], "resourceVariables": null, "postAuthorization": null}, {"method": "POST", "permissions": ["assets:CreateTemplate"], "resourceVariables": null, "postAuthorization": null}]}, "/formula/${formulaId}": {"url": "/formula/${formulaId}", "methods": [{"method": "GET", "permissions": ["assets:ViewFormula"], "resourceVariables": null, "postAuthorization": null}, {"method": "PUT", "permissions": ["assets:UpdateFormula"], "resourceVariables": null, "postAuthorization": null}, {"method": "DELETE", "permissions": ["assets:DeleteFormula"], "resourceVariables": null, "postAuthorization": null}]}, "/asset/${assetName}/property": {"url": "/asset/${assetName}/property", "methods": [{"method": "PUT", "permissions": ["assets:UpdateAssetPropertyValue"], "resourceVariables": [{"origin": "body", "value": "$.newAssetProperty", "resourceVariable": "property_name"}, {"origin": "path", "value": "2", "resourceVariable": "asset_name"}], "postAuthorization": null}]}, "/asset/${assetName}": {"url": "/asset/${assetName}", "methods": [{"method": "GET", "permissions": ["assets:ViewAsset"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "asset_name"}], "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "action": "allow", "fields": ["data", "frameId", "prevFrameId"]}]}]}, {"method": "PUT", "permissions": ["assets:UpdateAsset"], "resourceVariables": null, "postAuthorization": null}, {"method": "DELETE", "permissions": ["assets:DeleteAsset"], "resourceVariables": null, "postAuthorization": null}]}}, "streamByTopicName": {}}, {"name": "alarms", "frnFormatByPermission": {"alarms:ViewAssetAlarm": "frn::alarms:<org-id>:/alarm/${asset_id:Asset Id}"}, "endpointByUrl": {}, "streamByTopicName": {"/alarm/${assetId}": {"topic": "/alarm/${assetId}", "permissions": ["alarms:ViewAssetAlarm"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "asset_id"}]}}}, {"name": "wells-app", "frnFormatByPermission": {}, "endpointByUrl": {}, "streamByTopicName": {}}, {"name": "map", "frnFormatByPermission": {"assets:UpdateFormula": "frn::assets:<org-id>:formula/${formula_id:Formula Id}", "assets:DeleteAsset": "frn::assets:<org-id>:asset/${asset_name:Asset Name}", "assets:AddTemplateProperty": "frn::assets:<org-id>:template/${template_id:Template Id}", "assets:ViewAsset": "frn::assets:<org-id>:asset/${asset_name:Asset Name}", "assets:DeleteTemplate": "frn::assets:<org-id>:template/${template_id:Template Id}", "assets:UpdateAssetPropertyValue": "frn::assets:<org-id>:asset/${asset_name:Asset Name}", "assets:ViewAssetPropertyValue": "frn::assets:<org-id>:asset/${asset_name:Asset Name}", "assets:UpdateAsset": "frn::assets:<org-id>:asset/${asset_name:Asset Name}", "map:ViewMap": "frn::map:<org-id>:/mainmap", "assets:UpdateTemplate": "frn::assets:<org-id>:template/${template_id:Template Id}", "assets:ViewFormula": "frn::assets:<org-id>:formula/${formula_id:Formula Id}", "assets:DeleteFormula": "frn::assets:<org-id>:formula/${formula_id:Formula Id}", "assets:ViewTemplate": "frn::assets:<org-id>:template/${template_id:Template Id}"}, "endpointByUrl": {"/map": {"url": "/map", "methods": [{"method": "GET", "permissions": [], "resourceVariables": null, "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "action": null, "fields": ["*"]}]}]}]}}, "streamByTopicName": {}}]}