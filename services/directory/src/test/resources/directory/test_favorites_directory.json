{"id": "pwf8f792-f693-11ea-9367-37e575fc557y", "enabled": true, "endpointsOnly": false, "name": "favorites", "serviceName": "favorites", "serviceDisplayName": "Fathom Favorite Asset Groups Service", "description": "Fathom Favorite Asset Groups Service", "frnFormat": "frn::favorites:<org-id>:/<resource-type>/<resource_name>", "frnRegex": "^frn::favorites:.+", "permissions": {"read": [{"permission": "favorites:ViewAssetNames", "description": "view asset names in the favorite group", "requiredResources": ["favoriteGroup"]}], "write": [{"permission": "favorites:CreateFavoriteGroup", "description": "create a new favoriteGroup", "requiredResources": []}, {"permission": "favorites:DeleteFavoriteGroup", "description": "delete favoriteGroup", "requiredResources": ["favoriteGroup"]}, {"permission": "favorites:UpdateFavoriteGroup", "description": "update favoriteGroup", "requiredResources": ["favoriteGroup"]}], "list": [{"permission": "favorites:ListFavoriteGroups", "description": "list favorite groups", "requiredResources": ["favoriteGroups"]}]}, "resources": {"favoriteGroups": {"name": "Favorite Groups", "frnFormat": "frn::favorites:<org-id>:/favorites"}, "favoriteGroup": {"name": "Favorite Group", "frnFormat": "frn::favorites:<org-id>:/favorites/${group_id:Group Id}"}}, "endpoints": [{"url": "/favorites/groups/{group_id}/assets/names", "methods": [{"method": "GET", "permissions": ["favorites:ViewAssetNames"]}]}, {"url": "/favorites/groups/search", "methods": [{"method": "GET", "permissions": ["favorites:ListFavoriteGroups"]}]}, {"url": "/favorites/groups", "methods": [{"method": "GET", "permissions": ["favorites:ListFavoriteGroups"]}]}, {"url": "/favorites/groups}", "methods": [{"method": "POST", "permissions": ["favorites:CreateFavoriteGroup"]}]}, {"url": "/favorites/groups/${group_id}", "methods": [{"method": "PUT", "permissions": ["favorites:UpdateFavoriteGroup"]}, {"method": "DELETE", "permissions": ["favorites:DeleteFavoriteGroup"]}]}], "stream": []}