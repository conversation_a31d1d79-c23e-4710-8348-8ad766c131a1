{"id": "poj8f792-f693-11ea-9367-37e575fc853f", "enabled": true, "endpointsOnly": false, "name": "wells-app", "serviceName": "wells-app", "serviceDisplayName": "Fathom Wells App", "description": "Fathom Wells Web App", "frnFormat": "frn::wells-app:<org-id>:<resource-type>", "frnRegex": "^frn::wells-app:.+", "permissions": {"read": [{"permission": "assets:ViewAssetWidget", "description": "view asset widget", "requiredResources": []}]}, "staticResources": [{"name": "<PERSON><PERSON> Widget", "description": "Dashboard asset widget", "frnFormat": "frn::wells-app:dashboard/asset"}], "stream": []}