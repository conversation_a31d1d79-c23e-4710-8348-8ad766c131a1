{"id": "ec2d56e6-3e0a-11eb-a915-3f8458462f2b", "enabled": true, "endpointsOnly": false, "name": "alarms", "serviceName": "alarms", "serviceDisplayName": "Fathom Alarms Service", "description": "Fathom Alarms Service", "frnFormat": "frn::alarms:<org-id>:/<resource-type>/<resource_name>", "frnRegex": "^frn::alarms:.+", "permissions": {"read": [{"permission": "alarms:ViewAssetAlarm", "description": "view asset alarm", "requiredResources": ["assetAlarm"]}], "write": [{"permission": "alarms:ViewAssetAlarm", "description": "view asset alarm", "requiredResources": ["assetAlarm"]}], "list": []}, "resources": {"assetAlarm": {"name": "AssetAlarm", "frnFormat": "frn::alarms:<org-id>:/alarm/${asset_id:Asset Id}"}}, "endpoints": [], "stream": [{"topic": "/alarm/${assetId}", "permissions": ["alarms:ViewAssetAlarm"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "asset_id"}]}], "staticResources": null}