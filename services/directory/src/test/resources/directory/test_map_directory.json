{"enabled": true, "visible": false, "endpointsOnly": true, "name": "map", "serviceName": "map", "dependentServices": ["assets"], "permissions": {"read": [{"permission": "map:ViewMap", "description": "view map", "requiredResources": ["map"]}], "list": []}, "resources": {"map": {"name": "Map", "frnFormat": "frn::map:<org-id>:/mainmap"}}, "endpoints": [{"url": "/map", "methods": [{"method": "GET", "permissions": [], "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}], "stream": null, "staticResources": null}