package com.fathom.services.directory.service;

import com.fathom.services.directory.dto.internal.InternalServiceDirectoryDto;
import com.fathom.services.directory.models.DirectoryStream;
import com.fathom.services.directory.models.ServiceDirectory;
import com.fathom.services.directory.models.ServiceEndpoint;
import com.fathom.services.directory.models.ServicePermission;
import com.fathom.services.directory.models.ServiceResource;
import com.fathom.services.directory.repository.DirectoryRepository;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

@Service
public class InternalDirectoryService {

  private final DirectoryRepository repository;

  public InternalDirectoryService(DirectoryRepository repository) {
    this.repository = repository;
  }

  public Set<InternalServiceDirectoryDto> getEnabledServices() {
    List<ServiceDirectory> services = repository.findAll();
    return services.stream()
        .filter(ServiceDirectory::isEnabled)
        .map(s -> map(s, services))
        .collect(Collectors.toSet());
  }

  private InternalServiceDirectoryDto map(
      ServiceDirectory serviceDirectory, List<ServiceDirectory> services) {
    InternalServiceDirectoryDto dto = new InternalServiceDirectoryDto();
    dto.setName(serviceDirectory.getName());

    Map<String, ServiceEndpoint> endpointByUrl =
        serviceDirectory.getEndpoints().stream()
            .collect(Collectors.toMap(ServiceEndpoint::getUrl, Function.identity()));
    Map<String, DirectoryStream> streamByTopic =
        serviceDirectory.getStream().stream()
            .collect(Collectors.toMap(DirectoryStream::getTopic, Function.identity()));

    dto.setEndpointByUrl(endpointByUrl);
    dto.setStreamByTopicName(streamByTopic);
    dto.setFrnFormatByPermission(getFrnFormatsByPermission(serviceDirectory));

    if (serviceDirectory.hasDependentServices()) {
      Map<String, ServiceDirectory> serviceByName =
          services.stream()
              .collect(Collectors.toMap(ServiceDirectory::getName, Function.identity()));

      for (String dependentService : serviceDirectory.getDependentServices()) {
        ServiceDirectory dependentDirectory = serviceByName.get(dependentService);
        if (dependentDirectory != null) {
          dto.appendFrnFormatsByPermission(getFrnFormatsByPermission(dependentDirectory));
        }
      }
    }
    return dto;
  }

  private Map<String, String> getFrnFormatsByPermission(ServiceDirectory directory) {
    Map<String, String> frnFormatByPermission = new HashMap<>();
    for (ServicePermission permission : directory.getCombinedPermissions()) {
      permission
          .getRequiredResourceName()
          .map(name -> directory.getResources().get(name))
          .map(ServiceResource::getFrnFormat)
          .ifPresent(frnFormat -> frnFormatByPermission.put(permission.getPermission(), frnFormat));
    }
    return frnFormatByPermission;
  }
}
