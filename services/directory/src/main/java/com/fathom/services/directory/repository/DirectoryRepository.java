package com.fathom.services.directory.repository;

import com.fathom.services.directory.models.ServiceDirectory;
import java.util.List;
import java.util.Optional;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DirectoryRepository extends MongoRepository<ServiceDirectory, String> {

  @Query(
      value = "{'endpointsOnly': false}",
      fields = "{'permissions':0, 'endpoints':0, 'stream':0, 'dependentServices':0}")
  List<ServiceDirectory> findAllProjected();

  Optional<ServiceDirectory> findByName(String name);
}
