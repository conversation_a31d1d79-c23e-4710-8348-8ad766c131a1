package com.fathom.services.directory.controller;

import com.fathom.services.directory.dto.DirectoryContentDto;
import com.fathom.services.directory.dto.internal.InternalServicesContentDto;
import com.fathom.services.directory.models.ServiceDirectory;
import com.fathom.services.directory.repository.DirectoryRepository;
import com.fathom.services.directory.service.DirectoryValidator;
import com.fathom.services.directory.service.InternalDirectoryService;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;

@CrossOrigin(origins = "*")
@RestController
// @ExposesResourceFor(ServiceDirectory.class)
@RequestMapping(value = "/directory", produces = "application/json")
public class DirectoryController {

  static final String NO_DIR_WITH_ID_ERROR_MSG =
      "There is no service directory with \"id\": \"%s\"";
  static final String DIR_ALREADY_EXIST_ERROR_MSG = "The '%s' service directory is already exist!";
  @Autowired private DirectoryRepository directoryRepository;

  @Autowired private DirectoryValidator directoryValidator;

  @Autowired private InternalDirectoryService service;

  @Async("asyncExecutor")
  @GetMapping
  public CompletableFuture<ResponseEntity<DirectoryContentDto>> findAll() {
    List<ServiceDirectory> all = directoryRepository.findAll();
    return CompletableFuture.completedFuture(
        new ResponseEntity<>(new DirectoryContentDto(all), HttpStatus.OK));
  }

  @Async("asyncExecutor")
  @GetMapping("/services")
  public CompletableFuture<ResponseEntity<List<ServiceDirectory>>> findProjectedServices() {

    List<ServiceDirectory> serviceDirectories = directoryRepository.findAllProjected();

    if (serviceDirectories.isEmpty()) {
      return CompletableFuture.completedFuture(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }
    return CompletableFuture.completedFuture(
        new ResponseEntity<>(serviceDirectories, HttpStatus.OK));
  }

  @Async("asyncExecutor")
  @GetMapping("/services/{id}/actions")
  public CompletableFuture<ResponseEntity<Object>> findServiceActions(@PathVariable String id) {
    Optional<ServiceDirectory> service = directoryRepository.findById(id);

    if (service.isPresent()) {
      return CompletableFuture.completedFuture(
          new ResponseEntity<>(service.get().getPermissions(), HttpStatus.OK));
    } else {
      return CompletableFuture.completedFuture(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }
  }

  @Async("asyncExecutor")
  @PostMapping(consumes = "application/json")
  public CompletableFuture<ResponseEntity<?>> createServiceDirectory(
      @RequestBody ServiceDirectory serviceDirectory) {
    if (directoryRepository.findByName(serviceDirectory.getName()).isPresent()) {
      return CompletableFuture.completedFuture(
          new ResponseEntity<>(
              DIR_ALREADY_EXIST_ERROR_MSG.formatted(serviceDirectory.getName()),
              HttpStatus.CONFLICT));
    } else {
      directoryValidator.validate(serviceDirectory);
      serviceDirectory.setId(UUID.randomUUID().toString());
      ServiceDirectory createdServiceDirectory = directoryRepository.save(serviceDirectory);
      return CompletableFuture.completedFuture(
          new ResponseEntity<>(createdServiceDirectory, HttpStatus.CREATED));
    }
  }

  @Async("asyncExecutor")
  @PutMapping(consumes = "application/json")
  public CompletableFuture<ResponseEntity<?>> updateServiceDirectory(
      @RequestBody ServiceDirectory updatedServiceDirectory) {
    String id = updatedServiceDirectory.getId();
    directoryValidator.validate(updatedServiceDirectory);
    if (directoryRepository.findById(id).isPresent()) {
      directoryRepository.save(updatedServiceDirectory);
      return CompletableFuture.completedFuture(
          new ResponseEntity<>(directoryRepository.findById(id), HttpStatus.OK));
    }

    return CompletableFuture.completedFuture(
        new ResponseEntity<>(NO_DIR_WITH_ID_ERROR_MSG.formatted(id), HttpStatus.NOT_FOUND));
  }

  @Async("asyncExecutor")
  @DeleteMapping("/{id}")
  public CompletableFuture<ResponseEntity<Void>> deleteServiceDirectory(@PathVariable String id) {
    directoryRepository.deleteById(id);
    boolean wasDeleted = true;
    HttpStatus responseStatus = wasDeleted ? HttpStatus.NO_CONTENT : HttpStatus.NOT_FOUND;

    return CompletableFuture.completedFuture(new ResponseEntity<>(responseStatus));
  }

  @Async("asyncExecutor")
  @GetMapping("/enabled")
  public CompletableFuture<ResponseEntity<InternalServicesContentDto>> getEnabledService() {
    InternalServicesContentDto body = new InternalServicesContentDto();
    body.setContent(service.getEnabledServices());
    return CompletableFuture.completedFuture(ResponseEntity.ok(body));
  }
}
