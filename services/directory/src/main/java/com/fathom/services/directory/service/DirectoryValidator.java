package com.fathom.services.directory.service;

import com.fathom.services.directory.models.ServiceDirectory;
import com.fathom.services.directory.models.ServiceEndpoint;
import com.fathom.services.directory.models.ServicePermission;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class DirectoryValidator {

  public void validate(ServiceDirectory serviceDirectory) {
    Set<String> dependentServices = serviceDirectory.getDependentServices();
    if (dependentServices != null && dependentServices.contains(serviceDirectory.getName())) {
      throw new ValidationException("Service directory cannot depend on itself");
    }
    validatePermissions(serviceDirectory);
    validateEndpointsUrls(serviceDirectory);
  }

  private void validatePermissions(ServiceDirectory serviceDirectory) {
    List<String> temp =
        serviceDirectory.getPermissions().values().stream()
            .map(Arrays::asList)
            .flatMap(Collection::stream)
            .map(ServicePermission::getPermission)
            .collect(Collectors.toList());
    validateDuplications(temp);
  }

  private void validateEndpointsUrls(ServiceDirectory serviceDirectory) {
    List<String> endpoints =
        serviceDirectory.getEndpoints().stream()
            .map(ServiceEndpoint::getUrl)
            .collect(Collectors.toList());
    validateDuplications(endpoints);
  }

  private void validateDuplications(List<String> source) {
    Set<String> temp = new HashSet<>();
    source.forEach(
        permissionName -> {
          if (!temp.add(permissionName)) {
            throw new ValidationException(
                "Duplicated resource [%s] within the service".formatted(permissionName));
          }
        });
  }
}
