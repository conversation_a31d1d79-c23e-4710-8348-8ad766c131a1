package com.fathom.services.directory.models.post_authorization;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum FieldProtectionAction {
  ALLOW,
  DENY;

  @JsonCreator
  public static FieldProtectionAction fromValue(String value) {
    if (value == null) {
      return DENY;
    }
    try {
      return FieldProtectionAction.valueOf(value.toUpperCase());
    } catch (IllegalArgumentException e) {
      return DENY;
    }
  }

  @JsonValue
  public String getValue() {
    return this.name().toLowerCase();
  }
}
