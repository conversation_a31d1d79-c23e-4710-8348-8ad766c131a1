package com.fathom.services.directory.repository;

import java.util.UUID;
import org.springframework.stereotype.Component;

// @Component annotation allows our Spring boot application to recognize (through component
// scanning)
// our generator class as an injectable component. This will allow us to use the @Autowired
// annotation in other classes
// and have our component injected without having to create the generator using the new operator

@Component
public class IdGenerator {

  public UUID getNextId() {
    return UUID.randomUUID();
  }
}
