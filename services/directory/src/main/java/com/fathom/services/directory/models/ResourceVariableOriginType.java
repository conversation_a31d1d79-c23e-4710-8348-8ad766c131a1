package com.fathom.services.directory.models;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum ResourceVariableOriginType {
  HEADER,
  BODY,
  PATH,
  QUERY;

  @JsonCreator
  public static ResourceVariableOriginType fromValue(String s) {
    return ResourceVariableOriginType.valueOf(s.toUpperCase());
  }

  @JsonValue
  public String getValue() {
    return this.name().toLowerCase();
  }
}
