package com.fathom.services.directory.models;

import java.util.List;

public class UnitResponseReference {
  private Type type;
  private String jsonPath;
  private List<DataConfig> data;

  public enum Type {
    ARRAY,
    MAP,
    OBJECT
  }

  public Type getType() {
    return type;
  }

  public void setType(Type type) {
    this.type = type;
  }

  public String getJsonPath() {
    return jsonPath;
  }

  public void setJsonPath(String jsonPath) {
    this.jsonPath = jsonPath;
  }

  public List<DataConfig> getData() {
    return data;
  }

  public void setData(List<DataConfig> data) {
    this.data = data;
  }
}
