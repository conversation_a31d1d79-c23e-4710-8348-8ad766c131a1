package com.fathom.services.directory.models.post_authorization;

import java.util.Set;

public class FieldsProtector {
  private MaskType maskType;
  private FieldProtectionAction action;
  private Set<String> fields;

  public MaskType getMaskType() {
    return maskType;
  }

  public void setMaskType(MaskType maskType) {
    this.maskType = maskType;
  }

  public Set<String> getFields() {
    return fields;
  }

  public void setFields(Set<String> fields) {
    this.fields = fields;
  }

  public FieldProtectionAction getAction() {
    return action;
  }

  public void setAction(FieldProtectionAction action) {
    this.action = action;
  }
}
