package com.fathom.services.directory.models;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.Arrays;
import java.util.Optional;

public class ServicePermission {
  private String permission;
  private String description;
  private String[] requiredResources;

  public String[] getRequiredResources() {
    return requiredResources;
  }

  public void setRequiredResources(String[] requiredResources) {
    this.requiredResources = requiredResources;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public String getPermission() {
    return permission;
  }

  public void setPermission(String permission) {
    this.permission = permission;
  }

  @JsonIgnore
  // returns first required resource for permission, there is supposed to be only one permission not
  // array
  // change it when agreement with UI side is done
  public Optional<String> getRequiredResourceName() {
    return Optional.ofNullable(requiredResources)
        .filter(r -> r.length > 0)
        .map(Arrays::asList)
        .flatMap(l -> l.stream().findFirst());
  }
}
