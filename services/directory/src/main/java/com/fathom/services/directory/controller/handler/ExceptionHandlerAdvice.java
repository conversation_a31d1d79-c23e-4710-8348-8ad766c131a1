package com.fathom.services.directory.controller.handler;

import com.fathom.services.directory.dto.ErrorDto;
import com.fathom.services.directory.service.ValidationException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class ExceptionHandlerAdvice {

  private static final Logger logger = LogManager.getLogger(ExceptionHandlerAdvice.class);

  @ExceptionHandler(ValidationException.class)
  @ResponseStatus(HttpStatus.BAD_REQUEST)
  ErrorDto handleValidationException(ValidationException e) {
    logger.error("Validation exception ", e);
    return new ErrorDto(e.getMessage());
  }

  @ExceptionHandler(Exception.class)
  @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
  public Exception handleAllExceptions(Exception e) {
    logger.error(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase(), e);
    return e;
  }
}
