package com.fathom.services.directory;

import com.fathom.diagnostics.configuration.DiagnosticProperties;
import com.fathom.services.directory.repository.DirectoryRepository;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.hateoas.config.EnableHypermediaSupport;
import org.springframework.hateoas.config.EnableHypermediaSupport.HypermediaType;

@EnableHypermediaSupport(type = HypermediaType.HAL)
@SpringBootApplication
@EnableDiscoveryClient
@EnableMongoRepositories(basePackageClasses = DirectoryRepository.class)
@EnableConfigurationProperties(DiagnosticProperties.class)
@ComponentScan(basePackages = {
        "com.fathom.services.directory",
        "com.fathom.diagnostics"
})
public class App {
  public static void main(String[] args) {
    SpringApplication.run(App.class, args);
  }
}
