package com.fathom.services.directory.models.post_authorization;

import java.util.List;

public class PostAuthorizationAction {
  private String permission;
  private List<FieldsProtector> protectors;

  public String getPermission() {
    return permission;
  }

  public void setPermission(String permission) {
    this.permission = permission;
  }

  public List<FieldsProtector> getProtectors() {
    return protectors;
  }

  public void setProtectors(List<FieldsProtector> protectors) {
    this.protectors = protectors;
  }
}
