package com.fathom.services.directory.dto.internal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fathom.services.directory.models.DirectoryStream;
import com.fathom.services.directory.models.ServiceEndpoint;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class InternalServiceDirectoryDto {

  private String name;
  private Map<String, String> frnFormatByPermission = new HashMap<>();
  private Map<String, ServiceEndpoint> endpointByUrl = new HashMap<>();
  
  private Map<String, DirectoryStream> streamByTopicName = new HashMap<>();


  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public Map<String, String> getFrnFormatByPermission() {
    return frnFormatByPermission;
  }

  public void setFrnFormatByPermission(Map<String, String> frnFormatByPermission) {
    this.frnFormatByPermission = frnFormatByPermission;
  }

  public Map<String, ServiceEndpoint> getEndpointByUrl() {
    return endpointByUrl;
  }

  public void setEndpointByUrl(Map<String, ServiceEndpoint> endpointByUrl) {
    this.endpointByUrl = endpointByUrl;
  }

  public Map<String, DirectoryStream> getStreamByTopicName() {
    return streamByTopicName;
  }

  public void setStreamByTopicName(Map<String, DirectoryStream> streamByTopicName) {
    this.streamByTopicName = streamByTopicName;
  }

  @JsonIgnore
  public void appendFrnFormatsByPermission(Map<String, String> frnFormatByPermission) {
    this.frnFormatByPermission.putAll(frnFormatByPermission);
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    InternalServiceDirectoryDto that = (InternalServiceDirectoryDto) o;
    return Objects.equals(name, that.name);
  }

  @Override
  public int hashCode() {
    return Objects.hash(name);
  }
}
