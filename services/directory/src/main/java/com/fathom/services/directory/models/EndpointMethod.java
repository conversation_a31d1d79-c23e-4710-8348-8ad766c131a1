package com.fathom.services.directory.models;

import com.fathom.services.directory.models.post_authorization.PostAuthorizationAction;
import java.util.List;
import java.util.Set;

public class EndpointMethod {
  private String method;
  private Set<String> permissions;
  private UnitResponseReference unitConversion;
  private List<ResourceVariableDefinition> resourceVariables;
  private List<PostAuthorizationAction> postAuthorization;

  public String getMethod() {
    return method;
  }

  public void setMethod(String method) {
    this.method = method;
  }

  public Set<String> getPermissions() {
    return permissions;
  }

  public void setPermissions(Set<String> permissions) {
    this.permissions = permissions;
  }

  public List<ResourceVariableDefinition> getResourceVariables() {
    return resourceVariables;
  }

  public void setResourceVariables(List<ResourceVariableDefinition> resourceVariables) {
    this.resourceVariables = resourceVariables;
  }

  public List<PostAuthorizationAction> getPostAuthorization() {
    return postAuthorization;
  }

  public void setPostAuthorization(List<PostAuthorizationAction> postAuthorization) {
    this.postAuthorization = postAuthorization;
  }

  public UnitResponseReference getUnitConversion() {
    return unitConversion;
  }

  public void setUnitConversion(UnitResponseReference unitConversion) {
    this.unitConversion = unitConversion;
  }
}
