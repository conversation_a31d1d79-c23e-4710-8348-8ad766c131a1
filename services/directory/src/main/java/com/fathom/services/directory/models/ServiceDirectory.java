package com.fathom.services.directory.models;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class ServiceDirectory {
  private String id;
  private boolean enabled;
  // if false - service is meta service, not configurable from UI
  private boolean endpointsOnly;
  private String name;
  private String serviceName;
  private String serviceDisplayName;
  private String description;
  private String frnFormat;
  private String frnRegex;
  private Set<String> dependentServices;
  private Map<String, ServicePermission[]> permissions;
  private Map<String, ServiceResource> resources;
  private List<ServiceEndpoint> endpoints;
  private List<DirectoryStream> stream;
  private List<StaticResource> staticResources;

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public boolean isEnabled() {
    return enabled;
  }

  public void setEnabled(boolean enabled) {
    this.enabled = enabled;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getServiceName() {
    return serviceName;
  }

  public void setServiceName(String serviceName) {
    this.serviceName = serviceName;
  }

  public String getServiceDisplayName() {
    return serviceDisplayName;
  }

  public void setServiceDisplayName(String serviceDisplayName) {
    this.serviceDisplayName = serviceDisplayName;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public String getFrnFormat() {
    return frnFormat;
  }

  public void setFrnFormat(String frnFormat) {
    this.frnFormat = frnFormat;
  }

  public String getFrnRegex() {
    return frnRegex;
  }

  public void setFrnRegex(String frnRegex) {
    this.frnRegex = frnRegex;
  }

  public Map<String, ServicePermission[]> getPermissions() {
    return permissions;
  }

  public void setPermissions(Map<String, ServicePermission[]> permissions) {
    this.permissions = permissions;
  }

  public Map<String, ServiceResource> getResources() {
    return resources;
  }

  public void setResources(Map<String, ServiceResource> resources) {
    this.resources = resources;
  }

  public List<ServiceEndpoint> getEndpoints() {
    if (endpoints == null) {
      return new ArrayList<>();
    }
    return endpoints;
  }

  public void setEndpoints(List<ServiceEndpoint> endpoints) {
    this.endpoints = endpoints;
  }

  public List<StaticResource> getStaticResources() {
    return staticResources;
  }

  public void setStaticResources(List<StaticResource> staticResources) {
    this.staticResources = staticResources;
  }

  public List<DirectoryStream> getStream() {
    if (stream == null) {
      return new ArrayList<>();
    }
    return stream;
  }

  public void setStream(List<DirectoryStream> stream) {
    this.stream = stream;
  }

  public boolean isEndpointsOnly() {
    return endpointsOnly;
  }

  public void setEndpointsOnly(boolean endpointsOnly) {
    this.endpointsOnly = endpointsOnly;
  }

  public Set<String> getDependentServices() {
    return dependentServices;
  }

  public void setDependentServices(Set<String> dependentServices) {
    this.dependentServices = dependentServices;
  }

  @JsonIgnore
  public List<ServicePermission> getCombinedPermissions() {
    if (this.permissions == null || this.permissions.isEmpty()) return new ArrayList<>();
    return this.getPermissions().values().stream()
        .map(Arrays::asList)
        .flatMap(Collection::stream)
        .collect(Collectors.toList());
  }

  @JsonIgnore
  public boolean hasDependentServices() {
    return this.dependentServices != null && !this.dependentServices.isEmpty();
  }
}
