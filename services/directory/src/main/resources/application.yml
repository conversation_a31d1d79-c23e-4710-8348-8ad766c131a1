server:
  port: ${DIRECTORY_SERVICE_PORT:8086}
spring:
  datasource:
    hikari:
      connectionTimeout: 20000
      maximumPoolSize: 5
  jpa:
    show-sql: true
  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017}
      database: ${MONGODB_DATABASE:dev}
  cloud:
    loadbalancer:
      ribbon:
        enabled: false
    kubernetes:
      loadbalancer:
        mode: service
      discovery:
        all-namespaces: false

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: ALWAYS
logging:
  pattern:
    # Logging pattern containing traceId and spanId; no longer provided through Sleuth by default
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"


diagnostics:
  service-name: "directory"
  version: ${VERSION:0.0.1-dev}
  build-timestamp: ${BUILD_TIMESTAMP:1970-01-01T00:00:00Z}
  git-hash: ${GIT_HASH:unknown}
  git-branch: ${GIT_BRANCH:main}
  git-commit-timestamp: ${GIT_COMMIT_TIMESTAMP:1970-01-01T00:00:00Z}
