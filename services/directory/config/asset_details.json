{"enabled": true, "endpointsOnly": false, "name": "assetdetails", "serviceName": "assetdetails", "serviceDisplayName": "Fathom Asset Details Service", "description": "Fathom Asset Details Service", "frnFormat": "frn::historian:<org-id>:/<resource-type>/<resource_name>", "frnRegex": "^frn::historian:.+", "dependentServices": ["assets"], "permissions": {"read": [{"permission": "details:ViewAssetDetails", "description": "view details", "requiredResources": ["assetdetails"]}], "write": [], "list": []}, "resources": {"assetdetails": {"name": "assetdetails", "frnFormat": "frn::historian:<org-id>:/assetdetails"}}, "endpoints": [{"url": "/assetdetails", "methods": [{"method": "GET", "permissions": ["details:ViewAssetDetails"]}]}]}