{"id": "a09507ab-6396-44d5-b70d-827313a835a8", "enabled": true, "endpointsOnly": false, "name": "historyreader", "serviceName": "historyreader", "serviceDisplayName": "Fathom Historian Service", "description": "Fathom Historian Service", "frnFormat": "frn::historyreader:<org-id>:/<resource-type>/<resource_name>", "frnRegex": "^frn::historyreader:.+", "permissions": {"read": [{"permission": "historyreader:<PERSON><PERSON><PERSON><PERSON>", "description": "view asset frame", "requiredResources": ["frame"]}, {"permission": "historyreader:<PERSON><PERSON>rameP<PERSON><PERSON>", "description": "view asset frame properties", "requiredResources": ["frameProperty"]}], "write": [], "list": []}, "resources": {"frame": {"name": "<PERSON>ame", "frnFormat": "frn::historyreader:<org-id>:/frame/${asset_id:Asset Id}"}, "frameProperty": {"name": "Frame Property", "frnFormat": "frn::historyreader:<org-id>:/frame/${asset_id:Asset Id}/property/${property_name:Property Name}"}}, "endpoints": [{"url": "/frame/single", "methods": [{"method": "POST", "permissions": ["historyreader:<PERSON><PERSON><PERSON><PERSON>"], "resourceVariables": [{"origin": "body", "value": "$.assetId", "resourceVariable": "asset_id"}], "postAuthorization": [{"permission": "historyreader:<PERSON><PERSON>rameP<PERSON><PERSON>", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/frame/multiple", "methods": [{"method": "POST", "permissions": ["historyreader:<PERSON><PERSON><PERSON><PERSON>"], "resourceVariables": [{"origin": "body", "value": "$.assetId", "resourceVariable": "asset_id"}], "postAuthorization": [{"permission": "historyreader:<PERSON><PERSON><PERSON><PERSON>", "protectors": [{"maskType": "protect", "fields": ["*"]}]}, {"permission": "historyreader:<PERSON><PERSON>rameP<PERSON><PERSON>", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/frame/scroll", "methods": [{"method": "POST", "permissions": ["historyreader:<PERSON><PERSON><PERSON><PERSON>"], "resourceVariables": [{"origin": "body", "value": "$.assetId", "resourceVariable": "asset_id"}], "postAuthorization": [{"permission": "historyreader:<PERSON><PERSON>rameP<PERSON><PERSON>", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/frame/scroll/continue", "methods": [{"method": "POST", "postAuthorization": [{"permission": "historyreader:<PERSON><PERSON>rameP<PERSON><PERSON>", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/frame/single/latestPerAsset", "methods": [{"method": "POST", "postAuthorization": [{"permission": "historyreader:<PERSON><PERSON><PERSON><PERSON>", "protectors": [{"maskType": "protect", "fields": ["*"]}]}, {"permission": "historyreader:<PERSON><PERSON>rameP<PERSON><PERSON>", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/frame/single/latest", "methods": [{"method": "POST", "permissions": ["historyreader:<PERSON><PERSON><PERSON><PERSON>"], "resourceVariables": [{"origin": "body", "value": "$.assetId", "resourceVariable": "asset_id"}], "postAuthorization": [{"permission": "historyreader:<PERSON><PERSON>rameP<PERSON><PERSON>", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/frame/pageable", "methods": [{"method": "POST", "permissions": ["historyreader:<PERSON><PERSON><PERSON><PERSON>"], "resourceVariables": [{"origin": "body", "value": "$.search.assetId", "resourceVariable": "asset_id"}], "postAuthorization": [{"permission": "historyreader:<PERSON><PERSON>rameP<PERSON><PERSON>", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/frame/multiple/latest", "methods": [{"method": "POST", "permissions": ["historyreader:<PERSON><PERSON><PERSON><PERSON>"], "resourceVariables": [{"origin": "body", "value": "$.assetId", "resourceVariable": "asset_id"}], "postAuthorization": [{"permission": "historyreader:<PERSON><PERSON>rameP<PERSON><PERSON>", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}]}