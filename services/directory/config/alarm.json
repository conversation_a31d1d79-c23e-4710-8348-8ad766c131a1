{"id": "9d85ed4d-7d63-4ee6-85a1-6daae4c4d922", "enabled": true, "endpointsOnly": false, "name": "alarm", "serviceName": "alarm", "serviceDisplayName": "Fathom Alarm Service", "description": "Fathom Alarm Service", "frnFormat": "frn::alarm:<org-id>:/<resource-type>/<resource_name>", "frnRegex": "^frn::alarm:.+", "permissions": {"read": [{"permission": "alarm:ViewAlarmWatcher", "description": "view alarm watcher", "requiredResources": ["alarmWatcher"]}, {"permission": "alarms:ViewAssetAlarm", "description": "view asset alarm", "requiredResources": ["assetAlarm"]}], "write": [{"permission": "alarm:C<PERSON><PERSON>larmWatcher", "description": "create a new alarm watcher", "requiredResources": ["alarmWatchers"]}, {"permission": "alarm:UpdateAlarmWatcher", "description": "update alarm watcher", "requiredResources": ["alarmWatcher"]}, {"permission": "alarm:DeleteAlarmWatcher", "description": "delete alarm watcher", "requiredResources": ["alarmWatcher"]}], "list": [{"permission": "alarm:ListAlarmWatchers", "description": "list alarms watchers", "requiredResources": ["alarmWatchers"]}]}, "resources": {"alarmWatchers": {"name": "Alarm Watchers", "frnFormat": "frn::alarm:<org-id>:/alarm_watchers"}, "alarmWatcher": {"name": "Alarm Watcher", "frnFormat": "frn::alarm:<org-id>:/alarm_watcher/${watcher_id: Watcher Id}"}, "assetAlarm": {"name": "<PERSON><PERSON>", "frnFormat": "frn::alarms:<org-id>:/alarm/${asset_id:Asset Id}"}}, "endpoints": [{"url": "/v2/watchers", "methods": [{"method": "GET", "permissions": ["alarm:ListAlarmWatchers"], "postAuthorization": [{"permission": "alarm:ViewAlarmWatcher", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}, {"method": "POST", "permissions": ["alarm:C<PERSON><PERSON>larmWatcher"]}]}, {"url": "/v2/watchers/createMany", "methods": [{"method": "POST", "permissions": ["alarm:C<PERSON><PERSON>larmWatcher"]}]}, {"url": "/v2/watchers/${watcherId}", "methods": [{"method": "GET", "permissions": ["alarm:ViewAlarmWatcher"], "resourceVariables": [{"origin": "path", "value": "3", "resourceVariable": "watcher_id"}]}, {"method": "PUT", "permissions": ["alarm:UpdateAlarmWatcher"], "resourceVariables": [{"origin": "path", "value": "3", "resourceVariable": "watcher_id"}]}, {"method": "DELETE", "permissions": ["alarm:DeleteAlarmWatcher"], "resourceVariables": [{"origin": "path", "value": "3", "resourceVariable": "watcher_id"}]}]}], "stream": [{"topic": "/alarm/${assetId}", "permissions": ["alarms:ViewAssetAlarm"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "asset_id"}]}]}