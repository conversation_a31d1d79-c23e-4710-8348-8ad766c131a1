{"id": "d6ef1ad7-62a4-47b0-a46a-468bef3481a8", "enabled": true, "endpointsOnly": false, "name": "assets", "serviceName": "assets", "serviceDisplayName": "Fathom Asset Service", "description": "Fathom Asset Service", "frnFormat": "frn::asset_manager:<org-id>:/<resource-type>/<resource_name>", "frnRegex": "^frn::asset_manager:.+", "permissions": {"read": [{"permission": "assets:ViewAsset", "description": "view asset details", "requiredResources": ["asset"]}, {"permission": "assets:ViewAssetProperty", "description": "view asset properties", "requiredResources": ["assetProperty"]}, {"permission": "assets:ViewAssetFrame", "description": "view asset frame", "requiredResources": ["assetFrame"]}, {"permission": "assets:ViewAssetFrameProperty", "description": "view frame property", "requiredResources": ["assetFrameProperty"]}, {"permission": "assets:ViewAssetRevisions", "description": "view asset revisions", "requiredResources": ["asset"]}, {"permission": "assets:ViewTemplate", "description": "view template", "requiredResources": ["template"]}, {"permission": "assets:ViewTemplateProperty", "description": "view template property", "requiredResources": ["templateProperty"]}, {"permission": "assets:ViewTemplateAssets", "description": "view template property", "requiredResources": ["template"]}, {"permission": "assets:ViewFormula", "description": "view formula", "requiredResources": ["formula"]}, {"permission": "assets:ViewCalculation", "description": "view calculation", "requiredResources": ["calculation"]}], "write": [{"permission": "assets:CreateAsset", "description": "create a new asset", "requiredResources": ["assets"]}, {"permission": "assets:DeleteAsset", "description": "delete asset", "requiredResources": ["asset"]}, {"permission": "assets:UpdateAsset", "description": "update asset", "requiredResources": ["asset"]}, {"permission": "assets:CreateTemplate", "description": "create template", "requiredResources": ["templates"]}, {"permission": "assets:UpdateTemplate", "description": "update template", "requiredResources": ["template"]}, {"permission": "assets:DeleteTemplate", "description": "delete template", "requiredResources": ["template"]}, {"permission": "assets:CreateFormula", "description": "create formula", "requiredResources": ["formulas"]}, {"permission": "assets:UpdateFormula", "description": "update formula", "requiredResources": ["formula"]}, {"permission": "assets:DeleteFormula", "description": "delete formula", "requiredResources": ["formula"]}, {"permission": "assets:CreateCalculation", "description": "create calculation", "requiredResources": ["calculations"]}, {"permission": "assets:UpdateCalculation", "description": "update calculation", "requiredResources": ["calculation"]}, {"permission": "assets:DeleteCalculation", "description": "delete calculation", "requiredResources": ["calculationList"]}, {"permission": "assets:CreateSystemTemplate", "description": "create system template", "requiredResources": ["templates"]}, {"permission": "assets:UpdateSystemTemplate", "description": "update system template", "requiredResources": ["template"]}, {"permission": "assets:DeleteSystemTemplate", "description": "delete system template", "requiredResources": ["template"]}, {"permission": "assets:CreateSystemFormula", "description": "create system formula", "requiredResources": ["formulas"]}, {"permission": "assets:UpdateSystemFormula", "description": "update system formula", "requiredResources": ["formula"]}, {"permission": "assets:DeleteSystemFormula", "description": "delete system formula", "requiredResources": ["formula"]}], "list": [{"permission": "assets:ListAssets", "description": "list assets", "requiredResources": ["assets"]}, {"permission": "assets:ListTemplates", "description": "list templates", "requiredResources": ["templates"]}, {"permission": "assets:ListFormulas", "description": "list formulas", "requiredResources": ["formulas"]}, {"permission": "assets:ListCalculations", "description": "list calculations", "requiredResources": ["calculationList"]}, {"permission": "assets:ListSystemTemplates", "description": "list system templates", "requiredResources": ["templates"]}]}, "resources": {"assets": {"name": "Assets", "frnFormat": "frn::asset_manager:<org-id>:/assets"}, "asset": {"name": "<PERSON><PERSON>", "frnFormat": "frn::asset_manager:<org-id>:/asset/${asset_name:Asset Name}"}, "assetProperty": {"name": "Asset Property", "frnFormat": "frn::asset_manager:<org-id>:/asset/${asset_name:Asset Name}/property/${property_name:Property Name}"}, "assetFrame": {"name": "<PERSON><PERSON>", "frnFormat": "frn::asset_manager:<org-id>:/frame/${asset_id:Asset Id}"}, "assetFrameProperty": {"name": "Asset Frame Property", "frnFormat": "frn::asset_manager:<org-id>:/frame/${asset_name:Asset Name}/property/${property_name:Property Name}"}, "templates": {"name": "Templates", "frnFormat": "frn::asset_manager:<org-id>:/templates"}, "template": {"name": "Template", "frnFormat": "frn::asset_manager:<org-id>:/template/${template_id:Template Id}"}, "templateProperty": {"name": "Template Property", "frnFormat": "frn::asset_manager:<org-id>:/template/${template_id:Template Id}/property/${template_property:Template Property}"}, "formulas": {"name": "Formulas", "frnFormat": "frn::asset_manager:<org-id>:/formulas"}, "formula": {"name": "Formula", "frnFormat": "frn::asset_manager:<org-id>:/formulas/${formula_id:Formula Id}"}, "calculation": {"name": "Calculation", "frnFormat": "frn::asset_manager:<org-id>:/template/${template_name:Template Name}/calculation/${calculation_id:Calculation Id}"}, "calculations": {"name": "Calculations", "frnFormat": "frn::asset_manager:<org-id>:/template/${template_name:Template Name}"}, "calculationList": {"name": "Calculation standalone", "frnFormat": "frn::asset_manager:<org-id>:/calculations"}}, "endpoints": [{"url": "/assets", "methods": [{"method": "GET", "permissions": ["assets:ListAssets"], "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}, {"method": "POST", "permissions": ["assets:CreateAsset"]}]}, {"url": "/assets/${assetName}", "methods": [{"method": "GET", "permissions": ["assets:ViewAsset"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "asset_name"}], "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}, {"permission": "assets:ViewAssetProperty", "protectors": [{"maskType": "protect", "fields": ["*"]}]}, {"permission": "assets:ViewTemplate", "protectors": [{"maskType": "protect", "fields": ["*"]}]}, {"permission": "assets:ViewTemplateProperty", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}, {"method": "PUT", "permissions": ["assets:UpdateAsset"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "asset_name"}]}, {"method": "DELETE", "permissions": ["assets:DeleteAsset"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "asset_name"}]}]}, {"url": "/frames", "methods": [{"method": "GET", "permissions": ["assets:ViewAssetFrame"], "resourceVariables": [{"origin": "query", "value": "assetId", "resourceVariable": "asset_id"}], "postAuthorization": [{"permission": "assets:ViewAssetFrameProperty", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/assets/getByIds", "methods": [{"method": "GET", "permissions": ["assets:ListAssets"], "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/assets/${assetName}/template", "methods": [{"method": "GET", "permissions": ["assets:ViewAsset"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "asset_name"}], "postAuthorization": [{"permission": "assets:ViewTemplate", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/assets/${assetName}/revisions", "methods": [{"method": "GET", "permissions": ["assets:ViewAssetRevisions"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "asset_name"}]}]}, {"url": "/templates/${templateId}", "methods": [{"method": "GET", "permissions": ["assets:ViewTemplate"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "template_id"}], "postAuthorization": [{"permission": "assets:ViewTemplateProperty", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}, {"method": "PUT", "permissions": ["assets:UpdateTemplate"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "template_id"}]}, {"method": "DELETE", "permissions": ["assets:DeleteTemplate"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "template_id"}]}]}, {"url": "/templates/${templateId}?system=true", "methods": [{"method": "PUT", "permissions": ["assets:UpdateSystemTemplate"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "template_id"}]}, {"method": "DELETE", "permissions": ["assets:DeleteSystemTemplate"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "template_id"}]}]}, {"url": "/templates", "methods": [{"method": "GET", "permissions": ["assets:ListTemplates"], "postAuthorization": [{"permission": "assets:ViewTemplate", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}, {"method": "POST", "permissions": ["assets:CreateTemplate"]}]}, {"url": "/templates/${templateId}/assets", "methods": [{"method": "GET", "permissions": ["assets:ViewTemplateAssets"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "template_id"}], "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/formulas/${formulaId}", "methods": [{"method": "GET", "permissions": ["assets:ViewFormula"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "formula_id"}]}, {"method": "PUT", "permissions": ["assets:UpdateFormula"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "formula_id"}]}, {"method": "DELETE", "permissions": ["assets:DeleteFormula"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "formula_id"}]}]}, {"url": "/formulas/${formulaId}?system=true", "methods": [{"method": "PUT", "permissions": ["assets:UpdateSystemFormula"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "formula_id"}]}, {"method": "DELETE", "permissions": ["assets:DeleteSystemFormula"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "formula_id"}]}]}, {"url": "/formulas", "methods": [{"method": "GET", "permissions": ["assets:ListFormulas"]}, {"method": "POST", "permissions": ["assets:CreateFormula"]}]}, {"url": "/formulas?system=true", "methods": [{"method": "POST", "permissions": ["assets:CreateSystemFormula"]}]}]}