{"enabled": true, "endpointsOnly": false, "name": "status", "serviceName": "status", "serviceDisplayName": "Fathom Asset Status Service", "description": "Fathom Asset Status Service", "frnFormat": "frn::status:<org-id>:/<resource-type>/<resource_name>", "frnRegex": "^frn::status:.+", "dependentServices": ["assets"], "permissions": {"read": [{"permission": "status:ViewAssetStatus", "description": "view status", "requiredResources": ["assetStatus"]}], "write": [], "list": []}, "resources": {"assetStatus": {"name": "Asset Status", "frnFormat": "frn::status:<org-id>:/asset/${asset_id:Asset Id}"}}, "endpoints": [{"url": "/assetstatus/aggregated/major", "methods": [{"method": "GET", "permissions": ["status:ViewAssetStatus"], "resourceVariables": [{"origin": "query", "value": "assetId", "resourceVariable": "asset_id"}]}]}, {"url": "/assetstatus/aggregated/all", "methods": [{"method": "GET", "permissions": ["status:ViewAssetStatus"], "resourceVariables": [{"origin": "query", "value": "assetId", "resourceVariable": "asset_id"}]}]}, {"url": "/assetstatus/major", "methods": [{"method": "GET", "permissions": ["status:ViewAssetStatus"], "resourceVariables": [{"origin": "query", "value": "assetId", "resourceVariable": "asset_id"}], "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/assetstatus/latest", "methods": [{"method": "GET", "permissions": ["status:ViewAssetStatus"], "resourceVariables": [{"origin": "query", "value": "assetId", "resourceVariable": "asset_id"}], "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/assetstatus/all", "methods": [{"method": "GET", "permissions": ["status:ViewAssetStatus"], "resourceVariables": [{"origin": "query", "value": "assetId", "resourceVariable": "asset_id"}], "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}]}