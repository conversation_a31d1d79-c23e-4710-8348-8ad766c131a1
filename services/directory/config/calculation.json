{"enabled": true, "endpointsOnly": true, "name": "calculation", "serviceName": "calculation", "dependentServices": ["assets"], "endpoints": [{"url": "/calculations", "methods": [{"method": "GET", "permissions": ["assets:ListCalculations"], "postAuthorization": [{"permission": "assets:ViewCalculation", "protectors": [{"maskType": "protect", "fields": ["templateName", "description", "code", "preferredUnits", "excludedAssetsIds"]}]}]}, {"method": "POST", "permissions": ["assets:CreateCalculation"], "resourceVariables": [{"origin": "body", "value": "$.templateName", "resourceVariable": "template_name"}]}]}, {"url": "/calculations/${variable}", "methods": [{"method": "GET", "permissions": [], "postAuthorization": [{"permission": "assets:ViewCalculation", "protectors": [{"maskType": "protect", "fields": ["templateName", "description", "code", "preferredUnits", "excludedAssetsIds"]}]}]}, {"method": "PUT", "permissions": ["assets:UpdateCalculation"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "calculation_id"}, {"origin": "body", "value": "$.templateName", "resourceVariable": "template_name"}]}, {"method": "DELETE", "permissions": ["assets:DeleteCalculation"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "calculation_id"}]}]}, {"url": "/calculations/${assetId}", "methods": [{"method": "PUT", "permissions": ["assets:UpdateCalculation"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "calculation_id"}, {"origin": "body", "value": "$.templateName", "resourceVariable": "template_name"}]}, {"method": "DELETE", "permissions": ["assets:DeleteCalculation"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "calculation_id"}]}]}]}