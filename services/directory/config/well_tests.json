{"enabled": true, "endpointsOnly": false, "name": "welltests", "serviceName": "welltests", "serviceDisplayName": "Fathom Well Tests Service", "description": "Fathom Well Tests Service", "frnFormat": "frn:wells:welltests:<org-id>:/<resource-type>/<resource-name>", "frnRegex": "^frn:wells:welltests:.+", "permissions": {"read": [{"permission": "welltests:ViewWellTest", "description": "view well test", "requiredResources": ["well_test_for_well"]}], "list": [{"permission": "welltests:ViewWellTests", "description": "view well tests", "requiredResources": ["well_tests"]}], "write": [{"permission": "welltests:CreateWellTest", "description": "create well tests", "requiredResources": ["well_tests"]}, {"permission": "welltests:DeleteWellTest", "description": "delete well tests", "requiredResources": ["well_test"]}, {"permission": "welltests:DeleteWellTests", "description": "delete well tests for well", "requiredResources": ["well_test_for_well"]}, {"permission": "welltests:UpdateWellTest", "description": "update well test", "requiredResources": ["well_test"]}]}, "resources": {"well_test": {"name": "Well Test", "frnFormat": "frn:wells:welltests:<org-id>:/wellname/${well_name: Well Name}/welltest/${well_test_id: Well Test ID}"}, "well_test_for_well": {"name": "Well Tests For Well", "frnFormat": "frn:wells:welltests:<org-id>:/wellname/${well_name: Well Name}"}, "well_tests": {"name": "Well Tests", "frnFormat": "frn:wells:welltests:<org-id>:/well_tests"}}, "endpoints": [{"url": "/welltests", "methods": [{"method": "GET", "permissions": ["welltests:ViewWellTests"]}, {"method": "POST", "permissions": ["welltests:CreateWellTest"]}, {"method": "DELETE", "permissions": ["welltests:DeleteWellTests"], "resourceVariables": [{"origin": "query", "value": "wellName", "resourceVariable": "well_name"}]}]}, {"url": "/welltests/execute", "methods": [{"method": "POST", "permissions": ["welltests:CreateWellTest"]}]}, {"url": "/welltest", "methods": [{"method": "GET", "permissions": ["welltests:ViewWellTest"], "resourceVariables": [{"origin": "query", "value": "wellName", "resourceVariable": "well_name"}]}, {"method": "POST", "permissions": ["welltests:CreateWellTest"]}, {"method": "PUT", "permissions": ["welltests:UpdateWellTest"], "resourceVariables": [{"origin": "body", "value": "$.assetName", "resourceVariable": "well_name"}, {"origin": "body", "value": "$.wellTestId", "resourceVariable": "well_test_id"}]}, {"method": "DELETE", "permissions": ["welltests:DeleteWellTest"], "resourceVariables": [{"origin": "query", "value": "wellName", "resourceVariable": "well_name"}, {"origin": "query", "value": "wellTestId", "resourceVariable": "well_test_id"}]}]}, {"url": "/welltest/execute", "methods": [{"method": "POST", "permissions": ["welltests:CreateWellTest"]}]}]}