{"enabled": true, "endpointsOnly": false, "name": "logical-tree", "serviceName": "logical-tree", "serviceDisplayName": "Fathom Logical Tree Service", "description": "Fathom Logical Tree Service", "frnFormat": "frn::logical-tree:<org-id>:/<resource-type>/<resource_name>", "frnRegex": "^frn::logical-tree:.+", "dependentServices": ["assets"], "permissions": {"read": [{"permission": "logical-tree:ViewRootTree", "description": "view whole tree", "requiredResources": ["tree"]}], "write": [{"permission": "logical-tree:Create<PERSON>ree", "description": "create tree", "requiredResources": ["trees"]}, {"permission": "logical-tree:UpdateTree", "description": "update tree", "requiredResources": ["tree"]}, {"permission": "logical-tree:DeleteTree", "description": "delete tree", "requiredResources": ["trees"]}], "list": [{"permission": "logical-tree:ListTrees", "description": "view tree list", "requiredResources": ["trees"]}]}, "resources": {"trees": {"name": "Trees", "frnFormat": "frn::logical-tree:<org-id>:/trees"}, "tree": {"name": "Tree", "frnFormat": "frn::logical-tree:<org-id>:/tree/${tree_id: Tree Id}"}}, "endpoints": [{"url": "/api/v1/trees", "methods": [{"method": "GET", "permissions": ["logical-tree:ListTrees"], "resourceVariables": [], "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}, {"permission": "logical-tree:ViewRootTree", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}, {"method": "POST", "permissions": ["logical-tree:Create<PERSON>ree"]}, {"method": "PUT", "permissions": ["logical-tree:UpdateTree"], "resourceVariables": [{"origin": "body", "value": "$.id", "resourceVariable": "tree_id"}]}, {"method": "DELETE", "permissions": ["logical-tree:DeleteTree"]}]}, {"url": "/api/v1/trees/root", "methods": [{"method": "GET", "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}, {"permission": "logical-tree:ViewRootTree", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/api/v1/trees/${nodeId}", "methods": [{"method": "GET", "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}, {"permission": "logical-tree:ViewRootTree", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/api/v1/trees/${nodeId}/children", "methods": [{"method": "GET", "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}, {"permission": "logical-tree:ViewRootTree", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/api/v1/trees/${nodeId}/children/${assetId}/ids", "methods": [{"method": "GET", "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}, {"permission": "logical-tree:ViewRootTree", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}, {"url": "/api/v1/trees/{nodeId}/children/{assetId}/path", "methods": [{"method": "GET", "postAuthorization": [{"permission": "assets:ViewAsset", "protectors": [{"maskType": "protect", "fields": ["*"]}]}, {"permission": "logical-tree:ViewRootTree", "protectors": [{"maskType": "protect", "fields": ["*"]}]}]}]}]}