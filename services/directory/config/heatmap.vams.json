{"enabled": true, "endpointsOnly": false, "name": "heatmap", "serviceName": "heatmap", "serviceDisplayName": "Fathom Heatmap VAMS Service", "description": "Fathom Heatmap VAMS Service", "frnFormat": "frn::heatmap:<org-id>:/<resource-type>/<resource-name>", "frnRegex": "^frn::heatmap:.+", "permissions": {"read": [{"permission": "heatmap:ViewHeatMap", "description": "view heatmap", "requiredResources": ["heatmap_for_asset"]}], "list": [{"permission": "heatmap:ViewHeatMaps", "description": "view all heatmaps", "requiredResources": ["heatmaps"]}, {"permission": "heatmap:ViewHeatMapsForAsset", "description": "view all heatmaps for child assets", "requiredResources": ["heatmap_for_asset"]}], "write": [{"permission": "heatmap:CreateHeatMap", "description": "create heatmap", "requiredResources": ["heatmap_for_asset"]}, {"permission": "heatmap:DeletHeatMap", "description": "delete heat map", "requiredResources": ["heatmap_for_asset"]}, {"permission": "heatmap:CreateHeatMaps", "description": "create multiple heatmaps", "requiredResources": ["heatmaps"]}, {"permission": "heatmap:UpdateHeatMap", "description": "update heatmap", "requiredResources": ["heatmap"]}]}, "resources": {"heatmap": {"name": "Heatmap", "frnFormat": "frn::heatmap:<org-id>:/asset/${asset_name: Asset Name}/heatmap/${heatmap_id: Heatmap Test ID}"}, "heatmap_for_asset": {"name": "Heatmap for asset", "frnFormat": "frn::heatmap:<org-id>:/heatmap/${asset_name: Asset Name}"}, "heatmaps": {"name": "Heatmaps", "frnFormat": "frn::heatmap:<org-id>:/heatmaps"}}, "endpoints": [{"url": "/heatmaps", "methods": [{"method": "GET", "permissions": ["heatmap:ViewHeatMaps"]}, {"method": "POST", "permissions": ["heatmap:CreateHeatMaps"]}]}, {"url": "/heatmaps/${assetName}", "methods": [{"method": "GET", "permissions": ["heatmap:ViewHeatMapsForAsset"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "asset_name"}]}]}, {"url": "/heatmap", "methods": [{"method": "POST", "permissions": ["heatmap:CreateHeatMap"], "resourceVariables": [{"origin": "body", "value": "$.assetName", "resourceVariable": "asset_name"}]}, {"method": "PUT", "permissions": ["heatmap:UpdateHeatMap"], "resourceVariables": [{"origin": "body", "value": "$.assetName", "resourceVariable": "asset_name"}, {"origin": "body", "value": "$.heatmapId", "resourceVariable": "heatmap_id"}]}]}, {"url": "/heatmap/${assetName}", "methods": [{"method": "GET", "permissions": ["heatmap:ViewHeatMap"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "asset_name"}]}, {"method": "DELETE", "permissions": ["heatmap:DeletHeatMap"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "asset_name"}]}]}]}