{"enabled": true, "endpointsOnly": false, "name": "productiontargets", "serviceName": "productiontargets", "serviceDisplayName": "Fathom Production Targets Service", "description": "Fathom Production Targets Service", "frnFormat": "frn::productiontargets:<org-id>:/<resource-type>/<resource_name>", "frnRegex": "^frn::productiontargets:.+", "permissions": {"read": [], "write": [{"permission": "productiontargets:CreateDefaultTarget", "description": "create a default target", "requiredResources": ["targets"]}, {"permission": "productiontargets:UpdateDefaultTarget", "description": "update a default target", "requiredResources": ["targets"]}], "list": []}, "resources": {"targets": {"name": "Target", "frnFormat": "frn::productiontargets:<org-id>:/target"}}, "endpoints": [{"url": "/productionTargets?isDefault=true", "methods": [{"method": "POST", "permissions": ["productiontargets:CreateDefaultTarget"]}, {"method": "PUT", "permissions": ["productiontargets:UpdateDefaultTarget"]}]}]}