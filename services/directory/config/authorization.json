{"enabled": false, "endpointsOnly": false, "name": "authorization", "serviceName": "authorization", "serviceDisplayName": "Fathom Authorization Service", "description": "Fathom Authorization Service", "frnFormat": "frn::authorization:<org-id>:/<resource-type>/<resource_name>", "frnRegex": "^frn::authorization:.+", "permissions": {"read": [{"permission": "authorization:ViewGroup", "description": "view group details", "requiredResources": ["group"]}, {"permission": "authorization:ViewOrganization", "description": "view organization details", "requiredResources": ["organization"]}, {"permission": "authorization:ViewMember", "description": "view member details", "requiredResources": ["member"]}, {"permission": "authorization:ViewPolicy", "description": "view policy details", "requiredResources": ["policy"]}], "write": [{"permission": "authorization:AddMember", "description": "add member to organization", "requiredResources": ["members"]}, {"permission": "authorization:AttachPolicyToGroup", "description": "attach policy to group", "requiredResources": ["actionPolicyByGroup"]}, {"permission": "authorization:AttachPolicyToMember", "description": "attach policy to member", "requiredResources": ["actionPolicyByMember"]}, {"permission": "authorization:CreateGroup", "description": "create a new group", "requiredResources": ["groups"]}, {"permission": "authorization:CreatePolicy", "description": "create a new policy", "requiredResources": ["policies"]}, {"permission": "authorization:DeAttachPolicyFromGroup", "description": "de-attach policy from group", "requiredResources": ["actionPolicyByGroup"]}, {"permission": "authorization:DeAttachPolicyFromMember", "description": "de-attach policy from member", "requiredResources": ["actionPolicyByMember"]}, {"permission": "authorization:DeleteGroup", "description": "delete group", "requiredResources": ["group"]}, {"permission": "authorization:DeletePolicy", "description": "delete policy", "requiredResources": ["policy"]}, {"permission": "authorization:RemoveMember", "description": "remove member from organization", "requiredResources": ["member"]}, {"permission": "authorization:AddMemberToGroup", "description": "add member to group", "requiredResources": ["actionGroupByMember"]}, {"permission": "authorization:RemoveMemberFromGroup", "description": "remove member from group", "requiredResources": ["actionGroupByMember"]}, {"permission": "authorization:UpdateOrganizationOwner", "description": "update organization's owner", "requiredResources": ["organization"]}, {"permission": "authorization:UpdatePolicy", "description": "update policy", "requiredResources": ["policy"]}], "list": [{"permission": "authorization:ListGroupMembers", "description": "list group's members", "requiredResources": ["group"]}, {"permission": "authorization:ListGroupPolicies", "description": "list group's policies", "requiredResources": ["group"]}, {"permission": "authorization:ListGroups", "description": "list groups", "requiredResources": ["groups"]}, {"permission": "authorization:ListMemberPolicies", "description": "list member's policies", "requiredResources": ["member"]}, {"permission": "authorization:ListMemberGroups", "description": "list member's groups", "requiredResources": ["member"]}, {"permission": "authorization:ListMembers", "description": "list organization's members", "requiredResources": ["members"]}, {"permission": "authorization:ListPolicies", "description": "list policies", "requiredResources": ["policies"]}]}, "resources": {"organization": {"id": null, "name": "Organization", "frnFormat": "frn::authorization:<org-id>:/"}, "member": {"id": null, "name": "Member", "frnFormat": "frn::authorization:<org-id>:/orgs/${organization_id:Organization Id}/members/${member_email:Member <PERSON><PERSON>}"}, "members": {"id": null, "name": "Members", "frnFormat": "frn::authorization:<org-id>:/orgs/${organization_id:Organization Id}/members"}, "actionPolicyByGroup": {"id": null, "name": "ActionPolicyByGroup", "frnFormat": "frn::authorization:<org-id>:/orgs/${organization_id:Organization Id}/groups/${group_name:Group Name}/policies/${policy_name:Policy Name}"}, "actionPolicyByMember": {"id": null, "name": "ActionPolicyByMember", "frnFormat": "frn::authorization:<org-id>:/orgs/${organization_id:Organization Id}/members/${member_email:Member Email}/policies/${policy_name:Policy Name}"}, "actionGroupByMember": {"id": null, "name": "ActionMemberByGroup", "frnFormat": "frn::authorization:<org-id>:/orgs/${organization_id:Organization Id}/groups/${group_name:Group Name}/members/${member_email:Member Email}"}, "policy": {"id": null, "name": "Policy", "frnFormat": "frn::authorization:<org-id>:/orgs/${organization_id:Organization Id}/policies/${policy_name:Policy Name}"}, "policies": {"id": null, "name": "Policies", "frnFormat": "frn::authorization:<org-id>:/orgs/${organization_id:Organization Id}/policies"}, "group": {"id": null, "name": "Group", "frnFormat": "frn::authorization:<org-id>:/orgs/${organization_id:Organization Id}/groups/${group_name:Group Name}"}, "groups": {"id": null, "name": "Groups", "frnFormat": "frn::authorization:<org-id>:/orgs/${organization_id:Organization Id}/groups"}}, "endpoints": [{"url": "/orgs/${orgId}", "methods": [{"method": "GET", "permissions": ["authorization:ViewOrganization"], "resourceVariables": []}, {"method": "PUT", "permissions": ["authorization:UpdateOrganization"], "resourceVariables": []}]}, {"url": "/orgs/${orgId}/members", "methods": [{"method": "GET", "permissions": ["authorization:ListMembers"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}]}, {"method": "POST", "permissions": ["authorization:AddMember"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}]}]}, {"url": "/orgs/${orgId}/members/${memberEmail}", "methods": [{"method": "GET", "permissions": ["authorization:ViewMember"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "member_email"}]}, {"method": "DELETE", "permissions": ["authorization:RemoveMember"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "member_email"}]}]}, {"url": "/orgs/${orgId}/members/${memberEmail}/groups", "methods": [{"method": "GET", "permissions": ["authorization:ListMemberGroups"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "member_email"}]}, {"method": "POST", "permissions": ["authorization:AddMemberToGroup"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "member_email"}, {"origin": "body", "value": "$.groupName", "resourceVariable": "group_name"}]}]}, {"url": "/orgs/${orgId}/members/${memberEmail}/groups/${groupName}", "methods": [{"method": "DELETE", "permissions": ["authorization:RemoveMemberFromGroup"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "member_email"}, {"origin": "path", "value": "6", "resourceVariable": "group_name"}]}]}, {"url": "/orgs/${orgId}/members/${memberEmail}/policies", "methods": [{"method": "GET", "permissions": ["authorization:ListMemberPolicies"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "member_email"}]}, {"method": "POST", "permissions": ["authorization:AttachPolicyToMember"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "member_email"}, {"origin": "body", "value": "$.policyName", "resourceVariable": "policy_name"}]}]}, {"url": "/orgs/${orgId}/members/${memberEmail}/policies/${policyName}", "methods": [{"method": "DELETE", "permissions": ["authorization:DeAttachPolicyFromMember"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "member_email"}, {"origin": "path", "value": "6", "resourceVariable": "policy_name"}]}]}, {"url": "/orgs/${orgId}/groups", "methods": [{"method": "GET", "permissions": ["authorization:ListGroups"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}]}, {"method": "POST", "permissions": ["authorization:CreateGroup"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}]}]}, {"url": "/orgs/${orgId}/groups/${groupName}", "methods": [{"method": "GET", "permissions": ["authorization:ViewGroup"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "group_name"}]}, {"method": "DELETE", "permissions": ["authorization:DeleteGroup"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "group_name"}]}]}, {"url": "/orgs/${orgId}/groups/${groupName}/members", "methods": [{"method": "GET", "permissions": ["authorization:ListGroupMembers"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "group_name"}]}, {"method": "POST", "permissions": ["authorization:AddMemberToGroup"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "group_name"}, {"origin": "body", "value": "$.memberEmail", "resourceVariable": "member_email"}]}]}, {"url": "/orgs/${orgId}/groups/${groupName}/members/${memberEmail}", "methods": [{"method": "DELETE", "permissions": ["authorization:RemoveMemberFromGroup"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "group_name"}, {"origin": "path", "value": "6", "resourceVariable": "member_email"}]}]}, {"url": "/orgs/${orgId}/groups/${groupName}/policies", "methods": [{"method": "GET", "permissions": ["authorization:ListGroupPolicies"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "group_name"}]}, {"method": "POST", "permissions": ["authorization:AttachPolicyToGroup"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "group_name"}, {"origin": "body", "value": "$.policyName", "resourceVariable": "policy_name"}]}]}, {"url": "/orgs/${orgId}/groups/${groupName}/policies/${policyName}", "methods": [{"method": "DELETE", "permissions": ["authorization:DeAttachPolicyFromGroup"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "group_name"}, {"origin": "path", "value": "6", "resourceVariable": "policy_name"}]}]}, {"url": "/orgs/${orgId}/policies", "methods": [{"method": "GET", "permissions": ["authorization:ListPolicies"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}]}, {"method": "POST", "permissions": ["authorization:CreatePolicy"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}]}]}, {"url": "/orgs/${orgId}/policies/${policyName}", "methods": [{"method": "GET", "permissions": ["authorization:ViewPolicy"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "policy_name"}]}, {"method": "PUT", "permissions": ["authorization:UpdatePolicy"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "policy_name"}]}, {"method": "DELETE", "permissions": ["authorization:DeletePolicy"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "policy_name"}]}]}, {"url": "/orgs/${orgId}/policies/${policyName}/members", "methods": [{"method": "GET", "permissions": ["authorization:ViewPolicy"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "policy_name"}]}, {"method": "POST", "permissions": ["authorization:AttachPolicyToMember"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "policy_name"}, {"origin": "body", "value": "$.memberEmail", "resourceVariable": "member_email"}]}]}, {"url": "/orgs/${orgId}/policies/${policyName}/members/${memberEmail}", "methods": [{"method": "DELETE", "permissions": ["authorization:DeAttachPolicyFromMember"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "policy_name"}, {"origin": "path", "value": "6", "resourceVariable": "member_email"}]}]}, {"url": "/orgs/${orgId}/policies/${policyName}/groups", "methods": [{"method": "GET", "permissions": ["authorization:ViewPolicy"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "policy_name"}]}, {"method": "POST", "permissions": ["authorization:AttachPolicyToGroup"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "policy_name"}, {"origin": "body", "value": "$.groupName", "resourceVariable": "group_name"}]}]}, {"url": "/orgs/${orgId}/policies/${policyName}/groups/${groupName}", "methods": [{"method": "DELETE", "permissions": ["authorization:DeAttachPolicyFromGroup"], "resourceVariables": [{"origin": "path", "value": "2", "resourceVariable": "organization_id"}, {"origin": "path", "value": "4", "resourceVariable": "policy_name"}, {"origin": "path", "value": "6", "resourceVariable": "group_name"}]}]}], "stream": null, "staticResources": null}