# -*- MakeFile -*-
export ASSETSMANAGER_TAG := $(shell ./mvnw -q -Dexec.executable="echo" -Dexec.args='$${project.version}' --non-recursive exec:exec)
export VERSION := $(shell ./mvnw org.apache.maven.plugins:maven-help-plugin:evaluate -Dexpression=project.version -q -DforceStdout)
export ARTIFACT_ID := $(shell ./mvnw org.apache.maven.plugins:maven-help-plugin:evaluate -Dexpression=project.artifactId -q -DforceStdout)
export JAR_NAME := $(ARTIFACT_ID)-$(VERSION).jar

.PHONY: info
info:
	wget https://fathomsjenkinsdeployment.s3.eu-central-1.amazonaws.com/scripts/Java/buildBanner.sh
	wget https://fathomsjenkinsdeployment.s3.eu-central-1.amazonaws.com/scripts/Java/getAppProp.sh
	wget https://fathomsjenkinsdeployment.s3.eu-central-1.amazonaws.com/scripts/Java/getLastCommit.sh
	wget https://fathomsjenkinsdeployment.s3.eu-central-1.amazonaws.com/scripts/Java/getVersion.sh
	chmod +x buildBanner.sh getAppProp.sh getLastCommit.sh getVersion.sh
	./buildBanner.sh
	
.PHONY: clean
clean: mvnw mvnw.cmd
	./mvnw clean

.PHONY: compile
compile: mvnw mvnw.cmd info
	./mvnw compile

.PHONY: test
test: mvnw mvnw.cmd
	./mvnw test

.PHONY: package
package: compile
	./mvnw package

.PHONY: repackage
repackage: clean package

.PHONY: build-artifact
build-artifact:
	echo "Build gateway service jar"
	./mvnw clean install

PHONY: docker
docker: 
	docker build -f ./Dockerfile -t b4ft1bt3.gra7.container-registry.ovh.net/library/$(ARTIFACT_ID):$(VERSION) --build-arg JAR_PATH="target/$(JAR_NAME)" .

.PHONY: docker-latest
docker-latest: 
	docker tag b4ft1bt3.gra7.container-registry.ovh.net/library/$(ARTIFACT_ID):$(VERSION) b4ft1bt3.gra7.container-registry.ovh.net/library/$(ARTIFACT_ID):latest

.PHONY: docker-nexus
docker-nexus: 
	docker push b4ft1bt3.gra7.container-registry.ovh.net/library/$(ARTIFACT_ID):latest
