pipeline {
    agent any
    stages {
        stage("build") {
            when {
                expression { env.BRANCH_NAME ==~ /^v2-(dev|qa|stg)/ }
            }
            steps {
                build job: 'Maven_Build', parameters: [string(name: 'Repository', value: env.GIT_URL), string(name: 'Branch', value: env.BRANCH_NAME)]
            }
        }
        stage("deploy") {
            when {
                expression { env.BRANCH_NAME ==~ /^v2-(dev|qa|stg)/ }
            }
            steps {
                script {
                    env.GIT_REPO_NAME = env.GIT_URL.replaceFirst(/^.*\/([^\/]+?).git$/, '$1')
                    env.SERVICE_NAME = env.GIT_REPO_NAME.replaceFirst(/(com\.fathom\.services\.)/, "")
                    if (env.BRANCH_NAME == 'v2-dev') {
                        echo "Uninstalling old release"
                        try {
                            build job: 'Uninstall_K8s', parameters: [string(name: 'SERVICE_NAME', value: env.SERVICE_NAME), string(name: 'ENVIRONMENT', value: "dev")]
                            } catch (err) {
                                echo "There is error while Uninstalling " + "env.SERVICE_NAME"
                            }
                        echo "Installing new release"
                        try {
                            build job: 'Deploy_K8s', parameters: [string(name: 'SERVICE_NAME', value: env.SERVICE_NAME), string(name: 'ENVIRONMENT', value: "dev")]
                            } catch (err) {
                                echo "There is error while Installing " + "env.SERVICE_NAME"
                            }
                    } else if (env.BRANCH_NAME == 'v2-qa') {
                        echo "Uninstalling old release"
                        try {
                            build job: 'Uninstall_K8s', parameters: [string(name: 'SERVICE_NAME', value: env.SERVICE_NAME), string(name: 'ENVIRONMENT', value: "qa")]
                            } catch (err) {
                                echo "There is error while Uninstalling " + "env.SERVICE_NAME"
                            }
                        echo "Installing new release"
                        try {
                            build job: 'Deploy_K8s', parameters: [string(name: 'SERVICE_NAME', value: env.SERVICE_NAME), string(name: 'ENVIRONMENT', value: "qa")]
                            } catch (err) {
                                echo "There is error while Installing " + "env.SERVICE_NAME"
                            }
                    } 
                }
            }
        }
    }
    post {
        always {
            cleanWs()
        }
    }
}