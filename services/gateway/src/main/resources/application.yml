spring:
  cloud:
    gateway:
      httpclient:
        websocket:
          max-frame-payload-length: ${SPRING_CLOUD_GATEWAY_HTTPSERVER_WEBSOCKET_MAX_FRAME_PAYLOAD_LENGTH:20971520} # Default to 20 MB
        connect-timeout: 120000
        response-timeout: 120000
        ssl:
          useInsecureTrustManager: true
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Credentials Access-Control-Allow-Origin , RETAIN_LAST
      globalcors:
        corsConfigurations:
          "[/**]":
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
      routes:
        - uri: ${AGGREGATION_URI:http://aggregation:8088}
          predicates:
            - Path=/api/aggregation/**
          filters:
            - StripPrefix=2
        - uri: ${DASHBOARD_URI:http://dashboard:8100}
          predicates:
            - Path=/api/dashboard/**
          filters:
            - StripPrefix=2
        - uri: ${ASSETDETAILS_URI:http://assetdetails:8024}
          predicates:
            - Path=/api/assetdetails/**
          filters:
            - StripPrefix=2
        - uri: ${ASSETHISTORY_URI:http://assethistory:8098}
          predicates:
            - Path=/api/assethistory/**
          filters:
            - StripPrefix=2
        - uri: ${ASSETSTATUSREADER_URI:http://assetstatusreader:8556}
          predicates:
            - Path=/api/status/**
          filters:
            - StripPrefix=2
        - uri: ${ASSETINDICATORS_URI:http://assetindicators:8023}
          predicates:
            - Path=/api/assetindicators/**
          filters:
            - StripPrefix=2
        - uri: ${ASSETLIFECYCLE_URI:http://assetlifecycle:8020}
          predicates:
            - Path=/api/assetlifecycle/**
          filters:
            - StripPrefix=2
        - uri: ${ASSETSUMMARY_URI:http://assetsummary:8072}
          predicates:
            - Path=/api/assetsummary/**
          filters:
            - StripPrefix=2
        - uri: ${AUTHORIZATION_URI:http://authorization:5999/v1}
          predicates:
            - Path=/api/authorization/**
          filters:
            - StripPrefix=2
        - uri: ${CATALOG_URI:http://catalog:8074}
          predicates:
            - Path=/api/catalog/**
          filters:
            - StripPrefix=2
        - uri: ${DIAGNOSTICS_URI:http://diagnostics:8006}
          predicates:
            - Path=/api/diagnostics/**
          filters:
            - StripPrefix=2
        - uri: ${DIRECTORY_URI:http://directory:38086}
          predicates:
            - Path=/api/services/**
          filters:
            - StripPrefix=2
        - uri: ${ESPCALC_URI:http://espcalcapi:5100}
          predicates:
            - Path=/api/artificialLiftCalculations/**
          filters:
            - StripPrefix=2
        - uri: ${FILE_URI:http://fileservice:8015}
          predicates:
            - Path=/api/fileservice/**
          filters:
            - StripPrefix=2
        - uri: ${MAPAPI_URI:http://mapapi:8092}
          predicates:
            - Path=/api/mapapi/**
          filters:
            - StripPrefix=2
        - uri: ${MAP_URI:http://map:8082}
          predicates:
            - Path=/api/map/**
          filters:
            - StripPrefix=2
        - uri: ${ORGANIZATION_URI:http://organization:8080}
          predicates:
            - Path=/api/organization/**
          filters:
            - StripPrefix=2
        - uri: ${PROFILE_URI:http://profile:8089}
          predicates:
            - Path=/api/profile/**
          filters:
            - StripPrefix=2
        - uri: ${USERUNITS_URI:http://userunits:8093}
          predicates:
            - Path=/api/userunits/**
            - Method=PUT,POST,DELETE
          filters:
            - StripPrefix=2
        - uri: ${INVITATION_URI:http://invitation:8101}
          predicates:
            - Path=/api/invitation/**
          filters:
            - StripPrefix=2
        - uri: ${AUTOMATION_DIRECTOR_URI:http://automation-director:8080}
          predicates:
            - Path=/api/automation-director/**
          filters:
            - StripPrefix=2
        - uri: ${AUTOMATION_USER_TASKS_URI:http://automation-user-tasks:8080}
          predicates:
            - Path=/api/automation-user-tasks/**
          filters:
            - StripPrefix=2
        - uri: ${PROJECTS_URL:http://projects:8055}
          predicates:
            - Path=/api/projects/**
          filters:
            - StripPrefix=2
        - uri: ${FUNCTIONS_URI:http://functions-api.faas:8080}
          predicates:
            - Path=/api/functions/**
          filters:
            - StripPrefix=2
        - uri: ${NOTEBOOKS_URI:http://notebooks-api.kns:8080}
          predicates:
            - Path=/api/notebooks/**
          filters:
            - StripPrefix=2
        - uri: ${KUBEFLOW_WORKSPACE_MANAGER_URI:http://kubeflow-workspace-manager-api.kwm:8080}
          predicates:
            - Path=/api/kubeflow-workspace-manager/**
          filters:
            - StripPrefix=2
        - uri: ${EECS_ENGINE_URI:http://eecs-engine:17001}
          predicates:
            - Path=/api/eecs/**
          filters:
            - StripPrefix=2
        - uri: ${NOTIFICATIONS_URI:http://notifications:8057}
          predicates:
            - Path=/api/notifications/**
          filters:
            - StripPrefix=2
        - uri: ${USER_MANAGEMENT_URI:http://usermanagement:8059}
          predicates:
            - Path=/api/user-management/public/users
            - Method=POST
          filters:
            - RewritePath=/api/user-management/public/users,/users
        - uri: ${USER_MANAGEMENT_URI:http://usermanagement:8059}
          predicates:
            - Path=/api/user-management/public/users/{email}/setup-password
            - Method=POST
          filters:
            - RewritePath=/api/user-management/public/users/(?<email>.*)/setup-password,/users/${email}/setup-password
        - uri: ${USER_MANAGEMENT_URI:http://usermanagement:8059}
          predicates:
            - Path=/api/user-management/**
          filters:
            - StripPrefix=2
        - uri: ${TEMPLATE_CATALOG_URL:http://templatecatalog:5001}
          predicates:
            - Path=/api/templatecatalog/**
          filters:
            - StripPrefix=2
        - uri: ${DOCUMENT_BUILDER_URI:http://document-builder-director:8080}
          predicates:
            - Path=/api/document_builder/**
          filters:
            - StripPrefix=2
        - uri: ${WORKFLOW_ORCHESTRATOR_URI:http://workflow-orchestrator:8080}
          predicates:
            - Path=/api/workflow-orchestrator/**
          filters:
            - StripPrefix=2
        # Data Fabric
        - uri: ${DATA_PROJECT_URI:http://data-project:8080}
          predicates:
            - Path=/api/data_project/**
          filters:
            - StripPrefix=2
        - uri: ${DATA_INGESTION_URI:http://data-ingestion:8080}
          predicates:
            - Path=/api/data_ingestion/**
          filters:
            - StripPrefix=2
        - uri: ${DATASET_URL:http://dataset:8066}
          predicates:
            - Path=/api/dataset/**
          filters:
            - StripPrefix=2
        - uri: ${SEQUENCE_URI:http://sequences:8012}
          predicates:
            - Path=/api/sequences/**
          filters:
            - StripPrefix=2
        - uri: ${ASSETS_URI:http://assets:8022}
          predicates:
            - Path=/api/asset/**
          filters:
            - StripPrefix=2
        - uri: ${EVENTS_URI:http://events:8024}
          predicates:
            - Path=/api/events/**
          filters:
            - StripPrefix=2
        - uri: ${TILE_URI:http://tile-service:8080}
          predicates:
            - Path=/api/tile/**
          filters:
            - StripPrefix=2
        - uri: ${TIME_SERIES_URI:http://timeseries:8026}
          predicates:
            - Path=/api/timeseries/**
          filters:
            - StripPrefix=2
        - uri: ${FILES_URL:http://files:8020}
          predicates:
            - Path=/api/files/**
          filters:
            - StripPrefix=2
        - uri: ${ASSET_SERVICE_URI:http://asset-service:8080}
          predicates:
            - Path=/api/df_asset/**
          filters:
            - StripPrefix=2
        - uri: ${SEQUENCE_SERVICE_URI:http://sequence-service:8080}
          predicates:
            - Path=/api/df_sequence/**
          filters:
            - StripPrefix=2
        - uri: ${EVENT_SERVICE_URI:http://event-service:8080}
          predicates:
            - Path=/api/df_event/**
          filters:
            - StripPrefix=2
        - uri: ${TIMESERIES_SERVICE_URI:http://timeseries-service:8080}
          predicates:
            - Path=/api/df_timeseries/**
          filters:
            - StripPrefix=2
        - uri: ${FILE_SERVICE_URI:http://file-service:8080}
          predicates:
            - Path=/api/df_file/**
          filters:
            - StripPrefix=2
        - uri: ${DATA_QUALITY_URI:http://data-quality-director:8080}
          predicates:
            - Path=/api/data_quality/**
          filters:
            - StripPrefix=2
        - uri: ${ENTITY_MATCHING_URI:http://entity-matching-director:8080}
          predicates:
            - Path=/api/entity_matching/**
          filters:
            - StripPrefix=2
        - uri: ${LABEL_URL:http://label:8077}
          predicates:
            - Path=/api/label/**
          filters:
            - StripPrefix=2
        - uri: ${RAW_URI:http://rawservice:8016}
          predicates:
            - Path=/api/raw/**
          filters:
            - StripPrefix=2
        - uri: ${RAW_SERVICE_URI:http://raw-service:8080}
          predicates:
            - Path=/api/df_raw/**
          filters:
            - StripPrefix=2
        - uri: ${TRANSFORMATION_URI:http://transformation-director:8080}
          predicates:
            - Path=/api/transformation/**
          filters:
            - StripPrefix=2
        - uri: ${DATA_TEMPLATING_URI:http://data-templating:8080}
          predicates:
            - Path=/api/data_templating/**
          filters:
            - StripPrefix=2
        - uri: ${DATA_GRAPH_URI:http://knowledge-graph:8080}
          predicates:
            - Path=/api/data_graph/**
          filters:
            - StripPrefix=2
        - uri: ${PATH_CATALOG_URI:http://path-catalog-service:8080}
          predicates:
            - Path=/api/path_catalog/**
          filters:
            - StripPrefix=2
        - uri: ${DATA_VIEW_URI:http://data-view:8080}
          predicates:
            - Path=/api/data_view/**
          filters:
            - StripPrefix=2
        - uri: ${PULSAR_BROKER_URI:http://pulsar-broker:8080}
          predicates:
            - Path=/api/ingestion/**
          filters:
            - StripPrefix=2
        - uri: ${DATA_PROFILER_URI:http://data-profiler-director:8080}
          predicates:
            - Path=/api/data_profiler/**
          filters:
            - StripPrefix=2
        - uri: ${METADATA_URI:http://metadata-api:8080}
          predicates:
            - Path=/api/metadata/**
          filters:
            - StripPrefix=2
        - uri: ${XR_PROJECTS_URI):http://xr-project:8080}
          predicates:
            - Path=/api/xr-project/**
          filters:
            - StripPrefix=2
        - uri: ${MODULE_MANAGEMENT_SERVICE_URI:http://modulemanagement:8066}
          predicates:
            - Path=/api/modulemanagement/**
          filters:
            - StripPrefix=2
        - uri: ${MARKETPLACE_SERVICE_URI:http://marketplace:8077}
          predicates:
            - Path=/api/marketplace/**
          filters:
            - StripPrefix=2
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${JWK_SET_URI}

directory-uri: ${DIRECTORY_URI:http://localhost:8086}
authorization-uri: ${AUTHORIZATION_URI:http://authorization:5999/v1}

server:
  port: ${SERVER_PORT:8052}
  error:
    include-message: always

logging:
  level:
    root: INFO
    org:
      apache:
        kafka: ERROR
      springframework:
        web: DEBUG
        boot:
          autoconfigure:
            web:
              reactive: DEBUG
        cloud:
          gateway: DEBUG
management:
  endpoints:
    web:
      exposure:
        include: "*"

allowed-routes:
  routes:
  ## actuator (healthcheck)
    - path: "/actuator/**"
      method: "GET"
    - path: "/actuator/health"
      method: "GET"
    - path: "/actuator/info"
      method: "GET"
  ## invitation
    - path: "/api/invitation/user/invitation/verify/**"
      method: "GET"
    - path: "/api/invitation/user/invitation/accept/**"
      method: "POST"
    - path: "/api/invitation/user/invitation/reject/**"
      method: "POST"
    ## fileservice
    - path: "/api/fileservice/file/download/**"
      method: "GET"
    ## marketplace
    - path: "/api/marketplace/marketplace-item"
      method: "GET"
    - path: "/api/marketplace/marketplace-item/logo/**"
      method: "POST"
    - path: "/api/marketplace/marketplace-item/image/**"
      method: "POST"
    - path: "/api/marketplace/marketplace-item/advanced-filters/**"
      method: "POST"
    - path: "/api/marketplace/marketplace-item/filtering-values/**"
      method: "GET"
    - path: "/api/marketplace/marketplace-item/comment/**"
      method: "GET"
    ## usermanagement
    - path: "/api/user-management/public/users"
      method: "POST"
    - path: "/api/user-management/public/users/*/setup-password"
      method: "POST"

diagnostics:
  service-name: "diagnostics"
  version: ${VERSION:0.0.1-dev}
  build-timestamp: ${BUILD_TIMESTAMP:1970-01-01T00:00:00Z}
  git-hash: ${GIT_HASH:unknown}
  git-branch: ${GIT_BRANCH:main}
  git-commit-timestamp: ${GIT_COMMIT_TIMESTAMP:1970-01-01T00:00:00Z}
