package com.fathom.services.apigateway.directory.repository;

import com.fathom.services.apigateway.directory.dto.DirectoryContentDto;
import com.fathom.services.apigateway.exception.ExternalServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Repository;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Repository
@Slf4j
class RemoteDirectoryRepository implements DirectoryRepository {

  private static final String DIRECTORY_ENDPOINT = "/directory/enabled";
  private final WebClient webClient;

  public RemoteDirectoryRepository(
      @Value("${directory-uri}") String directoryServiceUrl, WebClient.Builder webClientBuilder) {
    this.webClient = webClientBuilder.baseUrl(directoryServiceUrl).build();
  }

  @Override
  public Mono<DirectoryContentDto> getDirectory() {
    log.debug("Fetching current directory");
    return webClient
        .get()
        .uri(DIRECTORY_ENDPOINT)
        .accept(MediaType.APPLICATION_JSON)
        .retrieve()
        .bodyToMono(DirectoryContentDto.class)
        .switchIfEmpty(
            Mono.error(
                new ExternalServiceException("Unable to get directories from directory service")));
  }
}
