package com.fathom.services.apigateway.directory.dto;

import static java.util.stream.Collectors.toMap;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fathom.services.apigateway.directory.dto.method.EndpointMethodDto;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

public class DirectoryEndpointDto {
  private final String url;
  private final Map<String, EndpointMethodDto> endpointMethods;

  @JsonCreator
  public DirectoryEndpointDto(
      @JsonProperty("url") String url, @JsonProperty("methods") List<EndpointMethodDto> methods) {
    this.url = url;
    this.endpointMethods =
        Optional.ofNullable(methods)
            .map(Collection::stream)
            .map(s -> s.collect(toMap(EndpointMethodDto::getMethod, Function.identity())))
            .orElse(new HashMap<>());
  }

  public String getUrl() {
    return url;
  }

  public EndpointMethodDto getMethod(String methodName) {
    return endpointMethods.get(methodName);
  }
}
