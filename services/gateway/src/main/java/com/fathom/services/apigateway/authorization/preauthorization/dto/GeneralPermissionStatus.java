package com.fathom.services.apigateway.authorization.preauthorization.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public enum GeneralPermissionStatus {
  @JsonProperty("partially-allowed")
  PARTIALLY_ALLOWED,
  @JsonProperty("allowed")
  ALLOWED,
  @JsonProperty("denied")
  DENIED;

  public static boolean isAllowed(GeneralPermissionStatus status) {
    return status == ALLOWED || status == PARTIALLY_ALLOWED;
  }
}
