package com.fathom.services.apigateway.authorization.preauthorization;

import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationResponseDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.ResourceRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.frn.ExtractedVariable;
import com.fathom.services.apigateway.authorization.preauthorization.frn.FrnDefinitionUtil;
import com.fathom.services.apigateway.authorization.preauthorization.frn.variable.VariableExtractorFactory;
import com.fathom.services.apigateway.authorization.repository.AuthorizationRepository;
import com.fathom.services.apigateway.directory.DirectoryEndpointContext;
import com.fathom.services.apigateway.directory.ProxiedRequest;
import jakarta.validation.constraints.NotNull;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class PreAuthorizerImpl implements PreAuthorizer {

  private final AuthorizationRepository repository;

  public PreAuthorizerImpl(AuthorizationRepository repository) {
    this.repository = repository;
  }

  @Override
  public Mono<AuthorizationResponseDto> authorize(
      @NotNull ProxiedRequest proxiedRequest, @NotNull DirectoryEndpointContext context) {
    AuthorizationRequestCreator creator = new AuthorizationRequestCreator(proxiedRequest, context);
    AuthorizationRequestDto requestDto = creator.build();
    if (log.isDebugEnabled()) {
      log.debug(
          "[{}.{}] Authorization server request={}",
          requestDto.getOrg(),
          requestDto.getPrincipal(),
          requestDto);
    }
    return repository
        .authorize(requestDto)
        .doOnSuccess(
            responseDto ->
                log.debug(
                    "[{}.{}] Authorization server response={}",
                    responseDto.getOrg(),
                    responseDto.getPrincipal(),
                    responseDto));
  }

  private static class AuthorizationRequestCreator {
    private final String orgId;
    private final String client;
    private final String principal;
    private final Map<String, String> frnFormatByPermission;
    private final List<ExtractedVariable> extractedVariables;

    private AuthorizationRequestCreator(
        @NotNull ProxiedRequest proxiedRequest, @NotNull DirectoryEndpointContext context) {
      this.orgId = proxiedRequest.getOrganizationId();
      this.client = proxiedRequest.getClient();
      this.principal = proxiedRequest.getPrincipal();
      this.frnFormatByPermission = context.preAuthzPermissions();
      extractedVariables =
          context.getResourceVariables().stream()
              .map(
                  d ->
                      VariableExtractorFactory.get(d.getOrigin())
                          .extractVariable(proxiedRequest, d))
              .collect(Collectors.toList());
    }

    private AuthorizationRequestDto build() {
      AuthorizationRequestDto request = new AuthorizationRequestDto(principal, orgId);
      Set<ResourceRequestDto> resources = new HashSet<>();
      for (String permission : frnFormatByPermission.keySet()) {
        String frnFormat = frnFormatByPermission.get(permission);
        if (frnFormat != null) {
          String frn = FrnDefinitionUtil.build(frnFormat, orgId, client, extractedVariables);
          resources.add(ResourceRequestDto.from(frn, permission));
        }
      }
      request.setResourceAccessRequests(resources);
      return request;
    }
  }
}
