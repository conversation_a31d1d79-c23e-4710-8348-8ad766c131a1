package com.fathom.services.apigateway.authorization.preauthorization.dto;

import java.util.Collections;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@AllArgsConstructor
@Builder
public class ResourceRequestDto {
  private String frn;
  private Set<String> permissions;

  public static ResourceRequestDto from(String frn, String permission) {
    return new ResourceRequestDto(frn, Collections.singleton(permission));
  }
}
