package com.fathom.services.apigateway.authorization.preauthorization.frn.variable;

import com.fathom.services.apigateway.authorization.preauthorization.frn.ExtractedVariable;
import com.fathom.services.apigateway.directory.ProxiedRequest;
import com.fathom.services.apigateway.directory.dto.method.ResourceVariableDefinitionDto;

public interface ResourceVariableExtractor {
  ExtractedVariable extractVariable(
      ProxiedRequest proxiedRequest, ResourceVariableDefinitionDto resourceVariable);
}
