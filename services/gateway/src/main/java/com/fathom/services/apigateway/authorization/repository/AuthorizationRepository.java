package com.fathom.services.apigateway.authorization.repository;

import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationResponseDto;
import jakarta.annotation.Nonnull;
import reactor.core.publisher.Mono;

public interface AuthorizationRepository {
  @Nonnull
  Mono<AuthorizationResponseDto> authorize(AuthorizationRequestDto request);
}
