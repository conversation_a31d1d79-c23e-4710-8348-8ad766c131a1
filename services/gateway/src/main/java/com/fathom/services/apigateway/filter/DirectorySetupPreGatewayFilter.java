package com.fathom.services.apigateway.filter;


import com.fathom.services.apigateway.config.AllowedRoutesConfig;
import com.fathom.services.apigateway.directory.DirectoryService;
import com.fathom.services.apigateway.directory.ProxiedRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.filter.factory.rewrite.ModifyRequestBodyGatewayFilterFactory;
import org.springframework.core.Ordered;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@Component
@Slf4j
@RequiredArgsConstructor
public class DirectorySetupPreGatewayFilter implements GlobalFilter, Ordered {

  public static final String DIRECTORY_CONTEXT_ATTRIBUTE = "directoryContext";
  public static final String PROXIED_REQUEST_ATTRIBUTE = "proxiedRequest";
  public static final String ORGANIZATION_ID_HEADER = "x-organizationid";
  public static final String ORG_ID_ATTRIBUTE = "orgId";
  public static final String EMAIL_ATTRIBUTE = "email";
  public static final String SERVICE_NAME_ATTR = "serviceName";

  private final DirectoryService directoryService;
  private final ModifyRequestBodyGatewayFilterFactory factory;
  private final AllowedRoutesConfig allowedRoutesConfig;

  @Override
  public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

    List<String> modifiedRoutes = new ArrayList<>();

    for (String route :
        allowedRoutesConfig.getRoutes().stream().map(AllowedRoutesConfig.Route::getPath).toList()) {
      String modifiedRoute = route.replace("/**", "");
      modifiedRoutes.add(modifiedRoute);
    }

    for (String route : modifiedRoutes) {
      if (exchange.getRequest().getPath().pathWithinApplication().value().contains(route)) {
        return chain.filter(exchange);
      }
    }

    return ReactiveSecurityContextHolder.getContext()
        .map(SecurityContext::getAuthentication)
        .flatMap(a -> Mono.defer(() -> setDirectory(a, exchange, chain)));
  }

  private Mono<Void> setDirectory(
      Authentication authentication, ServerWebExchange exchange, GatewayFilterChain chain) {
    ServerHttpRequest request = exchange.getRequest();

    String orgId =
        Objects.isNull(request.getHeaders().getFirst(ORGANIZATION_ID_HEADER))
            ? request.getQueryParams().getFirst("organizationId")
            : request.getHeaders().getFirst(ORGANIZATION_ID_HEADER);

    String email =
        (String)
            ((JwtAuthenticationToken) authentication).getTokenAttributes().get(EMAIL_ATTRIBUTE);
    String endpointPath = request.getURI().getRawPath();
    String serviceName = exchange.getAttributeOrDefault(SERVICE_NAME_ATTR, "");

    Assert.notNull(orgId, ORGANIZATION_ID_HEADER + " header must be present");
    Assert.notNull(email, "Jwt does not contain email claim");

    exchange.getAttributes().put(ORG_ID_ATTRIBUTE, orgId);
    exchange.getAttributes().put(EMAIL_ATTRIBUTE, email);

    ProxiedRequest proxiedRequest =
        new ProxiedRequest(
            endpointPath,
            email,
            serviceName,
            orgId,
            request.getMethod(),
            request.getHeaders(),
            request.getQueryParams(),
            request.getURI().getRawQuery());

    exchange.getAttributes().put(PROXIED_REQUEST_ATTRIBUTE, proxiedRequest);
    return directoryService
        .createContext(proxiedRequest)
        .doOnSuccess(c -> exchange.getAttributes().put(DIRECTORY_CONTEXT_ATTRIBUTE, c))
        .flatMap(
            c -> {
              if (c.requiresRequestBody() && exchange.getRequest().getMethod() == HttpMethod.POST) {
                log.debug("Request {} requires json body for authorization", request.getURI());
                return extractRequestBodyFilter().filter(exchange, chain);
              } else {
                return chain.filter(exchange);
              }
            });
  }

  private GatewayFilter extractRequestBodyFilter() {
    return factory.apply(
        config ->
            config.setRewriteFunction(
                String.class,
                String.class,
                (serverWebExchange, requestBody) -> {
                  log.debug("Extracting json request body");
                  ProxiedRequest request =
                      serverWebExchange.getRequiredAttribute(PROXIED_REQUEST_ATTRIBUTE);
                  request.setRequest(requestBody);
                  return Mono.just(requestBody);
                }));
  }

  @Override
  public int getOrder() {
    return 0;
  }
}
