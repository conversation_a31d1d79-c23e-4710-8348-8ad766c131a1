package com.fathom.services.apigateway.config;

import static org.springframework.security.config.Customizer.withDefaults;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.oauth2.server.resource.web.server.authentication.ServerBearerTokenAuthenticationConverter;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.web.server.authentication.ServerAuthenticationConverter;

@Configuration
@EnableWebFluxSecurity
@Slf4j
@RequiredArgsConstructor
public class SecurityConfig {

  private final AllowedRoutesConfig allowedRoutesConfig;

  @Bean
  SecurityWebFilterChain apiHttpSecurity(ServerHttpSecurity http) {

    http.csrf(ServerHttpSecurity.CsrfSpec::disable)
        .authorizeExchange(
            authorize -> {
              // Loop through the allowed routes and configure permissions
              allowedRoutesConfig
                  .getRoutes()
                  .forEach(
                      route -> {
                        HttpMethod httpMethod = HttpMethod.valueOf(route.getMethod().toUpperCase());
                        authorize.pathMatchers(httpMethod, route.getPath()).permitAll();
                      });

              // Allow preflight requests
              authorize.pathMatchers(HttpMethod.OPTIONS, "/**").permitAll();

              // Secure all other endpoints
              authorize.anyExchange().authenticated();
            })
        .oauth2ResourceServer(
            resourceServer ->
                resourceServer.bearerTokenConverter(bearerTokenConverter()).jwt(withDefaults()));

    return http.build();
  }

  @Bean
  ServerAuthenticationConverter bearerTokenConverter() {
    ServerBearerTokenAuthenticationConverter converter =
        new ServerBearerTokenAuthenticationConverter();
    converter.setAllowUriQueryParameter(true);
    return converter;
  }
}
