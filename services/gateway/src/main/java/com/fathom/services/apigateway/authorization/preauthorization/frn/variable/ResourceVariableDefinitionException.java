package com.fathom.services.apigateway.authorization.preauthorization.frn.variable;

import com.fathom.services.apigateway.directory.DirectoryConfigException;
import com.fathom.services.apigateway.directory.dto.method.ResourceVariableDefinitionDto;

public class ResourceVariableDefinitionException extends DirectoryConfigException {

  private static final String DEFINITION_FORMAT =
      "Variable :[%s] with type [%s] has invalid value = [%s]";

  public ResourceVariableDefinitionException(
      ResourceVariableDefinitionDto resourceVariableDefinition) {
    super(
        DEFINITION_FORMAT.formatted(
            resourceVariableDefinition.getVariable(),
            resourceVariableDefinition.getOrigin().name(),
            resourceVariableDefinition.getValue()));
  }

  public ResourceVariableDefinitionException(
      ResourceVariableDefinitionDto resourceVariableDefinition, Throwable throwable) {
    super(
        DEFINITION_FORMAT.formatted(
            resourceVariableDefinition.getVariable(),
            resourceVariableDefinition.getOrigin().name(),
            resourceVariableDefinition.getValue()),
        throwable);
  }
}
