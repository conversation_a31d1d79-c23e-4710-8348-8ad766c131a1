package com.fathom.services.apigateway.directory;

import jakarta.annotation.Nullable;
import lombok.Builder;
import lombok.Getter;
import org.springframework.http.HttpMethod;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

@Getter
@Builder
public class ProxiedRequest {
  private final String path;
  private final String client;
  private final String principal;
  private final String service;
  private final String organizationId;
  private final HttpMethod httpMethod;
  private final MultiValueMap<String, String> headers;
  private final MultiValueMap<String, String> queryParams;
  private final String query;
  private String jsonRequestBody;

  public ProxiedRequest(
      String path,
      @Nullable String client,
      String principal,
      String service,
      String organizationId,
      HttpMethod httpMethod,
      MultiValueMap<String, String> headers,
      MultiValueMap<String, String> queryParams,
      @Nullable String query,
      @Nullable String jsonRequestBody) {
    this.path = path;
    this.client = client;
    this.principal = principal;
    this.service = service;
    this.organizationId = organizationId;
    this.httpMethod = httpMethod;
    this.headers = headers;
    this.queryParams = queryParams;
    this.query = query;
    this.jsonRequestBody = jsonRequestBody;
  }

  public ProxiedRequest(
      String path,
      String principal,
      String service,
      String organizationId,
      HttpMethod httpMethod,
      MultiValueMap<String, String> headers,
      MultiValueMap<String, String> queryParams,
      @Nullable String query) {
    this(
        path,
        null,
        principal,
        service,
        organizationId,
        httpMethod,
        headers,
        queryParams,
        query,
        null);
  }

  public void setRequest(String jsonBody) {
    this.jsonRequestBody = jsonBody;
  }

  public MultiValueMap<String, String> getQueryParams() {
    if (queryParams == null) {
      return new LinkedMultiValueMap<>();
    }
    return queryParams;
  }

  public String getPathWithQuery() {
    return path + "?" + query;
  }

  @Override
  public String toString() {
    return "ProxiedRequest{"
        + "path='"
        + path
        + '\''
        + ", client='"
        + client
        + '\''
        + ", principal='"
        + principal
        + '\''
        + ", service='"
        + service
        + '\''
        + ", organizationId='"
        + organizationId
        + '\''
        + '}';
  }
}
