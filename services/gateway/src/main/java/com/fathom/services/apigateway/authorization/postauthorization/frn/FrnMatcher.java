package com.fathom.services.apigateway.authorization.postauthorization.frn;

import static com.fathom.services.apigateway.authorization.preauthorization.frn.FrnDefinitionUtil.FRN_DELIMITER;
import static com.fathom.services.apigateway.authorization.preauthorization.util.PathUtil.PATH_DELIMITER;
import static com.fathom.services.apigateway.authorization.preauthorization.util.PathUtil.isPathEqualsDefinition;

import jakarta.annotation.Nonnull;
import java.util.stream.Stream;

public final class FrnMatcher {

  private static final String FRN_PREFIX = "frn";
  private static final int RESOURCE_ID_TERM_INDEX = 4;
  // matches ${variable_name: Some Description}
  private static final String RESOURCE_VARIABLE_DEFINITION_REGEX =
      "\\$\\{([a-zA-Z0-9_ ]*):[a-zA-Z0-9_ ]*}";
  // replacement for ${variable_name}
  private static final String RESOURCE_VARIABLE_NAME_REPLACEMENT = "\\$\\{$1\\}";

  private FrnMatcher() {}

  public static boolean isMatch(@Nonnull String frn, @Nonnull String frnFormat) {
    String[] frnTerms = frn.split(FRN_DELIMITER);
    String[] frnFormatTerms = frnFormat.split(FRN_DELIMITER, RESOURCE_ID_TERM_INDEX + 1);
    if (frnFormatTerms.length != frnTerms.length) {
      return false;
    }
    return Stream.of(
            matchPrefixTerm(frnTerms),
            matchServiceNameTerm(frnTerms, frnFormatTerms),
            matchResourcePathTerm(frnTerms, frnFormatTerms))
        .reduce(Boolean::logicalAnd)
        .orElse(false);
  }

  private static boolean matchPrefixTerm(String[] frn) {
    return frn[0].equals(FRN_PREFIX);
  }

  private static boolean matchServiceNameTerm(String[] frn, String[] frnFormat) {
    return frnFormat[2].equals(frn[2]);
  }

  private static boolean matchResourcePathTerm(String[] frnTerms, String[] frnFormatTerms) {
    String[] frnResourceTermParts = frnTerms[RESOURCE_ID_TERM_INDEX].split(PATH_DELIMITER);
    String frnFormatResourceTerm = frnFormatTerms[RESOURCE_ID_TERM_INDEX];
    String resourcePathFormat = removeResourceVariableDescriptions(frnFormatResourceTerm);
    String[] frnFormatResourceTermParts = resourcePathFormat.split(PATH_DELIMITER);
    return isPathEqualsDefinition(frnResourceTermParts, frnFormatResourceTermParts);
  }

  // returns path to the resource in format /asset/${asset_name}/property/${property_name}
  private static String removeResourceVariableDescriptions(String frnFormatResourceTerm) {
    return frnFormatResourceTerm.replaceAll(
        RESOURCE_VARIABLE_DEFINITION_REGEX, RESOURCE_VARIABLE_NAME_REPLACEMENT);
  }
}
