package com.fathom.services.apigateway.authorization.preauthorization.endpoint;

import static com.fathom.services.apigateway.authorization.preauthorization.util.PathUtil.PATH_DELIMITER;
import static com.fathom.services.apigateway.authorization.preauthorization.util.PathUtil.isPathEqualsDefinition;

import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;

@Component
public class EndpointMatcherImpl implements EndpointMatcher {

  private static final Pattern QUERY_PATTERN = Pattern.compile("([^&=]+)(=?)([^&]+)?");

  // Matches path to endpoint
  // E.g. path == /assets/MF-104 => /assets/${assetName}
  @Nullable
  @Override
  public String matchEndpoint(Set<String> endpoints, String path) {
    if (path == null || endpoints == null || endpoints.isEmpty()) {
      return null;
    }
    // simple endpoint match
    if (endpoints.contains(path)) {
      return path;
    }
    return matchComplexEndpoint(endpoints, path);
  }

  private String matchComplexEndpoint(Set<String> endpoints, String path) {
    Endpoint currentPath = new Endpoint(path);
    Map<Boolean, Set<Endpoint>> separatedEndpoints =
        endpoints.stream()
            .map(Endpoint::new)
            .collect(Collectors.partitioningBy(Endpoint::hasQueryParams, Collectors.toSet()));

    String definition =
        matchPathToDefinition(
            separatedEndpoints.get(true), currentPath, currentPath::matchQueryParams);
    if (definition != null) {
      return definition;
    }

    Set<Endpoint> endpointWithoutParams = separatedEndpoints.get(false);
    // match with skipping query params checking
    return matchPathToDefinition(endpointWithoutParams, currentPath, e -> true);
  }

  private String matchPathToDefinition(
      Set<Endpoint> endpoints, Endpoint currentEndpoint, Predicate<Endpoint> queryParamsPredicate) {
    final String[] pathParts = currentEndpoint.path.split(PATH_DELIMITER);
    return endpoints.stream()
        .filter(endpoint -> isPathEqualsDefinition(pathParts, endpoint.path.split(PATH_DELIMITER)))
        .filter(queryParamsPredicate)
        .findAny()
        .map(Endpoint::toString)
        .orElse(null);
  }

  @Data
  private static class Endpoint {
    String path;
    MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();

    Endpoint(String endpointPath) {
      String[] endpointParts = endpointPath.split("\\?");
      this.path = endpointParts[0];
      if (endpointParts.length > 1) {
        String queryParams = endpointParts[1];
        this.queryParams = initQueryParams(queryParams);
      }
    }

    boolean hasQueryParams() {
      return !queryParams.isEmpty();
    }

    @Override
    public String toString() {
      if (queryParams.isEmpty()) {
        return path;
      }
      StringBuilder stringBuilder = new StringBuilder();
      queryParams.forEach(
          (key, list) ->
              list.forEach(
                  paramValue -> {
                    stringBuilder.append(key).append("=").append(paramValue);
                  }));
      return path + "?" + stringBuilder.toString();
    }

    boolean matchQueryParams(Endpoint endpoint) {
      if (!this.queryParams.keySet().containsAll(endpoint.queryParams.keySet())) {
        return false;
      }
      return matchQueryParamValues(endpoint);
    }

    boolean matchQueryParamValues(Endpoint endpoint) {
      for (Map.Entry<String, List<String>> param : this.queryParams.entrySet()) {
        String paramName = param.getKey();
        if (endpoint.queryParams.containsKey(paramName)) {
          if (!endpoint.queryParams.get(paramName).equals(param.getValue())) {
            return false;
          }
        }
      }
      return true;
    }

    protected MultiValueMap<String, String> initQueryParams(String query) {
      MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
      if (query != null) {
        Matcher matcher = QUERY_PATTERN.matcher(query);
        while (matcher.find()) {
          String name = matcher.group(1);
          String eq = matcher.group(2);
          String value = matcher.group(3);
          value = (value != null ? value : (StringUtils.hasLength(eq) ? "" : null));
          queryParams.add(name, value);
        }
      }
      return queryParams;
    }
  }
}
