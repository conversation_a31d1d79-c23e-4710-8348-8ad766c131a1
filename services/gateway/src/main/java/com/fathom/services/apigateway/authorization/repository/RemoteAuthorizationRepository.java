package com.fathom.services.apigateway.authorization.repository;

import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationResponseDto;
import com.fathom.services.apigateway.exception.ExternalServiceException;
import jakarta.annotation.Nonnull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Repository;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Repository
class RemoteAuthorizationRepository implements AuthorizationRepository {

  private static final String AUTHORIZATION_ENDPOINT = "/authorize";
  private final WebClient webClient;

  public RemoteAuthorizationRepository(
      @Value("${authorization-uri}") String authorizationServiceUrl,
      WebClient.Builder webClientBuilder) {
    this.webClient = webClientBuilder.baseUrl(authorizationServiceUrl).build();
  }

  @Nonnull
  @Override
  public Mono<AuthorizationResponseDto> authorize(AuthorizationRequestDto request) {
    return webClient
        .post()
        .uri(AUTHORIZATION_ENDPOINT)
        .contentType(MediaType.APPLICATION_JSON)
        .bodyValue(request)
        .retrieve()
        .bodyToMono(AuthorizationResponseDto.class)
        .switchIfEmpty(
            Mono.error(
                new ExternalServiceException("Unable to get response from authorization server")));
  }
}
