package com.fathom.services.apigateway.directory.dto.method.postauthorization;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum FieldProtectionAction {
  ALLOW,
  DENY;

  @Json<PERSON>reator
  public static FieldProtectionAction fromValue(String value) {
    if (value == null) {
      return DENY;
    }
    try {
      return FieldProtectionAction.valueOf(value.toUpperCase());
    } catch (IllegalArgumentException e) {
      return DENY;
    }
  }

  @JsonValue
  public String getValue() {
    return this.name().toLowerCase();
  }
}
