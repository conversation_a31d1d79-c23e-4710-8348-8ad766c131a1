package com.fathom.services.apigateway.authorization.postauthorization.frn;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;

public final class FrnExtractor {

  public static final String FRN_FIELD_NAME = "frn";

  private FrnExtractor() {}

  public static Set<String> extract(JsonNode jsonNode) {
    return extractFrnFromNode(jsonNode);
  }

  private static Set<String> extractFrnFromNode(JsonNode root) {
    Set<String> frns = new LinkedHashSet<>();
    if (root.isObject()) {
      extractFrnFromObjectNode(root, frns);
    } else if (root.isArray()) {
      extractFrnFromArrayNode((ArrayNode) root, frns);
    }
    return frns;
  }

  private static void extractFrnFromObjectNode(JsonNode root, Set<String> frns) {
    JsonNode frnNode = root.get(FRN_FIELD_NAME);
    if (frnNode != null) {
      frns.add(frnNode.textValue());
    }
    Iterator<String> fieldNames = root.fieldNames();
    while (fieldNames.hasNext()) {
      String fieldName = fieldNames.next();
      JsonNode fieldValue = root.get(fieldName);
      if (isNodeHasInnerNodes(fieldValue)) {
        frns.addAll(extractFrnFromNode(fieldValue));
      }
    }
  }

  private static void extractFrnFromArrayNode(ArrayNode arrayNode, Set<String> frns) {
    for (int i = 0; i < arrayNode.size(); i++) {
      JsonNode arrayElement = arrayNode.get(i);
      frns.addAll(extractFrnFromNode(arrayElement));
    }
  }

  private static boolean isNodeHasInnerNodes(JsonNode jsonNode) {
    return !jsonNode.isValueNode();
  }
}
