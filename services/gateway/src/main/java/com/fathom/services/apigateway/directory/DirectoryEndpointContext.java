package com.fathom.services.apigateway.directory;

import com.fathom.services.apigateway.authorization.postauthorization.frn.FrnMatcher;
import com.fathom.services.apigateway.directory.dto.DirectoryDto;
import com.fathom.services.apigateway.directory.dto.method.EndpointMethodDto;
import com.fathom.services.apigateway.directory.dto.method.ResourceVariableDefinitionDto;
import com.fathom.services.apigateway.directory.dto.method.postauthorization.FieldsProtectorDto;
import com.fathom.services.apigateway.directory.dto.method.postauthorization.PostAuthorizationActionDto;
import jakarta.annotation.Nonnull;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public class DirectoryEndpointContext {
  private final DirectoryDto directory;
  private final Map<String, String> preAuthorizeFrnFormatByPermission;
  private final Map<String, String> postAuthorizeFrnFormatByPermission;
  private final List<ResourceVariableDefinitionDto> definitions;
  private final Map<String, PostAuthorizationActionDto> postAuthorizationActions;

  public DirectoryEndpointContext(
      @Nonnull DirectoryDto directory, @Nonnull EndpointMethodDto endpointMethod) {
    this.directory = directory;
    this.definitions = endpointMethod.getResourceVariables();
    this.postAuthorizationActions = endpointMethod.getPostAuthorizationActions();
    Map<String, String> preAuthorizeFrnFormatByPermission = new HashMap<>();
    Optional.ofNullable(endpointMethod.getPermissions())
        .ifPresent(
            permissions ->
                permissions.forEach(
                    p -> preAuthorizeFrnFormatByPermission.put(p, directory.getFrnFormat(p))));

    Map<String, String> postAuthorizeFrnFormatByPermission = new HashMap<>();

    endpointMethod
        .getPostAuthorizationActions()
        .forEach((k, v) -> postAuthorizeFrnFormatByPermission.put(k, directory.getFrnFormat(k)));

    this.preAuthorizeFrnFormatByPermission = preAuthorizeFrnFormatByPermission;
    this.postAuthorizeFrnFormatByPermission = postAuthorizeFrnFormatByPermission;
  }

  public static DirectoryEndpointContext empty() {
    return new DirectoryEndpointContext(
        new DirectoryDto(null, new HashMap<>(), new HashMap<>()), new EndpointMethodDto());
  }

  @Nonnull
  public DirectoryDto getDirectory() {
    return directory;
  }

  @Nonnull
  public Map<String, String> preAuthzPermissions() {
    return preAuthorizeFrnFormatByPermission;
  }

  @Nonnull
  public List<ResourceVariableDefinitionDto> getResourceVariables() {
    return definitions;
  }

  public boolean requiresRequestBody() {
    return definitions != null
        && definitions.stream().anyMatch(ResourceVariableDefinitionDto::requestBodyOrigin);
  }

  public boolean requiresPreAuthorization() {
    return this.preAuthorizeFrnFormatByPermission != null
        && !this.preAuthorizeFrnFormatByPermission.isEmpty();
  }

  public boolean requiresPostAuthorization() {
    return this.postAuthorizationActions != null && !postAuthorizationActions.isEmpty();
  }

  public Set<String> getPostAuthorizationPermissions() {
    return postAuthorizationActions.keySet();
  }

  public List<FieldsProtectorDto> getProtectorsByPermission(String permission) {
    return Optional.ofNullable(postAuthorizationActions.get(permission))
        .map(PostAuthorizationActionDto::getProtectors)
        .orElse(new ArrayList<>());
  }

  public Optional<String> getPostAuthzFrnFormat(String permission) {
    return Optional.ofNullable(postAuthorizeFrnFormatByPermission.get(permission));
  }

  public Optional<String> getPostAuthPermission(String frn) {
    for (String permission : this.getPostAuthorizationPermissions()) {
      Boolean frnFormatByPermission =
          this.getPostAuthzFrnFormat(permission)
              .map(format -> FrnMatcher.isMatch(frn, format))
              .orElse(false);
      if (frnFormatByPermission) {
        return Optional.of(permission);
      }
    }
    return Optional.empty();
  }

  public Map<String, String> getPermissionByFrn(Set<String> frns) {
    Map<String, String> frnPermissionMap = new HashMap<>();
    for (String frn : frns) {
      this.getPostAuthPermission(frn)
          .ifPresent(permission -> frnPermissionMap.put(frn, permission));
    }
    return frnPermissionMap;
  }
}
