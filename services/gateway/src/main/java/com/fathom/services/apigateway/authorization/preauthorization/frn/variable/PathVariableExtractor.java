package com.fathom.services.apigateway.authorization.preauthorization.frn.variable;

import com.fathom.services.apigateway.authorization.preauthorization.frn.ExtractedVariable;
import com.fathom.services.apigateway.directory.DirectoryConfigException;
import com.fathom.services.apigateway.directory.ProxiedRequest;
import com.fathom.services.apigateway.directory.dto.method.ResourceVariableDefinitionDto;

class PathVariableExtractor implements ResourceVariableExtractor {

  private static final String PATH_DELIMITER = "/";

  @Override
  public ExtractedVariable extractVariable(
      ProxiedRequest proxiedRequest, ResourceVariableDefinitionDto resourceVariable)
      throws DirectoryConfigException {
    try {
      String partIndex =
          resourceVariable.getValue(); // value represents index of the part in the path
      String endpointPath = proxiedRequest.getPath();
      String endpointDefinition =
          endpointPath.startsWith(PATH_DELIMITER) ? endpointPath.substring(1) : endpointPath;
      String[] pathParts = endpointDefinition.split(PATH_DELIMITER);
      String part = pathParts[Integer.parseInt(partIndex) - 1];
      return new ExtractedVariable(resourceVariable.getVariable(), part);
    } catch (RuntimeException e) {
      throw new ResourceVariableDefinitionException(resourceVariable, e);
    }
  }
}
