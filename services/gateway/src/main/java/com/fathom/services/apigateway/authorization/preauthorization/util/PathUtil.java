package com.fathom.services.apigateway.authorization.preauthorization.util;

public final class PathUtil {
  public static final String PATH_DELIMITER = "/";
  private static final String VARIABLE_INDICATOR = "${";

  private PathUtil() {}

  // matches path to the resource to path definition with skipping ${variableName} parts
  public static boolean isPathEqualsDefinition(String[] path, String[] definition) {
    if (definition.length != path.length) {
      return false;
    }
    for (int i = 0; i < definition.length; i++) {
      if (definition[i].startsWith(VARIABLE_INDICATOR)) {
        continue;
      }
      if (!definition[i].equals(path[i])) return false;
    }
    return true;
  }
}
