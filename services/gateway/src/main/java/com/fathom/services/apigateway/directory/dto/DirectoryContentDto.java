package com.fathom.services.apigateway.directory.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;

@Data
public class DirectoryContentDto {
  private Map<String, DirectoryDto> directoryByName = new HashMap<>();

  public DirectoryContentDto(@JsonProperty("content") List<DirectoryDto> directories) {
    if (directories != null) {
      this.directoryByName =
          directories.stream()
              .collect(Collectors.toMap(DirectoryDto::getName, Function.identity()));
    }
  }

  public DirectoryDto getDirectory(String name) {
    return directoryByName.get(name);
  }

  public boolean contains(String name) {
    return directoryByName.containsKey(name);
  }
}
