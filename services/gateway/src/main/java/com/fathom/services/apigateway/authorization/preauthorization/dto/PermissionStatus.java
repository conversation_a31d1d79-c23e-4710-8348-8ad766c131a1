package com.fathom.services.apigateway.authorization.preauthorization.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum PermissionStatus {
  ALLOWED,
  DENIED;

  @JsonCreator
  public static PermissionStatus fromValue(String s) {
    return PermissionStatus.valueOf(s.toUpperCase());
  }

  @JsonValue
  public String getValue() {
    return this.name().toLowerCase();
  }
}
