package com.fathom.services.apigateway.authorization.preauthorization.dto;

import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@AllArgsConstructor
@Builder
public class AuthorizationRequestDto {
  private String principal;
  private String org;
  private Set<ResourceRequestDto> resourceAccessRequests;

  public AuthorizationRequestDto(String principal, String org) {
    this.principal = principal;
    this.org = org;
  }
}
