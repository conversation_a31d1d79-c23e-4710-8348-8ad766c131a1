package com.fathom.services.apigateway.directory.dto.method;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum ResourceVariableOriginType {
  HEADER,
  PATH,
  QUERY,
  BODY;

  @JsonCreator
  public static ResourceVariableOriginType fromValue(String s) {
    return ResourceVariableOriginType.valueOf(s.toUpperCase());
  }

  @JsonValue
  public String getValue() {
    return this.name().toLowerCase();
  }
}
