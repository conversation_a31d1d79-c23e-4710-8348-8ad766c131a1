package com.fathom.services.apigateway.authorization.preauthorization.frn.variable;

import com.fathom.services.apigateway.directory.dto.method.ResourceVariableOriginType;
import java.util.HashMap;
import java.util.Map;

public final class VariableExtractorFactory {

  private static final Map<ResourceVariableOriginType, ResourceVariableExtractor>
      VARIABLE_EXTRACTORS = new HashMap<>();

  static {
    VARIABLE_EXTRACTORS.put(ResourceVariableOriginType.HEADER, new HeaderVariableExtractor());
    VARIABLE_EXTRACTORS.put(ResourceVariableOriginType.PATH, new PathVariableExtractor());
    VARIABLE_EXTRACTORS.put(ResourceVariableOriginType.QUERY, new QueryParamVariableExtractor());
    VARIABLE_EXTRACTORS.put(ResourceVariableOriginType.BODY, new JsonHttpBodyVariableExtractor());
  }

  private VariableExtractorFactory() {}

  public static ResourceVariableExtractor get(ResourceVariableOriginType type) {
    return VARIABLE_EXTRACTORS.get(type);
  }
}
