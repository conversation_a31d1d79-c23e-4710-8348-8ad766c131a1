package com.fathom.services.apigateway;

import com.fathom.diagnostics.configuration.DiagnosticProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@EnableConfigurationProperties(DiagnosticProperties.class)
@ComponentScan(basePackages = {"com.fathom.services.gateway"})
public class ApiGatewayApplication {
  public static void main(String[] args) {
    System.setProperty("jdk.tls.client.protocols", "TLSv1.2");
    SpringApplication.run(ApiGatewayApplication.class, args);
  }
}
