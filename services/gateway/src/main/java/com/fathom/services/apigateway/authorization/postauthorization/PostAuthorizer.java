package com.fathom.services.apigateway.authorization.postauthorization;

import com.fathom.services.apigateway.authorization.postauthorization.protector.JsonProtectionSchema;
import com.fathom.services.apigateway.directory.DirectoryEndpointContext;
import com.fathom.services.apigateway.directory.ProxiedRequest;
import java.util.List;
import reactor.core.publisher.Mono;

public interface PostAuthorizer {

  Mono<List<JsonProtectionSchema>> buildSchemas(
      String jsonBody, ProxiedRequest proxiedRequest, DirectoryEndpointContext context);
}
