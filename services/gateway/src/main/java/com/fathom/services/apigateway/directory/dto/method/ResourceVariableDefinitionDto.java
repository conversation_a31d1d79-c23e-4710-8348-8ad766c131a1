package com.fathom.services.apigateway.directory.dto.method;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class ResourceVariableDefinitionDto {
  private final ResourceVariableOriginType origin;
  private final String value;
  private final String variable;

  @JsonCreator
  public ResourceVariableDefinitionDto(
      @JsonProperty("origin") ResourceVariableOriginType origin,
      @JsonProperty("value") String value,
      @JsonProperty("resourceVariable") String variable) {
    this.origin = origin;
    this.value = value;
    this.variable = variable;
  }

  public ResourceVariableOriginType getOrigin() {
    return origin;
  }

  public String getValue() {
    return value;
  }

  public String getVariable() {
    return variable;
  }

  public boolean requestBodyOrigin() {
    return origin == ResourceVariableOriginType.BODY;
  }
}
