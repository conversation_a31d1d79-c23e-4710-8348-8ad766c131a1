package com.fathom.services.apigateway.authorization.preauthorization.frn.variable;

import com.fathom.services.apigateway.authorization.preauthorization.frn.ExtractedVariable;
import com.fathom.services.apigateway.directory.ProxiedRequest;
import com.fathom.services.apigateway.directory.dto.method.ResourceVariableDefinitionDto;
import java.util.Optional;
import org.springframework.util.MultiValueMap;

class QueryParamVariableExtractor implements ResourceVariableExtractor {

  @Override
  public ExtractedVariable extractVariable(
      ProxiedRequest proxiedRequest, ResourceVariableDefinitionDto resourceVariable) {
    MultiValueMap<String, String> queryParams = proxiedRequest.getQueryParams();
    return Optional.ofNullable(queryParams.getFirst(resourceVariable.getValue()))
        .map(
            queryParamValue ->
                new ExtractedVariable(resourceVariable.getVariable(), queryParamValue))
        .orElseThrow(() -> new ResourceVariableDefinitionException(resourceVariable));
  }
}
