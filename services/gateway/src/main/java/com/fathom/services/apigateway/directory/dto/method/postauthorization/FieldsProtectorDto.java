package com.fathom.services.apigateway.directory.dto.method.postauthorization;

import com.fathom.services.apigateway.authorization.postauthorization.protector.mask.MaskFactory;
import com.fathom.services.apigateway.authorization.postauthorization.protector.mask.ProtectionMask;
import java.util.Set;
import lombok.Data;

@Data
public class FieldsProtectorDto {
  private static final String ALL_FIELDS_IDENTIFIER = "*";
  private MaskType maskType;
  private FieldProtectionAction action = FieldProtectionAction.DENY;
  private Set<String> fields;

  public boolean isAllFieldsDeclared() {
    return fields.contains(ALL_FIELDS_IDENTIFIER);
  }

  public ProtectionMask getMask() {
    return MaskFactory.mask(this.maskType);
  }

  public boolean isFieldDeclared(String fieldName) {
    return fields.contains(fieldName);
  }
}
