package com.fathom.services.apigateway.authorization.preauthorization;

import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationResponseDto;
import com.fathom.services.apigateway.directory.DirectoryEndpointContext;
import com.fathom.services.apigateway.directory.ProxiedRequest;
import jakarta.validation.constraints.NotNull;
import reactor.core.publisher.Mono;

public interface PreAuthorizer {
  Mono<AuthorizationResponseDto> authorize(
      @NotNull ProxiedRequest proxiedRequest, @NotNull DirectoryEndpointContext context);
}
