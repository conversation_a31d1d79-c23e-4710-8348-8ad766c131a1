package com.fathom.services.apigateway.authorization.postauthorization;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fathom.services.apigateway.authorization.postauthorization.frn.FrnExtractor;
import com.fathom.services.apigateway.authorization.postauthorization.protector.JsonProtectionSchema;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationResponseDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.ResourceRequestDto;
import com.fathom.services.apigateway.authorization.repository.AuthorizationRepository;
import com.fathom.services.apigateway.directory.DirectoryEndpointContext;
import com.fathom.services.apigateway.directory.ProxiedRequest;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class PostAuthorizerImpl implements PostAuthorizer {

  private final ObjectMapper objectMapper;
  private final AuthorizationRepository repository;

  public PostAuthorizerImpl(ObjectMapper objectMapper, AuthorizationRepository repository) {
    this.objectMapper = objectMapper;
    this.repository = repository;
  }

  @Override
  public Mono<List<JsonProtectionSchema>> buildSchemas(
      String jsonBody, ProxiedRequest proxiedRequest, DirectoryEndpointContext context) {
    Set<String> frns = extractFrns(jsonBody);
    if (frns.isEmpty()) {
      return Mono.empty();
    }
    Map<String, String> frnPermission = context.getPermissionByFrn(frns);
    return getDeniedFrns(frnPermission, proxiedRequest)
        .map(
            deniedFrns -> {
              Map<String, String> denied =
                  frnPermission.keySet().stream()
                      .filter(deniedFrns::contains)
                      .collect(Collectors.toMap(Function.identity(), frnPermission::get));
              return buildProtectionSchemas(denied, context);
            });
  }

  private Set<String> extractFrns(String jsonBody) {
    try {
      JsonNode jsonNode = objectMapper.readTree(jsonBody);
      return FrnExtractor.extract(jsonNode);
    } catch (JsonProcessingException e) {
      log.error("Cannot extract frns from json response", e);
      return new HashSet<>();
    }
  }

  private Mono<Set<String>> getDeniedFrns(
      Map<String, String> frnPermissionMap, ProxiedRequest proxiedRequest) {
    Set<ResourceRequestDto> requests =
        frnPermissionMap.entrySet().stream()
            .map(
                entry ->
                    new ResourceRequestDto(entry.getKey(), Collections.singleton(entry.getValue())))
            .collect(Collectors.toSet());
    AuthorizationRequestDto request =
        new AuthorizationRequestDto(
            proxiedRequest.getPrincipal(), proxiedRequest.getOrganizationId(), requests);
    log.debug(
        "[{}.{}] Post authorization request ={}",
        proxiedRequest.getHttpMethod(),
        proxiedRequest.getPath(),
        request);

    return repository.authorize(request).map(AuthorizationResponseDto::getDeniedFrns);
  }

  private List<JsonProtectionSchema> buildProtectionSchemas(
      Map<String, String> frnPermissionMap, DirectoryEndpointContext context) {
    return frnPermissionMap.entrySet().stream()
        .map(
            entry ->
                new JsonProtectionSchema(
                    entry.getKey(), context.getProtectorsByPermission(entry.getValue())))
        .collect(Collectors.toList());
  }
}
