package com.fathom.services.apigateway.directory.dto.method;

import com.fasterxml.jackson.annotation.JsonSetter;
import com.fathom.services.apigateway.directory.dto.method.postauthorization.PostAuthorizationActionDto;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;

@Data
public class EndpointMethodDto {
  private String method;
  private Set<String> permissions;
  private List<ResourceVariableDefinitionDto> resourceVariables = new ArrayList<>();
  private Map<String, PostAuthorizationActionDto> postAuthorizationActions = new HashMap<>();

  @JsonSetter
  public void setResourceVariables(List<ResourceVariableDefinitionDto> resourceVariables) {
    if (resourceVariables != null) {
      this.resourceVariables = resourceVariables;
    }
  }

  @JsonSetter("postAuthorization")
  public void setPostAuthorization(List<PostAuthorizationActionDto> postAuthorizationActions) {
    if (postAuthorizationActions != null) {
      this.postAuthorizationActions =
          postAuthorizationActions.stream()
              .collect(
                  Collectors.toMap(PostAuthorizationActionDto::getPermission, Function.identity()));
    }
  }
}
