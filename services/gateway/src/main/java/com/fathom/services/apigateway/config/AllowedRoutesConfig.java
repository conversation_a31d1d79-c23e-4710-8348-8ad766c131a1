package com.fathom.services.apigateway.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@Data
@ConfigurationProperties(prefix = "allowed-routes")
public class AllowedRoutesConfig {

  private List<Route> routes;

  @Data
  public static class Route {
    private String path;
    private String method;
  }
}
