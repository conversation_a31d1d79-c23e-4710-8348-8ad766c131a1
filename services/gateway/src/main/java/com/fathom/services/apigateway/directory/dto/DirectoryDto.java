package com.fathom.services.apigateway.directory.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DirectoryDto {
  private final String name;
  private final Map<String, String> frnFormatByPermission;
  private final Map<String, DirectoryEndpointDto> endpointByUrl;

  public DirectoryDto(
      String name,
      Map<String, String> frnFormatByPermission,
      Map<String, DirectoryEndpointDto> endpointByUrl) {
    this.name = name;
    this.frnFormatByPermission = Optional.ofNullable(frnFormatByPermission).orElse(new HashMap<>());
    this.endpointByUrl = Optional.ofNullable(endpointByUrl).orElse(new HashMap<>());
  }

  @NotNull
  public String getFrnFormat(String permission) {
    return frnFormatByPermission.get(permission);
  }

  public String getName() {
    return name;
  }

  public Map<String, DirectoryEndpointDto> getUrlEndpoints() {
    return endpointByUrl;
  }
}
