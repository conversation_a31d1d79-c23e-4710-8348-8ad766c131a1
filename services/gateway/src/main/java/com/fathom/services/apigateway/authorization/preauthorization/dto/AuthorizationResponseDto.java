package com.fathom.services.apigateway.authorization.preauthorization.dto;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AuthorizationResponseDto {
  private String principal;
  private String org;
  private List<ResourceResponseDto> resourceAccesses;

  public boolean isDenied() {
    if (resourceAccesses == null || resourceAccesses.isEmpty()) {
      return true;
    }
    return resourceAccesses.stream()
        .noneMatch(resource -> GeneralPermissionStatus.isAllowed(resource.getStatus()));
  }

  public Set<String> getPermissions() {
    return Optional.ofNullable(resourceAccesses)
        .map(
            resourceAccesses ->
                resourceAccesses.stream()
                    .flatMap(resource -> resource.getPermissions().stream())
                    .map(PermissionResponseDto::getPermission)
                    .collect(Collectors.toSet()))
        .orElse(new HashSet<>());
  }

  public String getPermissionsDescription() {
    return '[' + String.join(",", this.getPermissions()) + ']';
  }

  public Set<String> getDeniedFrns() {
    return resourceAccesses.stream()
        .filter(ResourceResponseDto::isDenied)
        .map(ResourceResponseDto::getFrn)
        .collect(Collectors.toSet());
  }
}
