package com.fathom.services.apigateway.authorization.postauthorization.protector.mask;

import com.fathom.services.apigateway.directory.dto.method.postauthorization.MaskType;

public final class MaskFactory {
  private MaskFactory() {}

  public static ProtectionMask mask(MaskType maskType) {
    if (maskType == MaskType.PROTECT) return new SimpleProtectionMask();
    return new NoOpProtectionMask();
  }
}
