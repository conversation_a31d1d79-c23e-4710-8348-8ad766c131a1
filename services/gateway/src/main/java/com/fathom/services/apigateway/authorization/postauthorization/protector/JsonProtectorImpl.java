package com.fathom.services.apigateway.authorization.postauthorization.protector;

import static com.fathom.services.apigateway.authorization.postauthorization.frn.FrnExtractor.FRN_FIELD_NAME;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fathom.services.apigateway.authorization.AuthorizationException;
import com.fathom.services.apigateway.directory.dto.method.postauthorization.FieldProtectionAction;
import com.fathom.services.apigateway.directory.dto.method.postauthorization.FieldsProtectorDto;
import jakarta.annotation.Nonnull;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
// TODO: remove final
public class JsonProtectorImpl implements JsonProtector {

  private final ObjectMapper objectMapper;

  public JsonProtectorImpl(ObjectMapper objectMapper) {
    this.objectMapper = objectMapper;
  }

  @Nonnull
  @Override
  public String protect(@Nonnull String json, List<JsonProtectionSchema> schemas) {
    try {
      final JsonNode jsonNode = objectMapper.readTree(json);
      final JsonNodeProtector jsonNodeProtector = new JsonNodeProtector(schemas);
      jsonNodeProtector.dfsSecureNodes(jsonNode);
      return objectMapper.writeValueAsString(jsonNode);
    } catch (JsonProcessingException e) {
      log.error("Unable to process response", e);
      throw new AuthorizationException(
          "Unable to post-authorize, response is not valid json string");
    }
  }

  private static class JsonNodeProtector {
    private final Map<String, List<FieldsProtectorDto>> frnProtectors;

    private JsonNodeProtector(List<JsonProtectionSchema> jsonProtectionSchemas) {
      frnProtectors =
          jsonProtectionSchemas.stream()
              .collect(
                  Collectors.toMap(
                      JsonProtectionSchema::getDeniedFrn, JsonProtectionSchema::getProtectors));
    }

    private void dfsSecureNodes(JsonNode root) {
      if (root.isObject()) {
        protectObjectNode(root);
      } else if (root.isArray()) {
        protectArrayNode((ArrayNode) root);
      }
    }

    private void protectObjectNode(JsonNode root) {
      JsonNode frnNode = root.get(FRN_FIELD_NAME);
      if (frnNode != null) {
        final List<FieldsProtectorDto> fieldsProtectors = frnProtectors.get(frnNode.asText());
        if (fieldsProtectors != null) {
          protectFrnNode(root, fieldsProtectors);
        }
      }
      Iterator<String> fieldNames = root.fieldNames();
      while (fieldNames.hasNext()) {
        JsonNode fieldValue = root.get(fieldNames.next());
        if (isNodeHasInnerNodes(fieldValue)) {
          dfsSecureNodes(fieldValue);
        }
      }
    }

    private void protectArrayNode(ArrayNode root) {
      for (int i = 0; i < root.size(); i++) {
        JsonNode arrayElement = root.get(i);
        dfsSecureNodes(arrayElement);
      }
    }

    private boolean isNodeHasInnerNodes(JsonNode jsonNode) {
      return !jsonNode.isValueNode();
    }

    private void protectFrnNode(JsonNode node, List<FieldsProtectorDto> protectors) {
      ObjectNode writableNode = (ObjectNode) node;
      for (FieldsProtectorDto protector : protectors) {
        protectNode(writableNode, protector);
      }
    }

    private void protectNode(ObjectNode writableNode, FieldsProtectorDto protector) {
      if (protector.getAction() == FieldProtectionAction.DENY) {
        if (protector.isAllFieldsDeclared()) {
          protectAllFields(writableNode, protector);
          return;
        }
        protectDeclaredFields(writableNode, protector);
      } else if (protector.getAction() == FieldProtectionAction.ALLOW) {
        if (!protector.isAllFieldsDeclared()) {
          protectAllExceptDeclaredFields(writableNode, protector);
        }
      }
    }

    private void protectAllFields(ObjectNode writableNode, FieldsProtectorDto protector) {
      writableNode
          .fieldNames()
          .forEachRemaining(
              field ->
                  writableNode.put(
                      field, applyMaskToField(writableNode.get(field).textValue(), protector)));
    }

    private void protectAllExceptDeclaredFields(
        ObjectNode writableNode, FieldsProtectorDto protector) {
      writableNode
          .fieldNames()
          .forEachRemaining(
              field -> {
                if (!protector.isFieldDeclared(field)) {
                  writableNode.put(
                      field, applyMaskToField(writableNode.get(field).textValue(), protector));
                }
              });
    }

    private void protectDeclaredFields(ObjectNode writableNode, FieldsProtectorDto protector) {
      writableNode
          .fieldNames()
          .forEachRemaining(
              field -> {
                if (protector.isFieldDeclared(field)) {
                  writableNode.put(
                      field, applyMaskToField(writableNode.get(field).textValue(), protector));
                }
              });
    }

    private String applyMaskToField(String fieldValue, FieldsProtectorDto protector) {
      return protector.getMask().mask(fieldValue);
    }
  }
}
