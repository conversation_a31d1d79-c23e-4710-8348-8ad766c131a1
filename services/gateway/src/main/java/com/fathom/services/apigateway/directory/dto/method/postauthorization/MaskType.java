package com.fathom.services.apigateway.directory.dto.method.postauthorization;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum MaskType {
  PROTECT,
  NO_OP;

  @JsonCreator
  public static MaskType fromValue(String s) {
    return MaskType.valueOf(s.toUpperCase());
  }

  @JsonValue
  public String getValue() {
    return this.name().toLowerCase();
  }
}
