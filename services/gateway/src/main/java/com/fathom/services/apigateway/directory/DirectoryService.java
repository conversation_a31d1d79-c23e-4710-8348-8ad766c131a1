package com.fathom.services.apigateway.directory;

import com.fathom.services.apigateway.authorization.preauthorization.endpoint.EndpointMatcher;
import com.fathom.services.apigateway.directory.dto.DirectoryContentDto;
import com.fathom.services.apigateway.directory.dto.DirectoryDto;
import com.fathom.services.apigateway.directory.dto.DirectoryEndpointDto;
import com.fathom.services.apigateway.directory.dto.method.EndpointMethodDto;
import com.fathom.services.apigateway.directory.repository.DirectoryRepository;
import jakarta.annotation.Nonnull;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class DirectoryService {
  private final DirectoryRepository repository;
  private final EndpointMatcher endpointMatcher;

  public DirectoryService(DirectoryRepository repository, EndpointMatcher endpointMatcher) {
    this.repository = repository;
    this.endpointMatcher = endpointMatcher;
  }

  @Nonnull
  public Mono<DirectoryEndpointContext> createContext(ProxiedRequest proxiedRequest) {
    return getDirectories().map(d -> getEndpoint(d, proxiedRequest));
  }

  private Mono<DirectoryContentDto> getDirectories() {
    return repository.getDirectory();
  }

  private DirectoryEndpointContext getEndpoint(
      DirectoryContentDto directories, ProxiedRequest proxiedRequest) {
    String service = proxiedRequest.getService();
    HttpMethod httpMethod = proxiedRequest.getHttpMethod();
    String path = proxiedRequest.getPath();
    DirectoryDto directory = directories.getDirectory(proxiedRequest.getService());
    if (directory == null) {
      log.debug("[{}] Directory {} does not exist, unsecured proxy", httpMethod.name(), service);
      return DirectoryEndpointContext.empty();
    }
    Map<String, DirectoryEndpointDto> endpoints = directory.getUrlEndpoints();
    String endpoint =
        endpointMatcher.matchEndpoint(endpoints.keySet(), proxiedRequest.getPathWithQuery());
    DirectoryEndpointDto directoryEndpoint = endpoints.get(endpoint);
    if (directoryEndpoint == null) {
      log.debug(
          "[{}] Endpoint for path [{}] does not exist, unsecured proxy", httpMethod.name(), path);
      return DirectoryEndpointContext.empty();
    }
    EndpointMethodDto endpointMethodDto = directoryEndpoint.getMethod(httpMethod.name());
    if (endpointMethodDto == null) {
      log.debug(
          "[{}] Endpoint http method from service = {} for path {} does not exist, unsecured proxy",
          httpMethod.name(),
          service,
          path);
      return DirectoryEndpointContext.empty();
    }
    return new DirectoryEndpointContext(directory, endpointMethodDto);
  }
}
