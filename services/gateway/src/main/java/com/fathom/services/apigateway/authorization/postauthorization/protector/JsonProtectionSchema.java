package com.fathom.services.apigateway.authorization.postauthorization.protector;

import com.fathom.services.apigateway.directory.dto.method.postauthorization.FieldsProtectorDto;
import java.util.List;

public final class JsonProtectionSchema {
  private final String deniedFrn;
  private final List<FieldsProtectorDto> protectors;

  public JsonProtectionSchema(String deniedFrn, List<FieldsProtectorDto> protectors) {
    this.deniedFrn = deniedFrn;
    this.protectors = protectors;
  }

  public String getDeniedFrn() {
    return deniedFrn;
  }

  public List<FieldsProtectorDto> getProtectors() {
    return protectors;
  }
}
