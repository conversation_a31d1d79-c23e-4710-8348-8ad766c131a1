package com.fathom.services.apigateway.directory.dto.method.postauthorization;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Value;

@Value
@AllArgsConstructor
public class PostAuthorizationDto {
  List<PostAuthorizationActionDto> postAuthorization;

  public Set<String> getPermissions() {
    return postAuthorization.stream()
        .map(PostAuthorizationActionDto::getPermission)
        .collect(Collectors.toSet());
  }
}
