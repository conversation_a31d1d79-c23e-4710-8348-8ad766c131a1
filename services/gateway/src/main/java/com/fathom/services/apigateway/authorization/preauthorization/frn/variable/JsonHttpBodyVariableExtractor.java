package com.fathom.services.apigateway.authorization.preauthorization.frn.variable;

import com.fathom.services.apigateway.authorization.preauthorization.frn.ExtractedVariable;
import com.fathom.services.apigateway.directory.ProxiedRequest;
import com.fathom.services.apigateway.directory.dto.method.ResourceVariableDefinitionDto;
import com.jayway.jsonpath.JsonPath;

public class JsonHttpBodyVariableExtractor implements ResourceVariableExtractor {

  @Override
  public ExtractedVariable extractVariable(
      ProxiedRequest proxiedRequest, ResourceVariableDefinitionDto resourceVariable) {
    try {
      String json = proxiedRequest.getJsonRequestBody();
      String variableValue = JsonPath.parse(json).read(resourceVariable.getValue());
      return new ExtractedVariable(resourceVariable.getVariable(), variableValue);
    } catch (RuntimeException e) {
      throw new ResourceVariableDefinitionException(resourceVariable, e);
    }
  }
}
