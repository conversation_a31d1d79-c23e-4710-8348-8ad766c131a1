package com.fathom.services.apigateway.authorization.preauthorization.frn;

import jakarta.validation.constraints.NotNull;
import java.util.List;

public final class FrnDefinitionUtil {
  public static final String FRN_DELIMITER = ":";
  private static final String ORG_ID_PART = "<org-id>";

  private FrnDefinitionUtil() {}

  // build frn based on frn format with defined variables
  public static String build(
      @NotNull String frnFormat,
      @NotNull String orgId,
      String client,
      @NotNull List<ExtractedVariable> variables) {
    String frn = replaceCurrentOrgId(frnFormat, orgId);
    frn = insertClient(frn, client);
    for (ExtractedVariable extractedVariable : variables) {
      frn = replaceVariable(frn, extractedVariable);
    }
    return frn;
  }

  private static String replaceVariable(String frnFormat, ExtractedVariable extractedVariable) {
    final String regex = "\\$\\{" + extractedVariable.getVariableName() + "(.*?)}";
    return frnFormat.replaceFirst(regex, extractedVariable.getValue());
  }

  private static String replaceCurrentOrgId(String frnFormat, String orgId) {
    return frnFormat.replace(ORG_ID_PART, orgId);
  }

  // insert client within two colons
  private static String insertClient(String frnFormat, String client) {
    final int firstColonIndex = frnFormat.indexOf(FRN_DELIMITER); // first colon
    return frnFormat.substring(0, firstColonIndex + 1)
        + (client == null ? "" : client)
        + frnFormat.substring(firstColonIndex + 1);
  }
}
