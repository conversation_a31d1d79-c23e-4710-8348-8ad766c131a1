spring:
  cloud:
    gateway:
      routes:
        - id: ${SERVICE:service1}
          uri: ${SERVICE_URI:http://localhost:38086}
          predicates:
            - Path=/api/microservice/**
          filters:
        - id: ${SERVICE:service1}
          uri: ${SERVICE_URI:http://localhost:38086}
          predicates:
            - Path=/api/another/**
        - uri: ${USERUNITS_URI:http://localhost:38086}
          predicates:
            - Path=/api/userunits/units/**
            - Method=PUT,POST,DELETE
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: ${JWK_SET_URI:http://localhost:56001/auth/realms/realm/protocol/openid-connect/certs}
logging:
  level:
    root: INFO
    org:
      springframework:
        web: INFO
        boot:
          autoconfigure:
            web:
              reactive: INFO
        cloud:
          gateway: INFO

directory-uri: ${DIRECTORY_URI:http://localhost:38086}
authorization-uri: ${AUTHORIZATION_SERVER_URI:http://localhost/v1}
userunits-uri: ${USERUNITS_URI:http://userunits:8093}
