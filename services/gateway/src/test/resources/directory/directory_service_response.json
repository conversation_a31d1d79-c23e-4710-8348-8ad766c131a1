{"content": [{"name": "microservice", "frnFormatByPermission": {"microservice:ViewResource": "frn::microservice:<org-id>:/resource/${resourceId:Resource Id Description}", "microservice:ViewFrameProperty": "frn::microservice:<org-id>:/frame/${asset_id:Asset Id}/property/${property_name:Property Name}", "microservice:ViewFrame": "frn::microservice:<org-id>:/frame/${asset_id:Asset Id}"}, "endpointByUrl": {"/": {"url": "/", "methods": [{"method": "POST", "permissions": ["microservice:ViewResource"], "resourceVariables": [{"origin": "query", "value": "item", "resourceVariable": "resourceId"}]}]}, "/resource": {"url": "/resource", "methods": [{"method": "POST", "permissions": ["microservice:ViewResource"], "resourceVariables": [{"origin": "body", "value": "$.resourceId", "resourceVariable": "resourceId"}]}]}, "/frame": {"url": "/frame", "methods": [{"method": "GET", "unitConversion": {"type": "object", "jsonPath": "$", "data": [{"jsonPath": "$.data", "type": "map"}]}}]}, "/post_authorized_endpoint": {"url": "/post_authorized_endpoint", "methods": [{"method": "GET", "postAuthorization": [{"permission": "microservice:ViewFrame", "protectors": [{"maskType": "protect", "fields": ["assetId", "prevFrameId"]}]}, {"permission": "microservice:ViewFrameProperty", "protectors": [{"maskType": "protect", "action": "allow", "fields": ["frn"]}]}]}]}, "/not_authorized_endpoint": {"url": "/not_authorized_endpoint", "methods": [{"method": "GET"}]}, "/full_config_endpoint": {"url": "/full_config_endpoint", "methods": [{"method": "POST", "permissions": ["microservice:ViewFrame"], "resourceVariables": [{"origin": "body", "value": "$.assetId", "resourceVariable": "asset_id"}], "postAuthorization": [{"permission": "microservice:ViewFrameProperty", "protectors": [{"maskType": "protect", "action": "allow", "fields": ["frn"]}]}], "unitConversion": {"type": "object", "jsonPath": "$", "data": [{"jsonPath": "$.data", "type": "map"}]}}]}}}]}