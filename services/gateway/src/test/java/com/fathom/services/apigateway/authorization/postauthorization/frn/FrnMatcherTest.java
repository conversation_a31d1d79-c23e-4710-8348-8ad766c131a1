package com.fathom.services.apigateway.authorization.postauthorization.frn;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import java.util.stream.Stream;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

public class FrnMatcherTest {

  public static Stream<Arguments> data() {
    return Stream.of(
        Arguments.of(
            "frn::assets:orgId123:/asset/test_asset_1/property/test_property_1",
            "frn::assets:<org-id>:/asset/${asset_name:Asset Name}/property/${asset_property:Property Name}",
            true),
        Arguments.of(
            "frn::assets:orgId123:/asset/test_asset_1",
            "frn::assets:<org-id>:/asset/${asset_name:Asset Name}",
            true),
        Arguments.of(
            "frn::assets:orgId123:/asset/test_asset_1/property",
            "frn::assets:<org-id>:/asset/${asset_name:Asset Name}/property",
            true),
        Arguments.of(
            "frn::assets:orgId123:/asset/test_asset_1",
            "frn::assets:<org-id>:/asset/${asset_name:Asset Name}/property",
            false),
        Arguments.of(
            "frn::logical_tree:org_123:asset_tree/GC1",
            "frn::logical-tree:<org-id>:asset_tree/${asset_id: Asset Id}",
            false));
  }

  @ParameterizedTest
  @MethodSource("data")
  void givenFrnAndFrnFormat_whenCheckMatch_thenReturnIsMatched(
      String frn, String frnFormat, boolean expected) {
    boolean isMatches = FrnMatcher.isMatch(frn, frnFormat);
    assertThat(isMatches, equalTo(expected));
  }
}
