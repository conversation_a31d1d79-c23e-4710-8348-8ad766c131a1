package com.fathom.services.apigateway.authorization;

import static com.fathom.services.apigateway.BaseIntegrationTest.TEST_EMAIL;
import static com.fathom.services.apigateway.BaseIntegrationTest.TEST_ORG_ID;
import static com.fathom.services.apigateway.BaseIntegrationTest.X_ORGANIZATION_ID_HEADER;

import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationResponseDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.ResourceRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.ResourceResponseDto;
import com.fathom.services.apigateway.directory.ProxiedRequest;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.experimental.UtilityClass;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.util.MultiValueMap;

@UtilityClass
public class AuthorizationFactory {

  public AuthorizationRequestDto createRequest(ResourceRequestDto... request) {
    Set<ResourceRequestDto> resources = Stream.of(request).collect(Collectors.toSet());
    return AuthorizationRequestDto.builder()
        .org(TEST_ORG_ID)
        .principal(TEST_EMAIL)
        .resourceAccessRequests(resources)
        .build();
  }

  public AuthorizationResponseDto createResponse(ResourceResponseDto... response) {
    List<ResourceResponseDto> resources = Stream.of(response).collect(Collectors.toList());
    return AuthorizationResponseDto.builder()
        .org(TEST_ORG_ID)
        .principal(TEST_EMAIL)
        .resourceAccesses(resources)
        .build();
  }

  public ProxiedRequest simpleRequest() {
    return ProxiedRequest.builder()
        .service("microservice")
        .organizationId(TEST_ORG_ID)
        .principal(TEST_EMAIL)
        .httpMethod(HttpMethod.GET)
        .headers(createHeaders(Map.of(X_ORGANIZATION_ID_HEADER, TEST_ORG_ID)))
        .build();
  }

  public MultiValueMap<String, String> createHeaders(Map<String, String> map) {
    HttpHeaders headers = new HttpHeaders();
    map.forEach(headers::add);
    return headers;
  }
}
