package com.fathom.services.apigateway.authorization.postauthorization.protector;

import static com.fathom.services.apigateway.BaseIntegrationTest.MAPPER;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fathom.services.apigateway.directory.dto.method.postauthorization.FieldsProtectorDto;
import com.fathom.services.apigateway.util.JsonUtil;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;
import org.json.JSONException;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;

public class JsonProtectorTest {

  private static final String ROOT_DIR = "postauthorization/";

  // tree node
  public static final String TREE_NODE = ROOT_DIR + "tree_node/test_tree_node.json";
  public static final String TREE_NODE_FILTERED =
      ROOT_DIR + "tree_node/test_tree_node_protected.json";
  private static final String TREE_NODE_FILTERS =
      ROOT_DIR + "tree_node/test_tree_node_protectors.json";

  // array of nodes
  private static final String ARRAY_OF_NODES = ROOT_DIR + "array_node/test_array_node.json";
  private static final String ARRAY_OF_NODES_FILTERED =
      ROOT_DIR + "array_node/test_array_node_protected.json";
  private static final String ARRAY_OF_NODES_FILTERS =
      ROOT_DIR + "array_node/test_array_node_protectors.json";
  private static final String ARRAY_OF_NODES_FILTER_WITH_ALLOW_ACTION =
      ROOT_DIR + "array_node/test_array_node_allow_protectors.json";

  public static Stream<Arguments> data() {
    return Stream.of(
        Arguments.of(
            TREE_NODE,
            Arrays.asList(
                new JsonProtectionSchema(
                    "frn::logical_tree:org_123:asset_tree/GC2",
                    generateFieldProtectors(TREE_NODE_FILTERS)),
                new JsonProtectionSchema(
                    "frn::logical_tree:org_123:asset_tree/MN-0001",
                    generateFieldProtectors(TREE_NODE_FILTERS))),
            TREE_NODE_FILTERED),
        Arguments.of(
            ARRAY_OF_NODES,
            Collections.singletonList(
                new JsonProtectionSchema(
                    "frn::diagnostic:org_123:diagnostic/MN-0002",
                    generateFieldProtectors(ARRAY_OF_NODES_FILTERS))),
            ARRAY_OF_NODES_FILTERED),
        Arguments.of(
            ARRAY_OF_NODES,
            Collections.singletonList(
                new JsonProtectionSchema(
                    "frn::diagnostic:org_123:diagnostic/MN-0002",
                    generateFieldProtectors(ARRAY_OF_NODES_FILTER_WITH_ALLOW_ACTION))),
            ARRAY_OF_NODES_FILTERED));
  }

  private static List<FieldsProtectorDto> generateFieldProtectors(String fileName) {
    try {
      return MAPPER.readValue(JsonUtil.loadFromFile(fileName), new TypeReference<>() {});
    } catch (JsonProcessingException e) {
      throw new RuntimeException();
    }
  }

  @ParameterizedTest
  @MethodSource("data")
  public void givenSchemas_whenProtect_thenExpectProtected(
      String incomingJsonFile, List<JsonProtectionSchema> schemas, String expectedFile)
      throws JSONException {
    String json = JsonUtil.loadFromFile(incomingJsonFile);

    JsonProtectorImpl jsonProtector = new JsonProtectorImpl(new ObjectMapper());
    String protectedJson = jsonProtector.protect(json, schemas);
    JSONAssert.assertEquals(
        JsonUtil.loadFromFile(expectedFile), protectedJson, JSONCompareMode.STRICT);
  }
}
