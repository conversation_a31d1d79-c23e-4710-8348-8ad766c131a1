package com.fathom.services.apigateway.authorization.postauthorization.frn;

import static com.fathom.services.apigateway.BaseIntegrationTest.MAPPER;
import static java.util.Arrays.asList;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;

import com.fasterxml.jackson.databind.JsonNode;
import com.fathom.services.apigateway.util.JsonUtil;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

public class FrnExtractorTest {

  private static final String TEST_TREE_RESPONSE_JSON_FILE =
      "postauthorization/frn/test_tree_node.json";
  private static final String TEST_ARRAY_RESPONSE_JSON_FILE =
      "postauthorization/frn/test_array_node.json";

  public static Stream<Arguments> data() {
    return Stream.of(
        Arguments.of(
            TEST_TREE_RESPONSE_JSON_FILE,
            asList(
                "frn::logical_tree:org_123:root_tree/root_123",
                "frn::logical_tree:org_123:asset_tree/GC1",
                "frn::logical_tree:org_123:asset_tree/GC2",
                "frn::logical_tree:org_123:asset_tree/MN-0001",
                "frn::logical_tree:org_123:asset_tree/MN-0002",
                "frn::logical_tree:org_123:asset_tree/MN-0003")),
        Arguments.of(
            TEST_ARRAY_RESPONSE_JSON_FILE,
            asList(
                "frn::diagnostic:org_123:diagnostic/MN-0001",
                "frn::diagnostic:org_123:diagnostic/MN-0002",
                "frn::diagnostic:org_123:diagnostic/MN-0003")));
  }

  @ParameterizedTest
  @MethodSource("data")
  public void givenJsonNode_whenExtract_thenReturnFrns(
      String jsonFileName, List<String> expectedExtractedFrns) throws IOException {
    JsonNode treeResponse = MAPPER.readTree(JsonUtil.loadFromFile(jsonFileName));
    Set<String> actualResponse = FrnExtractor.extract(treeResponse);

    assertThat(actualResponse, equalTo(new HashSet<>(expectedExtractedFrns)));
  }
}
