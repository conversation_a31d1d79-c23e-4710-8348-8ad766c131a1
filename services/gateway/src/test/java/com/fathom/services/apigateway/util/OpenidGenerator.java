package com.fathom.services.apigateway.util;

import static com.fathom.services.apigateway.BaseIntegrationTest.TEST_EMAIL;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.gen.RSAKeyGenerator;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import java.util.Date;
import java.util.Map;

public final class OpenidGenerator {

  public static final String BEARER_PREFIX = "Bearer ";
  private static final ObjectMapper MAPPER = new ObjectMapper();
  private static final String USER_ID_CLAIM_NAME = "user_id";
  private static final String USER_ID_CLAIM_VALUE = "user1";
  private static final String KID = "123";
  private static final int KEY_SIZE = 2048;
  private static final String ISSUER = "http://localhost:8080";
  private static final String SUBJECT_NAME = "keycloak";
  private static final String EMAIL_CLAIM_NAME = "email";
  private static final String EMAIL_CLAIM_VALUE = TEST_EMAIL;
  private static final long TEN_MINUTES = 10 * 60 * 1000;

  private static OpenidGenerator instance;

  private final RSAKey privateKey;
  private final RSAKey publicKey;

  private OpenidGenerator() throws JOSEException {
    privateKey = new RSAKeyGenerator(KEY_SIZE).keyID(KID).generate();
    publicKey = privateKey.toPublicJWK();
  }

  public static OpenidGenerator getInstance() {
    if (instance == null) {
      synchronized (OpenidGenerator.class) {
        if (instance == null) {
          try {
            return instance = new OpenidGenerator();
          } catch (JOSEException e) {
            throw new RuntimeException("Unable to instantiate RSAGenerator", e);
          }
        }
      }
    }
    return instance;
  }

  public String getValidSingedJWT() {
    return getValidSignedJWT().serialize();
  }

  public String getExpiredJWT() {
    return getExpiredJWTSigned().serialize();
  }

  public String getValidJwkSet() {
    ArrayNode arrayNode = MAPPER.createArrayNode();
    Map<String, Object> stringObjectMap = this.publicKey.toJSONObject();
    arrayNode.add(MAPPER.convertValue(stringObjectMap, JsonNode.class));
    ObjectNode root = new ObjectNode(MAPPER.getNodeFactory());
    root.set("keys", arrayNode);
    return root.toString();
  }

  private SignedJWT getValidSignedJWT() {
    return generateSignedJWT(new Date(new Date().getTime() + TEN_MINUTES));
  }

  private SignedJWT getExpiredJWTSigned() {
    return generateSignedJWT(new Date(-10L));
  }

  private SignedJWT generateSignedJWT(Date expirationDate) {
    try {
      JWTClaimsSet claimsSet =
          new JWTClaimsSet.Builder()
              .subject(SUBJECT_NAME)
              .issuer(ISSUER)
              .claim(EMAIL_CLAIM_NAME, EMAIL_CLAIM_VALUE)
              .claim(USER_ID_CLAIM_NAME, USER_ID_CLAIM_VALUE)
              .expirationTime(expirationDate)
              .build();
      SignedJWT signedJWT =
          new SignedJWT(
              new JWSHeader.Builder(JWSAlgorithm.RS256).keyID(privateKey.getKeyID()).build(),
              claimsSet);
      signedJWT.sign(new RSASSASigner(privateKey));
      return signedJWT;
    } catch (JOSEException e) {
      throw new RuntimeException("Unable to generate signed JWT");
    }
  }
}
