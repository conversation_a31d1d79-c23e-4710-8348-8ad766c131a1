package com.fathom.services.apigateway.authorization.preauthorization.endpoint;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

import com.fathom.services.apigateway.authorization.preauthorization.frn.ExtractedVariable;
import com.fathom.services.apigateway.authorization.preauthorization.frn.FrnDefinitionUtil;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

public class FrnDefinitionUtilTest {
  private static final List<ExtractedVariable> EXTRACTED_VARIABLES =
      Arrays.asList(
          new ExtractedVariable("organization_id", "org123"),
          new ExtractedVariable("member_email", "<EMAIL>"),
          new ExtractedVariable("policy_name", "policy123"));

  public static Collection<Object[]> data() {
    return Arrays.asList(
        new Object[][] {
          {
            "currentOrg123",
            "wells",
            "frn::assets:<org-id>:/orgs/${organization_id:Organization Id}/members/${member_email:Member Email}/policies/${policy_name:Policy Name}",
            "frn:wells:assets:currentOrg123:/orgs/org123/members/<EMAIL>/policies/policy123"
          },
          {
            "currentOrg123",
            "wells",
            "frn::assets:<org-id>:/orgs/${organization_id:Organization Id}/members/${member_email:Member Email}/policies",
            "frn:wells:assets:currentOrg123:/orgs/org123/members/<EMAIL>/policies"
          },
          {
            "currentOrg123",
            null,
            "frn::assets:<org-id>:/orgs/${organization_id:Organization Id}/members/${member_email:Member Email}/policies",
            "frn::assets:currentOrg123:/orgs/org123/members/<EMAIL>/policies"
          }
        });
  }

  @ParameterizedTest
  @MethodSource("data")
  public void whenBuild_thenFrnConstructed(
      String orgId, String client, String frnFormat, String expectedFrn) {
    String actualFrn = FrnDefinitionUtil.build(frnFormat, orgId, client, EXTRACTED_VARIABLES);
    assertThat(expectedFrn).isEqualTo(actualFrn);
  }
}
