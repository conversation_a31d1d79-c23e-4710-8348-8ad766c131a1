package com.fathom.services.apigateway.authorization.preauthorization.frn.variable;

import static com.fathom.services.apigateway.BaseIntegrationTest.TEST_ORG_ID;
import static com.fathom.services.apigateway.directory.dto.method.ResourceVariableOriginType.BODY;
import static com.fathom.services.apigateway.directory.dto.method.ResourceVariableOriginType.HEADER;
import static com.fathom.services.apigateway.directory.dto.method.ResourceVariableOriginType.PATH;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.fathom.services.apigateway.authorization.AuthorizationFactory;
import com.fathom.services.apigateway.directory.DirectoryConfigException;
import com.fathom.services.apigateway.directory.dto.method.ResourceVariableDefinitionDto;
import java.util.Arrays;
import java.util.Collection;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

public class ResourceVariableExtractorExceptionTest {

  public static Collection<Object[]> data() {
    return Arrays.asList(
        new Object[][] {
          {
            new PathVariableExtractor(),
            new ResourceVariableDefinitionDto(PATH, "invalid_index", "member_name"),
          },
          {
            new HeaderVariableExtractor(),
            new ResourceVariableDefinitionDto(HEADER, "invalid_header_name", "orgId"),
          },
          {
            new PathVariableExtractor(),
            new ResourceVariableDefinitionDto(PATH, "11", "member_name"),
          },
          {
            new HeaderVariableExtractor(),
            new ResourceVariableDefinitionDto(HEADER, TEST_ORG_ID, "orgId"),
          },
          {
            new JsonHttpBodyVariableExtractor(),
            new ResourceVariableDefinitionDto(BODY, "invalid_json_path", "policy_name"),
          },
          {
            new JsonHttpBodyVariableExtractor(),
            new ResourceVariableDefinitionDto(BODY, "$.unknown_path", "policy_name"),
          }
        });
  }

  @ParameterizedTest
  @MethodSource("data")
  public void testExtractWhenVariableDefinitionIsNotValid(
      ResourceVariableExtractor extractor, ResourceVariableDefinitionDto givenResourceVariableDto) {
    assertThrows(
        DirectoryConfigException.class,
        () ->
            extractor.extractVariable(
                AuthorizationFactory.simpleRequest(), givenResourceVariableDto));
  }
}
