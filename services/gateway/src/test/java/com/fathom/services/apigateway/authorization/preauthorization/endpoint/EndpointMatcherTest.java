package com.fathom.services.apigateway.authorization.preauthorization.endpoint;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

public class EndpointMatcherTest {
  private static final Set<String> ENDPOINTS =
      new HashSet<>(
          Arrays.asList(
              "/asset",
              "/asset/${assetName}",
              "/asset/${assetName}/template/${templateName}",
              "/asset/template/${templateName}",
              "/templates",
              "/templates?system=true",
              "/templates?system=false",
              "/asset/${assetName}?system=true",
              "/asset/${assetName}/template/${templateName}/frame?system=true"));

  public static Collection<Object[]> data() {
    return Arrays.asList(
        new Object[][] {
          {"/asset", "/asset"},
          {"/asset/${assetName}", "/asset/MF-101"},
          {"/asset/${assetName}/template/${templateName}", "/asset/MF-101/template/TL-101"},
          {"/asset/template/${templateName}", "/asset/template/TL-101"},
          {"/templates?system=true", "/templates?paged=true&name=Temp1&system=true"},
          {"/templates", "/templates?paged=true&name=Temp1"},
          {"/templates", "/templates"},
          {"/templates", "/templates?paged=true&name=Temp1&system=another_value"},
          {"/templates?system=false", "/templates?system=false"},
          {"/templates", "/templates?system=another_value"},
          {
            "/asset/${assetName}/template/${templateName}",
            "/asset/MF-101/template/TL-101?test=true"
          },
          {"/asset/${assetName}?system=true", "/asset/MN-101?system=true"},
          {
            "/asset/${assetName}/template/${templateName}/frame?system=true",
            "/asset/MF-101/template/TL-101/frame?paged=true&system=true"
          }
        });
  }

  @ParameterizedTest
  @MethodSource("data")
  public void testMatchPathVariableEndpoint(String expectedEndpoint, String path) {
    String actualEndpoint = new EndpointMatcherImpl().matchEndpoint(ENDPOINTS, path);
    assertThat(expectedEndpoint).isEqualTo(actualEndpoint);
  }
}
