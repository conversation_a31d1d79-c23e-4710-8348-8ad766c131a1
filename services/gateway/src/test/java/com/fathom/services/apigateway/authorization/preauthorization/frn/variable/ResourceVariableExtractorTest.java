package com.fathom.services.apigateway.authorization.preauthorization.frn.variable;

import static com.fathom.services.apigateway.BaseIntegrationTest.TEST_ORG_ID;
import static com.fathom.services.apigateway.BaseIntegrationTest.X_ORGANIZATION_ID_HEADER;
import static com.fathom.services.apigateway.directory.dto.method.ResourceVariableOriginType.BODY;
import static com.fathom.services.apigateway.directory.dto.method.ResourceVariableOriginType.HEADER;
import static com.fathom.services.apigateway.directory.dto.method.ResourceVariableOriginType.PATH;
import static com.fathom.services.apigateway.directory.dto.method.ResourceVariableOriginType.QUERY;
import static org.assertj.core.api.Assertions.assertThat;

import com.fathom.services.apigateway.authorization.AuthorizationFactory;
import com.fathom.services.apigateway.authorization.preauthorization.frn.ExtractedVariable;
import com.fathom.services.apigateway.directory.ProxiedRequest;
import com.fathom.services.apigateway.directory.dto.method.ResourceVariableDefinitionDto;
import java.util.Map;
import java.util.stream.Stream;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.http.HttpMethod;
import org.springframework.util.LinkedMultiValueMap;

public class ResourceVariableExtractorTest {

  public static Stream<Arguments> data() {
    return Stream.of(
        Arguments.of(
            new PathVariableExtractor(),
            new ResourceVariableDefinitionDto(PATH, "4", "member_name"),
            new ExtractedVariable("member_name", "<EMAIL>")),
        Arguments.of(
            new HeaderVariableExtractor(),
            new ResourceVariableDefinitionDto(HEADER, X_ORGANIZATION_ID_HEADER, "orgId"),
            new ExtractedVariable("orgId", TEST_ORG_ID)),
        Arguments.of(
            new QueryParamVariableExtractor(),
            new ResourceVariableDefinitionDto(QUERY, "assetId", "resourceVar"),
            new ExtractedVariable("resourceVar", "test_123_asset")),
        Arguments.of(
            new JsonHttpBodyVariableExtractor(),
            new ResourceVariableDefinitionDto(BODY, "$.content.policyName", "policy_name"),
            new ExtractedVariable("policy_name", "policy123")));
  }

  @ParameterizedTest
  @MethodSource("data")
  void testExtract(
      ResourceVariableExtractor extractor,
      ResourceVariableDefinitionDto givenResourceVariableDto,
      ExtractedVariable expectedExtractedVariable) {
    ProxiedRequest proxiedRequest = generateProxiedRequest();
    ExtractedVariable actualVariable =
        extractor.extractVariable(proxiedRequest, givenResourceVariableDto);
    assertThat(expectedExtractedVariable).isEqualTo(actualVariable);
  }

  private ProxiedRequest generateProxiedRequest() {
    LinkedMultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
    queryParams.add("assetId", "test_123_asset");
    return ProxiedRequest.builder()
        .headers(AuthorizationFactory.createHeaders(Map.of(X_ORGANIZATION_ID_HEADER, TEST_ORG_ID)))
        .httpMethod(HttpMethod.GET)
        .path("/orgs/org_123/members/<EMAIL>/policies")
        .queryParams(queryParams)
        .jsonRequestBody("{\"content\": {\"policyName\":\"policy123\"}}")
        .build();
  }
}
