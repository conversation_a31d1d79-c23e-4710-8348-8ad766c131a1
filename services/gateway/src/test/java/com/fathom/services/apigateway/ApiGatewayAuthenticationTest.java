package com.fathom.services.apigateway;

import static com.fathom.services.apigateway.util.OpenidGenerator.BEARER_PREFIX;
import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;

public class ApiGatewayAuthenticationTest extends BaseIntegrationTest {

  private static final String MICROSERVICE_ENDPOINT = "/data";
  private static final String JSON_OK_RESPONSE = "{\"response\":\"OK\"}";

  @Test
  void whenRequestProtectedServiceEndpointWithValidToken_thenReturnOk() {
    stubRemoteProxiedService();

    client
        .get()
        .uri("/api/microservice/data")
        .header(AUTHORIZATION, getValidAuthorizationHeader())
        .header(X_ORGANIZATION_ID_HEADER, TEST_ORG_ID)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .json(JSON_OK_RESPONSE);
  }

  @Test
  void whenRequestProtectedServiceEndpointWithNotValidToken_thenReturn401() {
    String expiredJwt = OPENID_GENERATOR.getExpiredJWT();
    client
        .get()
        .uri("/api/microservice/data")
        .header(AUTHORIZATION, BEARER_PREFIX + expiredJwt)
        .exchange()
        .expectStatus()
        .isUnauthorized();
  }

  @Test
  void whenRequestProtectedServiceEndpointWithoutToken_thenReturn401() {
    client.get().uri("/api/assets/assets").exchange().expectStatus().isUnauthorized();
  }

  private void stubRemoteProxiedService() {
    stubFor(
        get(urlEqualTo(MICROSERVICE_ENDPOINT))
            .willReturn(
                aResponse()
                    .withBody(JSON_OK_RESPONSE)
                    .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));
  }
}
