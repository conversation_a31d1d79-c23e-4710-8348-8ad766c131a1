package com.fathom.services.apigateway.authorization.preauthorization;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.exactly;
import static com.github.tomakehurst.wiremock.client.WireMock.getRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

import com.fathom.services.apigateway.BaseIntegrationTest;
import com.fathom.services.apigateway.authorization.AuthorizationFactory;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationResponseDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.GeneralPermissionStatus;
import com.fathom.services.apigateway.authorization.preauthorization.dto.PermissionResponseDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.PermissionStatus;
import com.fathom.services.apigateway.authorization.preauthorization.dto.ResourceRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.ResourceResponseDto;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyInserters;

class ApiGatewayPreAuthorizationTest extends BaseIntegrationTest {
  private static final String JSON_REQUEST_BODY = "{\"response\":\"SUCCESS\"}";
  private static final String MICROSERVICE_FULL_API_PATH = "/api/microservice";

  @Test
  void givenUnSecuredEndpointRequest_whenEndpointNotExistInDirectory_thenSuccess() {
    stubRemoteProxiedService("/unsecured_endpoint");

    client
        .post()
        .uri(MICROSERVICE_FULL_API_PATH + "/unsecured_endpoint")
        .header(AUTHORIZATION, getValidAuthorizationHeader())
        .header(X_ORGANIZATION_ID_HEADER, TEST_ORG_ID)
        .accept(MediaType.APPLICATION_JSON)
        .contentType(MediaType.APPLICATION_JSON)
        .body(BodyInserters.fromValue(JSON_REQUEST_BODY))
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .json("[]");

    verify(exactly(1), getRequestedFor(urlEqualTo("/directory/enabled")));
    verify(exactly(0), postRequestedFor(urlEqualTo("/v1/authorize")));
  }

  @Test
  void givenSecuredEndpointRequest_whenParamUsedForAuthorizationAndAccessGranted_thenSuccess() {

    stubRemoteProxiedService("/?item=123");
    stubRemoteAuthorizationServiceResponse(
        GeneralPermissionStatus.ALLOWED, PermissionStatus.ALLOWED);

    client
        .post()
        .uri(MICROSERVICE_FULL_API_PATH + "?item=123")
        .header(AUTHORIZATION, getValidAuthorizationHeader())
        .header(X_ORGANIZATION_ID_HEADER, TEST_ORG_ID)
        .accept(MediaType.APPLICATION_JSON)
        .contentType(MediaType.APPLICATION_JSON)
        .body(BodyInserters.fromValue(JSON_REQUEST_BODY))
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .json("[]");

    verify(exactly(1), getRequestedFor(urlEqualTo("/directory/enabled")));
    verify(
        exactly(1),
        postRequestedFor(urlEqualTo("/v1/authorize"))
            .withHeader(CONTENT_TYPE, equalTo(MediaType.APPLICATION_JSON.toString())));

    verify(
        exactly(1),
        postRequestedFor(urlEqualTo("/?item=123"))
            .withHeader(X_ORGANIZATION_ID_HEADER, equalTo(TEST_ORG_ID))
            .withRequestBody(equalToJson(JSON_REQUEST_BODY)));
  }

  @Test
  void givenSecuredEndpointRequest_whenUserHasNoAccess_then403Returned() {

    stubRemoteProxiedService("/?item=123");
    stubRemoteAuthorizationServiceResponse(GeneralPermissionStatus.DENIED, PermissionStatus.DENIED);

    client
        .post()
        .uri(MICROSERVICE_FULL_API_PATH + "?item=123")
        .header(AUTHORIZATION, getValidAuthorizationHeader())
        .header(X_ORGANIZATION_ID_HEADER, TEST_ORG_ID)
        .accept(MediaType.APPLICATION_JSON)
        .contentType(MediaType.APPLICATION_JSON)
        .body(BodyInserters.fromValue(JSON_REQUEST_BODY))
        .exchange()
        .expectStatus()
        .isForbidden()
        .expectBody()
        .jsonPath("$.message", equalTo("403 FORBIDDEN \"microservice:ViewResource\""));

    verify(exactly(1), getRequestedFor(urlEqualTo("/directory/enabled")));
    verify(exactly(1), postRequestedFor(urlEqualTo("/v1/authorize")));
  }

  @Test
  void
      givenSecuredEndpointWithFrnParamInRequestBody_whenUserHasAccess_thenParamExtractedFromRequestBodyAndSuccess() {
    stubRemoteProxiedServiceRequestBodyWithResourceId();
    stubRemoteAuthorizationServiceResponse(
        GeneralPermissionStatus.ALLOWED, PermissionStatus.ALLOWED);

    client
        .post()
        .uri(MICROSERVICE_FULL_API_PATH + "/resource")
        .header(AUTHORIZATION, getValidAuthorizationHeader())
        .header(X_ORGANIZATION_ID_HEADER, TEST_ORG_ID)
        .accept(MediaType.APPLICATION_JSON)
        .contentType(MediaType.APPLICATION_JSON)
        .body(BodyInserters.fromValue("{\"resourceId\":\"123\"}"))
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .json("[]");
  }

  private void stubRemoteProxiedService(String path) {
    stubFor(
        post(urlEqualTo(path))
            .willReturn(
                aResponse()
                    .withBody("[]")
                    .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));
  }

  private void stubRemoteProxiedServiceRequestBodyWithResourceId() {
    stubFor(
        post(urlEqualTo("/resource"))
            .withRequestBody(equalToJson("{\"resourceId\":\"123\"}"))
            .withHeader(CONTENT_TYPE, equalTo(MediaType.APPLICATION_JSON_VALUE))
            .willReturn(
                aResponse()
                    .withBody("[]")
                    .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));
  }

  private void stubRemoteAuthorizationServiceResponse(
      GeneralPermissionStatus expectedStatus, PermissionStatus expectedPermissionStatus) {
    AuthorizationRequestDto request =
        AuthorizationFactory.createRequest(
            ResourceRequestDto.from(
                "frn::microservice:org_123:/resource/123", "microservice:ViewResource"));
    AuthorizationResponseDto response =
        AuthorizationFactory.createResponse(
            ResourceResponseDto.builder()
                .status(expectedStatus)
                .frn("frn::microservice:org_123:/resource/123")
                .permissions(
                    Collections.singleton(
                        PermissionResponseDto.builder()
                            .status(expectedPermissionStatus)
                            .permission("microservice:ViewResource")
                            .build()))
                .build());
    stubRemoteAuthorizationServiceResponse(request, response);
  }
}
