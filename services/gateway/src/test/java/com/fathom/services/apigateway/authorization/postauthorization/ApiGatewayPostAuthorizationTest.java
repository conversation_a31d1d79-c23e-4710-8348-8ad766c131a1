package com.fathom.services.apigateway.authorization.postauthorization;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.exactly;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.getRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

import com.fathom.services.apigateway.BaseIntegrationTest;
import com.fathom.services.apigateway.authorization.AuthorizationFactory;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationResponseDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.GeneralPermissionStatus;
import com.fathom.services.apigateway.authorization.preauthorization.dto.PermissionResponseDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.PermissionStatus;
import com.fathom.services.apigateway.authorization.preauthorization.dto.ResourceRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.ResourceResponseDto;
import com.fathom.services.apigateway.util.JsonUtil;
import java.io.IOException;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;

public class ApiGatewayPostAuthorizationTest extends BaseIntegrationTest {

  private static final String MICROSERVICE_FULL_API_PATH = "/api/microservice";
  private static final String REMOTE_SERVICE_RESPONSE_URL =
      "postauthorization/remote_service_response.json";
  private static final String EXPECTED_POST_AUTHORIZED_RESPONSE_URL =
      "postauthorization/expected_response_post_authorized.json";

  @Test
  void
      givenEndpointHasPostAuthorizationInDirectory_whenSomeFrnsDenied_thenPostAuthorizedResponse() {

    stubRemoteRemoteServiceWithResponseBody("/post_authorized_endpoint");
    stubRemoteAuthorizationServiceResponse(frameFrnsRequest(), responseForGivenFrns());

    client
        .get()
        .uri(MICROSERVICE_FULL_API_PATH + "/post_authorized_endpoint")
        .header(AUTHORIZATION, getValidAuthorizationHeader())
        .header(X_ORGANIZATION_ID_HEADER, TEST_ORG_ID)
        .accept(MediaType.APPLICATION_JSON)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .json(JsonUtil.loadFromFile(EXPECTED_POST_AUTHORIZED_RESPONSE_URL));

    verify(exactly(1), postRequestedFor(urlEqualTo("/v1/authorize")));
    verify(exactly(1), getRequestedFor(urlEqualTo("/directory/enabled")));
  }

  @Test
  void givenEndpointWithoutPostAuthorization_thenReturnedResponse() throws IOException {

    stubRemoteRemoteServiceWithResponseBody("/not_authorized_endpoint");
    stubRemoteAuthorizationServiceResponse(frameFrnsRequest(), responseForGivenFrns());

    client
        .get()
        .uri(MICROSERVICE_FULL_API_PATH + "/not_authorized_endpoint")
        .header(AUTHORIZATION, getValidAuthorizationHeader())
        .header(X_ORGANIZATION_ID_HEADER, TEST_ORG_ID)
        .accept(MediaType.APPLICATION_JSON)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .json(JsonUtil.loadFromFile(REMOTE_SERVICE_RESPONSE_URL));

    verify(exactly(1), getRequestedFor(urlEqualTo("/directory/enabled")));
    verify(exactly(0), postRequestedFor(urlEqualTo("/v1/authorize")));
  }

  private void stubRemoteRemoteServiceWithResponseBody(String url) {
    stubFor(
        get(urlEqualTo(url))
            .willReturn(
                aResponse()
                    .withBody(JsonUtil.loadFromFile(REMOTE_SERVICE_RESPONSE_URL))
                    .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));
  }

  private AuthorizationRequestDto frameFrnsRequest() {
    return AuthorizationFactory.createRequest(
        ResourceRequestDto.from(
            "frn::microservice:org_123:/frame/asset1", "microservice:ViewFrame"),
        ResourceRequestDto.from(
            "frn::microservice:org_123:/frame/asset1/property/iprmodel",
            "microservice:ViewFrameProperty"),
        ResourceRequestDto.from(
            "frn::microservice:org_123:/frame/asset1/property/motorratingvoltage",
            "microservice:ViewFrameProperty"),
        ResourceRequestDto.from(
            "frn::microservice:org_123:/frame/asset1/property/pumpassemblydepth",
            "microservice:ViewFrameProperty"));
  }

  private AuthorizationResponseDto responseForGivenFrns() {
    return AuthorizationFactory.createResponse(
        // DENIED top object response
        ResourceResponseDto.builder()
            .status(GeneralPermissionStatus.DENIED)
            .frn("frn::microservice:org_123:/frame/asset1")
            .permissions(
                Collections.singleton(
                    PermissionResponseDto.builder()
                        .status(PermissionStatus.DENIED)
                        .permission("historyreader:ViewFrame")
                        .build()))
            .build(),
        ResourceResponseDto.builder()
            .status(GeneralPermissionStatus.DENIED)
            .frn("frn::microservice:org_123:/frame/asset1/property/iprmodel")
            .permissions(
                Collections.singleton(
                    PermissionResponseDto.builder()
                        .status(PermissionStatus.DENIED)
                        .permission("historyreader:ViewFrameProperty")
                        .build()))
            .build(),
        ResourceResponseDto.builder()
            .status(GeneralPermissionStatus.ALLOWED)
            .frn("frn::microservice:org_123:/frame/asset1/property/pumpassemblydepth")
            .permissions(
                Collections.singleton(
                    PermissionResponseDto.builder()
                        .status(PermissionStatus.ALLOWED)
                        .permission("historyreader:ViewFrameProperty")
                        .build()))
            .build(),
        ResourceResponseDto.builder()
            .status(GeneralPermissionStatus.ALLOWED)
            .frn("frn::microservice:org_123:/frame/asset1/property/motorratingvoltage")
            .permissions(
                Collections.singleton(
                    PermissionResponseDto.builder()
                        .status(PermissionStatus.ALLOWED)
                        .permission("historyreader:ViewFrameProperty")
                        .build()))
            .build());
  }
}
