package com.fathom.services.apigateway;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalTo;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.exactly;
import static com.github.tomakehurst.wiremock.client.WireMock.getRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.postRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;
import static org.springframework.http.HttpHeaders.AUTHORIZATION;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fathom.services.apigateway.authorization.AuthorizationFactory;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationResponseDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.GeneralPermissionStatus;
import com.fathom.services.apigateway.authorization.preauthorization.dto.PermissionResponseDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.PermissionStatus;
import com.fathom.services.apigateway.authorization.preauthorization.dto.ResourceRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.ResourceResponseDto;
import com.fathom.services.apigateway.util.JsonUtil;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.BodyInserters;

public class ApiGatewayFilterOrderIntegrationTest extends BaseIntegrationTest {

  private static final String REMOTE_SERVICE_RESPONSE = "filter/remote_service_response.json";
  private static final String EXPECTED_MODIFIED_RESPONSE = "filter/expected_modified_response.json";

  @Test
  void
      givenEndpointWithFullConfigInDirectory_thenStripPrefixAndPreAndPostAuthorizationAndUnitConversionApplied()
          throws JsonProcessingException {

    stubRemoteServiceEndpointWithRequestAndResponseBody();
    stubRemoteAuthorizationServiceResponse(requestBodyAuthZRequest(), requestBodyAuthZResponse());
    stubRemoteAuthorizationServiceResponse(responseBodyAuthZRequest(), responseBodyAuthZResponse());

    client
        .post()
        .uri("/api/microservice/full_config_endpoint")
        .header(AUTHORIZATION, getValidAuthorizationHeader())
        .header(X_ORGANIZATION_ID_HEADER, TEST_ORG_ID)
        .header(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
        .body(BodyInserters.fromValue("{\"assetId\":\"asset1\"}"))
        .accept(MediaType.APPLICATION_JSON)
        .exchange()
        .expectStatus()
        .isOk()
        .expectBody()
        .json(JsonUtil.loadFromFile(EXPECTED_MODIFIED_RESPONSE));

    verify(exactly(2), postRequestedFor(urlEqualTo("/v1/authorize")));
    verify(exactly(1), getRequestedFor(urlEqualTo("/directory/enabled")));
    verify(exactly(1), getRequestedFor(urlEqualTo("/units/testuser%40gmail.com")));
  }

  private void stubRemoteServiceEndpointWithRequestAndResponseBody() {
    stubFor(
        post(urlEqualTo("/full_config_endpoint"))
            .withRequestBody(equalToJson("{\"assetId\":\"asset1\"}"))
            .withHeader(CONTENT_TYPE, equalTo(MediaType.APPLICATION_JSON_VALUE))
            .willReturn(
                aResponse()
                    .withBody(JsonUtil.loadFromFile(REMOTE_SERVICE_RESPONSE))
                    .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));
  }

  private AuthorizationRequestDto requestBodyAuthZRequest() {
    return AuthorizationFactory.createRequest(
        ResourceRequestDto.from(
            "frn::microservice:org_123:/frame/asset1", "microservice:ViewFrame"));
  }

  private AuthorizationResponseDto requestBodyAuthZResponse() {
    return AuthorizationFactory.createResponse(
        ResourceResponseDto.builder()
            .status(GeneralPermissionStatus.ALLOWED)
            .frn("frn::microservice:org_123:/frame/asset1")
            .permissions(
                Collections.singleton(
                    PermissionResponseDto.builder()
                        .status(PermissionStatus.ALLOWED)
                        .permission("historyreader:ViewFrame")
                        .build()))
            .build());
  }

  private AuthorizationRequestDto responseBodyAuthZRequest() {
    return AuthorizationFactory.createRequest(
        ResourceRequestDto.from(
            "frn::microservice:org_123:/frame/asset1/property/temperature",
            "microservice:ViewFrameProperty"),
        ResourceRequestDto.from(
            "frn::microservice:org_123:/frame/asset1/property/iprmodel",
            "microservice:ViewFrameProperty"));
  }

  private AuthorizationResponseDto responseBodyAuthZResponse() {
    return AuthorizationFactory.createResponse(
        ResourceResponseDto.builder()
            .status(GeneralPermissionStatus.ALLOWED)
            .frn("frn::microservice:org_123:/frame/asset1/property/temperature")
            .permissions(
                Collections.singleton(
                    PermissionResponseDto.builder()
                        .status(PermissionStatus.ALLOWED)
                        .permission("historyreader:ViewFrameProperty")
                        .build()))
            .build(),
        ResourceResponseDto.builder()
            .status(GeneralPermissionStatus.DENIED)
            .frn("frn::microservice:org_123:/frame/asset1/property/iprmodel")
            .permissions(
                Collections.singleton(
                    PermissionResponseDto.builder()
                        .status(PermissionStatus.DENIED)
                        .permission("historyreader:ViewFrameProperty")
                        .build()))
            .build());
  }
}
