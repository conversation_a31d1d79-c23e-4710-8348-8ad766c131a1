package com.fathom.services.apigateway;

import static com.fathom.services.apigateway.util.OpenidGenerator.getInstance;
import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToIgnoreCase;
import static com.github.tomakehurst.wiremock.client.WireMock.equalToJson;
import static com.github.tomakehurst.wiremock.client.WireMock.get;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlEqualTo;
import static org.springframework.boot.test.context.SpringBootTest.WebEnvironment.RANDOM_PORT;
import static org.springframework.http.HttpHeaders.CONTENT_TYPE;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationRequestDto;
import com.fathom.services.apigateway.authorization.preauthorization.dto.AuthorizationResponseDto;
import com.fathom.services.apigateway.util.OpenidGenerator;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.contract.wiremock.AutoConfigureWireMock;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.reactive.server.WebTestClient;

@SpringBootTest(
    classes = {ApiGatewayApplication.class},
    webEnvironment = RANDOM_PORT)
@AutoConfigureWireMock(port = 0)
@AutoConfigureWebTestClient
@ActiveProfiles("test")
@TestPropertySource(
    properties = {
      "DIRECTORY_URI=http://localhost:${wiremock.server.port}",
      "JWK_SET_URI=http://localhost:${wiremock.server.port}/auth/realms/realm/protocol/openid-connect/certs",
      "SERVICE_URI=http://localhost:${wiremock.server.port}",
      "AUTHORIZATION_SERVER_URI=http://localhost:${wiremock.server.port}/v1",
      "SERVICE_URI=http://localhost:${wiremock.server.port}",
      "USERUNITS_URI=http://localhost:${wiremock.server.port}",
      "wiremock.reset-mappings-after-each-test=true"
    })
public abstract class BaseIntegrationTest {

  public static final String X_ORGANIZATION_ID_HEADER = "x-organizationid";
  public static final String TEST_ORG_ID = "org_123";
  public static final String TEST_EMAIL = "<EMAIL>";
  public static final ObjectMapper MAPPER = new ObjectMapper();
  // this was commented out because it did not seem worth it to install a
  // dependency (com.fathom.lib.common) when the tests do not currently run.
  // public static final ObjectMapper MAPPER = MapperSingleton.getMapper();
  protected static final OpenidGenerator OPENID_GENERATOR = getInstance();
  private static final String JWT_SET_URI = "/auth/realms/realm/protocol/openid-connect/certs";
  private static final String DIRECTORY_SERVICE_RESPONSE_URL =
      "directory/directory_service_response.json";
  @Autowired protected WebTestClient client;

  // openid jwk set response
  protected void stubRemoteAuthorizationServerJwkSet() {
    String jwkSetResponse = OPENID_GENERATOR.getValidJwkSet();
    stubFor(
        get(urlEqualTo(JWT_SET_URI))
            .willReturn(
                aResponse()
                    .withBody(jwkSetResponse)
                    .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));
  }

  protected void stubRemoteAuthorizationServiceResponse(
      AuthorizationRequestDto request, AuthorizationResponseDto response) {
    try {
      String requestJson = MAPPER.writeValueAsString(request);
      String responseJson = MAPPER.writeValueAsString(response);
      stubFor(
          post(urlEqualTo("/v1/authorize"))
              .withRequestBody(equalToJson(requestJson))
              .withHeader(CONTENT_TYPE, equalToIgnoreCase(MediaType.APPLICATION_JSON_VALUE))
              .willReturn(
                  aResponse()
                      .withBody(responseJson)
                      .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));
    } catch (IOException e) {
      throw new RuntimeException("Unable to load class");
    }
  }

  private void stubRemoteDirectoryService() {
    try {
      URL resource = this.getClass().getClassLoader().getResource(DIRECTORY_SERVICE_RESPONSE_URL);
      File file = new File(Objects.requireNonNull(resource).getFile());
      String json = MAPPER.readTree(file).toString();
      stubFor(
          get(urlEqualTo("/directory/enabled"))
              .willReturn(
                  aResponse()
                      .withBody(json)
                      .withHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)));
    } catch (IOException e) {
      throw new RuntimeException("Unable to load class");
    }
  }

  protected String getValidAuthorizationHeader() {
    return "Bearer " + OPENID_GENERATOR.getValidSingedJWT();
  }
}
