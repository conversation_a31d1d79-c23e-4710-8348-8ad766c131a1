{"mappings": [{"request": {"method": "POST", "urlPath": "/users/filtered"}, "response": {"status": 200, "jsonBody": {"content": [{"id": "11111111-1111-1111-1111-111111111111", "firstName": "<PERSON>", "lastName": "Piper", "fullName": "<PERSON>", "email": "<EMAIL>", "groups": ["A", "B", "C"], "role": "member"}, {"id": "22222222-2222-2222-2222-222222222222", "firstName": "<PERSON>", "lastName": "Telecom", "fullName": "Rafael Telecom", "email": "<EMAIL>", "groups": ["A"], "role": "member"}, {"id": "33333333-3333-3333-3333-333333333333", "firstName": "<PERSON>", "lastName": "Egglayer", "fullName": "<PERSON>", "email": "<EMAIL>", "groups": ["A", "B", "C", "D", "E", "F"], "role": "member"}], "totalElements": 3, "totalPages": 1, "size": 100, "number": 0}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "GET", "urlPath": "/users/roles"}, "response": {"status": 200, "jsonBody": [{"organizationId": "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa", "email": "<EMAIL>", "role": "member"}, {"organizationId": "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa", "email": "<EMAIL>", "role": "member"}], "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "GET", "urlPath": "/users/roles/organization"}, "response": {"status": 200, "jsonBody": {"organizationId": "aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa", "email": "<EMAIL>", "role": "member"}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "POST", "urlPattern": "/users/roles/.*"}, "response": {"status": 200, "jsonBody": {"id": "11111111-1111-1111-1111-111111111111", "firstName": "<PERSON>", "lastName": "Piper", "fullName": "<PERSON>", "email": "<EMAIL>", "role": "admin"}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "DELETE", "urlPattern": "/users/roles/.*"}, "response": {"status": 200, "jsonBody": {}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "GET", "urlPath": "/users/roles/checkIfAdmin"}, "response": {"status": 200, "jsonBody": true, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "POST", "urlPath": "/users/profile-image", "multipartPatterns": [{"matchingType": "ANY"}]}, "response": {"status": 200, "jsonBody": {"id": "11111111-1111-1111-1111-111111111111", "firstName": "<PERSON>", "lastName": "Piper", "fullName": "<PERSON>", "email": "<EMAIL>", "profileImageUrl": "https://example.com/images/profile.jpg"}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "POST", "urlPath": "/users"}, "response": {"status": 200, "jsonBody": {"id": "44444444-4444-4444-4444-444444444444", "firstName": "Test", "lastName": "User", "fullName": "Test User", "email": "<EMAIL>"}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "POST", "urlPattern": "/users/.*/setup-password"}, "response": {"status": 204}}, {"request": {"method": "POST", "urlPath": "/users/roles/admin"}, "response": {"status": 200, "jsonBody": {}, "headers": {"Content-Type": "application/json"}}}]}