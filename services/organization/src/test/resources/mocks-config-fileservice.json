{"mappings": [{"request": {"method": "POST", "urlPattern": "/files", "multipartPatterns": [{"name": "file"}]}, "response": {"status": 201, "jsonBody": {"id": "test-logo-file-id", "uuid": "test-org-uuid", "fileName": "organization-logo.png", "contentType": "image/png", "description": "Organization logo", "classification": "organization-logo", "displayName": "organization-logo.png", "size": 12345, "createdAt": "2023-01-01T12:00:00Z", "updatedAt": "2023-01-01T12:00:00Z"}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "DELETE", "urlPattern": "/file/([^/]+)"}, "response": {"status": 204}}, {"request": {"method": "DELETE", "urlPattern": "/files/([^/]+)"}, "response": {"status": 204}}, {"request": {"method": "POST", "url": "/files/byUUID"}, "response": {"status": 200, "jsonBody": [{"id": "test-logo-file-id", "uuid": "test-org-uuid", "fileName": "organization-logo.png", "contentType": "image/png", "description": "Organization logo", "classification": "organization-logo", "displayName": "organization-logo.png", "size": 12345, "createdAt": "2023-01-01T12:00:00Z", "updatedAt": "2023-01-01T12:00:00Z"}], "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "POST", "url": "/file/generate-download-token"}, "response": {"status": 200, "jsonBody": [{"fileId": "test-logo-file-id", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmaWxlSWQiOiJ0ZXN0LWxvZ28tZmlsZS1pZCJ9.test-token"}], "headers": {"Content-Type": "application/json"}}}]}