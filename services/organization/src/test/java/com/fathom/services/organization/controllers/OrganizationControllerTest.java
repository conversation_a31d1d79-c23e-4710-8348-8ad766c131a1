package com.fathom.services.organization.controllers;

import static org.assertj.core.api.Assertions.*;

import com.fathom.services.organization.IntegrationTestBase;
import com.fathom.services.organization.OrganizationData;
import com.fathom.services.organization.OrganizationOperations;
import io.restassured.RestAssured;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class OrganizationControllerTest extends IntegrationTestBase
    implements OrganizationOperations, OrganizationData {

  private static final String testEmail = "<EMAIL>";

  @BeforeEach
  void beforeEach() {
    RestAssured.port = port;
  }

  @Test
  void shouldReturnEmptyBody_OnFindAllByUser() {
    var returned = findAvailableOrganizationsAsserting200(testEmail);
    assertThat(returned).isEmpty();
  }

  @Test
  void creatingNewOrganizationHappyPath() {
    var dto = anOrganization().build();

    var organization = createNewOrganizationAsserting201(testEmail, dto);

    assertThat(organization).isNotNull();
    assertThat(organization.getId()).isNotNull();
  }
}
