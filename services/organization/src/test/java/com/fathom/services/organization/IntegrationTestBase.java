package com.fathom.services.organization;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import org.junit.jupiter.api.BeforeAll;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.PostgreSQLContainer;
import org.wiremock.integrations.testcontainers.WireMockContainer;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public abstract class IntegrationTestBase {
  @LocalServerPort protected int port;

  @BeforeAll
  public static void beforeAll() {
    postgres.start();
    wiremockUsermanagement.start();
    wiremockFileservice.start();
  }

  protected static final PostgreSQLContainer<?> postgres =
      new PostgreSQLContainer<>("postgres:12")
          .withExposedPorts(5432)
          .withReuse(true)
          .withLabel("reuse-id", "pgorganization");

  protected static WireMockContainer wiremockUsermanagement =
      new WireMockContainer("wiremock/wiremock:3.6.0")
          .withMappingFromResource("mocks-config-usermanagement.json")
          .withReuse(true)
          .withLabel("reuse-id", "wiremockfsusermanagement");

  protected static WireMockContainer wiremockFileservice =
      new WireMockContainer("wiremock/wiremock:3.6.0")
          .withMappingFromJSON(loadMappingContent())
          .withReuse(true)
          .withLabel("reuse-id", "wiremockfsorganization");

  private static String loadMappingContent() {
    try {
      Path resourcePath = Paths.get("src/test/resources/mocks-config-fileservice.json");
      return Files.readString(resourcePath);
    } catch (IOException e) {
      throw new RuntimeException("Failed to load WireMock mapping file", e);
    }
  }

  @DynamicPropertySource
  static void configureProperties(DynamicPropertyRegistry registry) {
    registry.add("spring.flyway.url", postgres::getJdbcUrl);
    registry.add("spring.flyway.user", postgres::getUsername);
    registry.add("spring.flyway.password", postgres::getPassword);

    registry.add("spring.datasource.url", postgres::getJdbcUrl);
    registry.add("spring.datasource.username", postgres::getUsername);
    registry.add("spring.datasource.password", postgres::getPassword);

    registry.add("services.usermanagement.name", wiremockUsermanagement::getBaseUrl);
    registry.add("services.fileservice.name", wiremockFileservice::getBaseUrl);
  }
}
