package com.fathom.services.organization;

import static com.fathom.services.organization.controllers.OrganizationControllerV1.EMAIL_HEADER;
import static io.restassured.RestAssured.given;

import com.fathom.services.organization.model.dto.OrganizationDto;
import io.restassured.http.ContentType;
import java.util.List;

public interface OrganizationOperations {
  default List<OrganizationDto> findAvailableOrganizationsAsserting200(final String email) {
    return given()
        .header(EMAIL_HEADER, email)
        .when()
        .get("/api/v1/organizations")
        .then()
        .log()
        .ifError()
        .statusCode(200)
        .extract()
        .jsonPath()
        .getList("$", OrganizationDto.class);
  }

  default OrganizationDto createNewOrganizationAsserting201(
      final String email, final OrganizationDto organization) {
    return given()
        .contentType(ContentType.JSON)
        .header(EMAIL_HEADER, email)
        .body(organization)
        .when()
        .post("/api/v1/organizations")
        .then()
        .log()
        .ifError()
        .statusCode(201)
        .extract()
        .as(OrganizationDto.class);
  }
}
