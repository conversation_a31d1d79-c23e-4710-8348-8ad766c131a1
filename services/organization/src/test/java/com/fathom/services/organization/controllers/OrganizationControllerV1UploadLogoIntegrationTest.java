package com.fathom.services.organization.controllers;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fathom.services.organization.IntegrationTestBase;
import com.fathom.services.organization.feign.FileClient;
import com.fathom.services.organization.feign.UserMangerClient;
import com.fathom.services.organization.model.dto.OrganizationDto;
import com.fathom.services.organization.model.dto.feign.FileUploadResponseDto;
import com.fathom.services.organization.repositories.OrganizationRepository;
import com.fathom.services.organization.service.OrganizationExtraInformationService;
import com.fathom.services.organization.service.OrganizationService;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.multipart.MultipartFile;

@AutoConfigureMockMvc
@ActiveProfiles("test")
class OrganizationControllerV1UploadLogoIntegrationTest extends IntegrationTestBase {

  @Autowired private MockMvc mockMvc;
  @Autowired private OrganizationService organizationService;
  @Autowired private OrganizationRepository organizationRepository;

  @MockBean private UserMangerClient userMangerClient;
  @MockBean private FileClient fileClient;
  @MockBean private OrganizationExtraInformationService organizationExtraInformationService;

  private UUID organizationId;
  private OrganizationDto testOrganization;

  @BeforeEach
  void setUp() {
    organizationRepository.deleteAll();

    // Mock file client responses
    FileUploadResponseDto mockFileResponse = new FileUploadResponseDto();
    mockFileResponse.setId("mock-file-id");
    when(fileClient.addFile(
            any(UUID.class), any(MultipartFile.class), anyString(), anyString(), anyString()))
        .thenReturn(mockFileResponse);

    // Create a test organization
    testOrganization = new OrganizationDto();
    testOrganization.setName("Test Organization");
    testOrganization.setDescription("Test Description");

    OrganizationDto savedOrganization =
        organizationService.save(testOrganization, "<EMAIL>");
    organizationId = savedOrganization.getId();
  }

  @Test
  void shouldUploadLogoSuccessfully() throws Exception {
    // Given
    MockMultipartFile logoFile =
        new MockMultipartFile("file", "logo.png", "image/png", "test image content".getBytes());

    // When & Then
    mockMvc
        .perform(
            multipart("/api/v1/organizations/{organizationId}/logo", organizationId)
                .file(logoFile)
                .contentType(MediaType.MULTIPART_FORM_DATA))
        .andDo(print())
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.id").value(organizationId.toString()))
        .andExpect(jsonPath("$.name").value("Test Organization"));
  }

  @Test
  void shouldReturnNotFoundWhenOrganizationDoesNotExist() throws Exception {
    // Given
    UUID nonExistentId = UUID.randomUUID();
    MockMultipartFile logoFile =
        new MockMultipartFile("file", "logo.png", "image/png", "test image content".getBytes());

    // When & Then
    mockMvc
        .perform(
            multipart("/api/v1/organizations/{organizationId}/logo", nonExistentId)
                .file(logoFile)
                .contentType(MediaType.MULTIPART_FORM_DATA))
        .andDo(print())
        .andExpect(status().isBadRequest());
  }

  @Test
  void shouldReturnBadRequestForInvalidFileType() throws Exception {
    // Given
    MockMultipartFile invalidFile =
        new MockMultipartFile(
            "file", "document.pdf", "application/pdf", "test pdf content".getBytes());

    // When & Then
    mockMvc
        .perform(
            multipart("/api/v1/organizations/{organizationId}/logo", organizationId)
                .file(invalidFile)
                .contentType(MediaType.MULTIPART_FORM_DATA))
        .andDo(print())
        .andExpect(status().isInternalServerError());
  }

  @Test
  void shouldReturnBadRequestForEmptyFile() throws Exception {
    // Given
    MockMultipartFile emptyFile =
        new MockMultipartFile("file", "empty.png", "image/png", new byte[0]);

    // When & Then
    mockMvc
        .perform(
            multipart("/api/v1/organizations/{organizationId}/logo", organizationId)
                .file(emptyFile)
                .contentType(MediaType.MULTIPART_FORM_DATA))
        .andDo(print())
        .andExpect(status().isInternalServerError());
  }
}
