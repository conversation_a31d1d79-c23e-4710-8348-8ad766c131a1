package com.fathom.services.organization;

import com.fathom.services.organization.model.dto.OrganizationDto;
import java.util.UUID;

public interface OrganizationData {
  default OrganizationDto.OrganizationDtoBuilder anOrganization() {
    return OrganizationDto.builder()
        .name(UUID.randomUUID().toString())
        .displayName("New Organization")
        .description("This is a new organization")
        .headquarters("New York")
        .industry("Technology")
        .latitude(0.0)
        .longitude(0.0);
  }
}
