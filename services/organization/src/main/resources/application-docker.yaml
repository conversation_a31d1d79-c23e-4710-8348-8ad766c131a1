spring:
  cloud:
    discovery:
      client:
        simple:
          instances:
            usermanagement:
              - uri: ${USER_MANAGEMENT_SVC_NAME:http://localhost:8059/}
            fileservice:
              - uri: ${FILE_SERVICE_NAME:http://localhost:8015/}
    loadbalancer:
      ribbon:
        enabled: false
    kubernetes:
      enabled: false
      loadbalancer:
        enabled: false
      discovery:
        enabled: false
feign:
  hystrix:
    enabled: false