server:
  port: ${ORGANI<PERSON>ATION_SERVICE_PORT:8045}

spring:
  application:
    name: ${ORGANIZATION_SERVICE_NAME:organization-service}
  flyway:
    enabled: ${FLYWAY_MIGRATION_ENABLED:false}
    locations: classpath:db/migration
    url: ${spring.datasource.url}
    user: ${spring.datasource.username}
    password: ${spring.datasource.password}
    schemas: organization
  datasource:
    url: jdbc:postgresql://${POSTGRES_HOST:localhost}:${POSTGRES_PORT:5432}/${POSTGRES_DATABASE:fathom}
    username: ${POSTGRES_USER:postgres}
    password: ${POSTGRES_PASSWORD:postgres}
  jpa:
    open-in-view: false
    show-sql: true
    database: postgresql
    hibernate:
      ddl-auto: update

  cloud:
    loadbalancer:
      ribbon:
        enabled: false
    kubernetes:
      loadbalancer:
        mode: service
      discovery:
        all-namespaces: false
  sql.init.mode: always
  sql.init.schema-locations: classpath:schema-postgres.sql

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: ALWAYS

fathom:
  version: "@project.version@"

services:
  usermanagement:
    name: ${USER_MANAGEMENT_SVC_NAME:usermanagement:8059}
  fileservice:
    name: ${FILE_SERVICE_NAME:fileservice:8015}



logging:
  level:
    root: INFO
    org:
      springframework:
        boot:
          autoconfigure:
            web:
              reactive: DEBUG
        cloud:
          gateway: DEBUG
  pattern:
    # Logging pattern containing traceId and spanId; no longer provided through Sleuth by default
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"

springdoc:
  api-docs:
    version: "openapi_3_0"

diagnostics:
  service-name: "organization"
  version: ${VERSION:0.0.1-dev}
  build-timestamp: ${BUILD_TIMESTAMP:1970-01-01T00:00:00Z}
  git-hash: ${GIT_HASH:unknown}
  git-branch: ${GIT_BRANCH:main}
  git-commit-timestamp: ${GIT_COMMIT_TIMESTAMP:1970-01-01T00:00:00Z}