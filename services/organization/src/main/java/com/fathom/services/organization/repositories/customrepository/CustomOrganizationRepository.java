package com.fathom.services.organization.repositories.customrepository;

import com.fathom.services.organization.model.entity.Organization;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface CustomOrganizationRepository extends CustomBaseRepository<Organization> {
  Page<Organization> findOrganizationByOwnerEmailPaged(String email, Pageable pageable);
}
