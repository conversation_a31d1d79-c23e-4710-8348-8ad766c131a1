package com.fathom.services.organization.mapper;

import com.fathom.services.organization.model.dto.OrganizationDto;
import com.fathom.services.organization.model.entity.Organization;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class OrganizationMapper {

  @Mappings({
    @Mapping(target = "id", ignore = true),
    @Mapping(target = "createdDate", ignore = true),
    @Mapping(target = "updatedDate", ignore = true),
  })
  public abstract Organization toTransientEntity(final OrganizationDto source);

  @Mappings({
    @Mapping(target = "createdDate", ignore = true),
    @Mapping(target = "updatedDate", ignore = true),
  })
  public abstract Organization toEntity(final OrganizationDto source);

  public abstract OrganizationDto toDto(final Organization source);

  @Mappings({
    @Mapping(target = "id", ignore = true),
    @Mapping(target = "createdDate", ignore = true),
    @Mapping(target = "updatedDate", ignore = true),
  })
  public abstract void updateProperties(
      final OrganizationDto source, final @MappingTarget Organization target);
}
