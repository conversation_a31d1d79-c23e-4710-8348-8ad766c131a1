package com.fathom.services.organization.service.impl;

import static com.google.common.io.Files.getFileExtension;

import com.fathom.lib.common.model.page.CommonPageDTO;
import com.fathom.services.organization.controllers.exceptions.UpdateOrganizationException;
import com.fathom.services.organization.feign.FileClient;
import com.fathom.services.organization.feign.UserMangerClient;
import com.fathom.services.organization.mapper.OrganizationMapper;
import com.fathom.services.organization.model.dto.OrganizationDto;
import com.fathom.services.organization.model.dto.OrganizationUserEmailDto;
import com.fathom.services.organization.model.dto.feign.FileUploadResponseDto;
import com.fathom.services.organization.model.entity.Organization;
import com.fathom.services.organization.repositories.OrganizationRepository;
import com.fathom.services.organization.service.*;
import jakarta.persistence.EntityExistsException;
import jakarta.persistence.EntityNotFoundException;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationServiceImpl implements OrganizationService {

  private final OrganizationMapper mapper;
  private final OrganizationRepository repository;
  private final UserMangerClient userMangerClient;
  private final OrganizationExtraInformationService extraInformationService;
  private final FileClient fileClient;

  @Override
  @Transactional(readOnly = true)
  public List<OrganizationDto> findAvailableOrganizations(final String email) {

    List<OrganizationDto> organizations =
        repository.findByIdIn(getOrganizationIdsForUser(email)).stream()
            .map(mapper::toDto)
            .toList();

    extraInformationService.loadExtraInformation(organizations);
    return organizations;
  }

  @Override
  @Transactional(readOnly = true)
  public CommonPageDTO<OrganizationDto> findAllByEmail(
      final String email, Integer pageNumber, Integer pageSize) {

    Page<OrganizationDto> organizationPage =
        getPageWithAssetsFilteredByName(email, pageNumber, pageSize).map(mapper::toDto);

    List<OrganizationDto> organizations = organizationPage.getContent();
    extraInformationService.loadExtraInformation(organizations);

    return new CommonPageDTO<>(
        organizations,
        organizationPage.getNumber() + 1,
        organizationPage.getSize(),
        organizationPage.getTotalPages(),
        organizationPage.getTotalElements());
  }

  @Override
  @Transactional(readOnly = true)
  public Optional<OrganizationDto> findById(final UUID organizationId) {
    Optional<OrganizationDto> organization = repository.findById(organizationId).map(mapper::toDto);
    organization.ifPresent(extraInformationService::loadExtraInformation);
    return organization;
  }

  @Override
  @Transactional(readOnly = true)
  public Optional<OrganizationDto> findByName(final String name) {
    Optional<OrganizationDto> organization = repository.findByName(name).map(mapper::toDto);
    organization.ifPresent(extraInformationService::loadExtraInformation);
    return organization;
  }

  @Override
  @Transactional(readOnly = true)
  public boolean checkOrganizationExistenceByName(String name) {
    return repository.existsByName(name);
  }

  @Override
  @Transactional
  public OrganizationDto save(final OrganizationDto target, final String ownerEmail) {

    String name = target.getName();
    if (repository.existsByName(name)) {
      throw new EntityExistsException(
          "Organization is already exist with name '%s'".formatted(name));
    }

    Organization entity = mapper.toTransientEntity(target);
    entity.setOwnerEmail(ownerEmail);

    userMangerClient.createSuperAdmin(entity.getId(), ownerEmail);

    OrganizationDto savedOrganization = mapper.toDto(repository.save(entity));
    extraInformationService.loadExtraInformation(savedOrganization);
    return savedOrganization;
  }

  @Override
  @Transactional
  public OrganizationDto update(final OrganizationDto target, final String ownerEmail) {
    Assert.notNull(target, "Organization model is null");
    Assert.notNull(ownerEmail, "Owner's email address is null");

    UUID orgId = target.getId();
    Organization entity =
        repository
            .findById(orgId)
            .orElseThrow(
                () ->
                    new EntityNotFoundException(
                        "Organization with id '%s' does not exist".formatted(orgId)));

    if (!ownerEmail.equals(entity.getOwnerEmail())) {
      throw new UpdateOrganizationException(
          ("User [%s] is not owner of the organization [%s]"
              .formatted(ownerEmail, target.getName())));
    }
    log.debug("Update organization properties: source={}, target={}", target, entity);

    mapper.updateProperties(target, entity);
    OrganizationDto updatedOrganization = mapper.toDto(repository.save(entity));
    extraInformationService.loadExtraInformation(updatedOrganization);
    return updatedOrganization;
  }

  @Override
  @Transactional
  public void deleteById(final UUID organizationId) {
    if (!repository.existsById(organizationId)) {
      throw new EntityNotFoundException(
          "Organization with id '%s' does not exist".formatted(organizationId));
    }
    repository.deleteById(organizationId);
  }

  private Page<Organization> getPageWithAssetsFilteredByName(
      String email, Integer pageNumber, Integer pageSize) {
    Pageable pageable = PageRequest.of(pageNumber - 1, pageSize);
    return repository.findOrganizationByOwnerEmailPaged(email, pageable);
  }

  public Set<UUID> getOrganizationIdsForUser(final String email) {

    List<OrganizationUserEmailDto> userEmailDtoList = userMangerClient.getUserRoles(email);

    return userEmailDtoList.stream()
        .map(OrganizationUserEmailDto::getOrganizationId)
        .collect(Collectors.toSet());
  }

  @Override
  @Transactional
  public OrganizationDto uploadLogo(final UUID organizationId, final MultipartFile file) {
    log.info("Uploading logo for organization: {}", organizationId);

    // Validate file
    validateImageFile(file);

    // Get organization
    Organization organization =
        repository
            .findById(organizationId)
            .orElseThrow(
                () ->
                    new EntityNotFoundException(
                        "Organization with id '%s' does not exist".formatted(organizationId)));

    // Delete existing logo if present
    try {
      fileClient.deleteById(organization.getId().toString());
      log.info("Deleted existing logo file: {}", organization.getId().toString());
    } catch (Exception e) {
      log.warn("Failed to delete existing logo file: {}", organization.getId().toString(), e);
    }

    // Upload new logo
    FileUploadResponseDto uploadResponse =
        fileClient.addFile(
            organizationId,
            file,
            "Organization logo",
            "organization-logo",
            file.getOriginalFilename());

    OrganizationDto result = mapper.toDto(repository.save(organization));
    extraInformationService.loadExtraInformation(result);

    log.info(
        "Successfully uploaded logo for organization: {}, fileId: {}",
        organizationId,
        uploadResponse.getId());

    return result;
  }

  private void validateImageFile(MultipartFile file) {
    if (file == null || file.isEmpty()) {
      throw new IllegalArgumentException("File cannot be null or empty");
    }

    String fileName = file.getOriginalFilename();
    if (fileName == null) {
      throw new IllegalArgumentException("File name cannot be null");
    }

    String fileExtension = getFileExtension(fileName).toLowerCase();
    Set<String> allowedImageExtensions = Set.of("jpg", "jpeg", "png", "gif", "bmp", "webp");

    if (!allowedImageExtensions.contains(fileExtension)) {
      throw new IllegalArgumentException(
          "Invalid file type. Allowed image extensions: " + allowedImageExtensions);
    }

    // Check file size (10MB limit)
    long maxSize = 10 * 1024 * 1024; // 10MB
    if (file.getSize() > maxSize) {
      throw new IllegalArgumentException("File size exceeds maximum limit of 10MB");
    }
  }
}
