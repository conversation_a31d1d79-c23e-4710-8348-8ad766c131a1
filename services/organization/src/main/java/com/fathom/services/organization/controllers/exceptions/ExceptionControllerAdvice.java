package com.fathom.services.organization.controllers.exceptions;

import jakarta.persistence.PersistenceException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class ExceptionControllerAdvice {

  @Data
  @Builder
  private static class ErrorResponse {
    private final LocalDateTime timestamp;
    private final String status;
    private final String message;
  }

  @ExceptionHandler(PersistenceException.class)
  public ResponseEntity<ErrorResponse> handlePersistentException(PersistenceException ex) {
    ErrorResponse res = of(HttpStatus.BAD_REQUEST, ex.getMessage());
    return ResponseEntity.badRequest().body(res);
  }

  @ExceptionHandler(AuthorizationProcessingException.class)
  public ResponseEntity<ErrorResponse> handleAuthorizationProcessingException(
      AuthorizationProcessingException ex) {
    ErrorResponse res = of(HttpStatus.BAD_REQUEST, ex.getMessage());
    return ResponseEntity.badRequest().body(res);
  }

  @ExceptionHandler(UpdateOrganizationException.class)
  public ResponseEntity<ErrorResponse> handleUpdateOrganizationException(
      UpdateOrganizationException ex) {
    ErrorResponse res = of(HttpStatus.UNAUTHORIZED, ex.getMessage());
    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(res);
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<Map<String, String>> handleValidationExceptions(
      MethodArgumentNotValidException ex) {
    Map<String, String> errors = new HashMap<>();
    ex.getBindingResult()
        .getAllErrors()
        .forEach(
            error -> {
              String fieldName = ((FieldError) error).getField();
              String errorMessage = error.getDefaultMessage();
              errors.put(fieldName, errorMessage);
            });
    return new ResponseEntity<>(errors, HttpStatus.BAD_REQUEST);
  }

  @ExceptionHandler(ServletRequestBindingException.class)
  public ResponseEntity<ErrorResponse> handleServletRequestBindingException(
      ServletRequestBindingException ex) {
    ErrorResponse res = of(HttpStatus.BAD_REQUEST, ex.getMessage());
    return ResponseEntity.badRequest().body(res);
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<ErrorResponse> handleException(Exception ex) {
    ErrorResponse res = of(HttpStatus.INTERNAL_SERVER_ERROR, ex.getMessage());
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(res);
  }

  private static ErrorResponse of(HttpStatus status, String message) {
    return ErrorResponse.builder()
        .timestamp(LocalDateTime.now(ZoneId.of("UTC")))
        .status(status.getReasonPhrase())
        .message(message)
        .build();
  }
}
