package com.fathom.services.organization;

import com.fathom.diagnostics.configuration.DiagnosticProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication(exclude = {KafkaAutoConfiguration.class})
@EnableDiscoveryClient
@EnableFeignClients
@EnableConfigurationProperties(DiagnosticProperties.class)
@ComponentScan(basePackages = {
        "com.fathom.services.organization",
        "com.fathom.diagnostics"
})

public class OrganizationApplication {

  public static void main(String[] args) {
    SpringApplication.run(OrganizationApplication.class, args);
  }
}
