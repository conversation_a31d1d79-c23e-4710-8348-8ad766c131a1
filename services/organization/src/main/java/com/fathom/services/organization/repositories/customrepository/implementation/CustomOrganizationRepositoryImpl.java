package com.fathom.services.organization.repositories.customrepository.implementation;

import com.fathom.services.organization.model.entity.Organization;
import com.fathom.services.organization.repositories.customrepository.CustomOrganizationRepository;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.support.PageableExecutionUtils;

@Slf4j
public class CustomOrganizationRepositoryImpl extends CustomBaseRepositoryImpl<Organization>
    implements CustomOrganizationRepository {

  @Override
  @SuppressWarnings("unchecked")
  public Page<Organization> findOrganizationByOwnerEmailPaged(String email, Pageable pageable) {
    CriteriaBuilder cb = em.getCriteriaBuilder();
    CriteriaQuery<Organization> query = cb.createQuery(Organization.class);

    Root<Organization> root = query.from(Organization.class);

    Predicate emailPredicat = cb.equal(root.get("ownerEmail"), email);

    query.where(emailPredicat);
    TypedQuery<Organization> typedQuery = em.createQuery(query);

    if (pageable.isPaged()) {
      typedQuery.setFirstResult((int) pageable.getOffset());
      typedQuery.setMaxResults(pageable.getPageSize());
    }

    return PageableExecutionUtils.getPage(
        typedQuery.getResultList(),
        pageable,
        () -> executeCountQuery(getCountQuery(emailPredicat)));
  }

  private Long executeCountQuery(TypedQuery<Long> query) {
    List<Long> resultList = query.getResultList();

    long total = 0L;
    for (Long count : resultList) {
      total += count == null ? 0 : count;
    }
    return total;
  }

  private TypedQuery<Long> getCountQuery(Predicate... predicates) {
    CriteriaBuilder cb = em.getCriteriaBuilder();
    CriteriaQuery<Long> query = cb.createQuery(Long.class);

    Root<?> root = query.from(Organization.class);

    query.where(predicates);
    query.select(cb.count(root));

    return em.createQuery(query);
  }
}
