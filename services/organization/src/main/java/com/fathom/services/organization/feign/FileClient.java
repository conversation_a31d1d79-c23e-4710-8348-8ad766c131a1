package com.fathom.services.organization.feign;

import com.fathom.services.organization.model.dto.feign.FileInfoDto;
import com.fathom.services.organization.model.dto.feign.FileUploadResponseDto;
import com.fathom.services.organization.model.dto.feign.TokenFileIdDto;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(name = "${services.fileservice.name}")
public interface FileClient {

  @PostMapping(path = "file/generate-download-token")
  List<TokenFileIdDto> getFilesDownloadTokens(@RequestBody Set<String> stringSet);

  @PostMapping("files/byUUID")
  List<FileInfoDto> findFileInformationByUUIDs(@RequestBody Set<UUID> uuidList);

  @PostMapping(path = "files", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  FileUploadResponseDto addFile(
      @RequestParam UUID uuid,
      @RequestPart MultipartFile file,
      @RequestParam(required = false) String description,
      @RequestParam(required = false) String classification,
      @RequestParam(required = false) String displayName);

  @DeleteMapping("files/{uuid}")
  void deleteByUuid(@PathVariable UUID uuid);

  @DeleteMapping("file/{id}")
  void deleteById(@PathVariable String id);
}
