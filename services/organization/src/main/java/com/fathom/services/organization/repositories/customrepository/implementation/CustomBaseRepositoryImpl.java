package com.fathom.services.organization.repositories.customrepository.implementation;

import com.fathom.lib.filters.filtering.jpa.impl.JpaFilteringRepositoryImpl;
import com.fathom.services.organization.repositories.customrepository.CustomBaseRepository;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;

public class CustomBaseRepositoryImpl<T> extends JpaFilteringRepositoryImpl<T>
    implements CustomBaseRepository<T> {

  @PersistenceContext protected EntityManager em;

  @Override
  public void detach(T entity) {
    em.detach(entity);
  }

  @Override
  public void persist(T entity) {
    em.persist(entity);
  }

  @Override
  public T merge(T entity) {
    return em.merge(entity);
  }
}
