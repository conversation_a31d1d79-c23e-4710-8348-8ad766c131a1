package com.fathom.services.organization.controllers.doc;

import com.fathom.services.organization.controllers.OrganizationControllerV1;
import com.fathom.services.organization.model.dto.OrganizationDto;
import com.fathom.services.organization.model.dto.schema.CreateOrganizationSchema;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import java.util.List;
import java.util.UUID;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

public interface OrganizationControllerV1Doc {

  @Operation(
      description = "Endpoint for retrieving all organizations available for user",
      responses = @ApiResponse(responseCode = "200"))
  ResponseEntity<List<OrganizationDto>> findAllByUser(
      @Parameter(
              name = OrganizationControllerV1.EMAIL_HEADER,
              description = "User's email",
              in = ParameterIn.HEADER)
          String email);

  @Operation(
      description = "Endpoint for retrieving single organization by it's identifier",
      parameters = {
        @Parameter(
            name = "organizationId",
            description = "Organization's identifier",
            in = ParameterIn.PATH)
      },
      responses = {
        @ApiResponse(responseCode = "200"),
        @ApiResponse(responseCode = "404", description = "No organization exist by given id")
      })
  ResponseEntity<OrganizationDto> findById(@Parameter(hidden = true) UUID organizationId);

  @Operation(
      description = "Endpoint for retrieving single organization by it's name",
      parameters = {
        @Parameter(name = "name", description = "Organization's name", in = ParameterIn.QUERY)
      },
      responses = {
        @ApiResponse(responseCode = "200"),
        @ApiResponse(responseCode = "404", description = "No organization exist by given name")
      })
  ResponseEntity<OrganizationDto> findByName(@Parameter(hidden = true) String name);

  @Operation(
      description = "Endpoint for checking organization existence by name",
      responses = @ApiResponse(responseCode = "200"))
  boolean existsByName(String name);

  @Operation(
      description = "Endpoint for save organization",
      parameters = {
        @Parameter(
            name = OrganizationControllerV1.EMAIL_HEADER,
            description = "User's email address",
            in = ParameterIn.HEADER)
      },
      requestBody =
          @RequestBody(
              description = "Organization create model",
              content =
                  @Content(schema = @Schema(implementation = CreateOrganizationSchema.class))),
      responses = @ApiResponse(responseCode = "201"))
  ResponseEntity<OrganizationDto> save(String email, OrganizationDto target);

  @Operation(
      description = "Endpoint for update organization",
      requestBody = @RequestBody(description = "Organization update model"),
      responses = @ApiResponse(responseCode = "200"))
  ResponseEntity<OrganizationDto> update(String email, OrganizationDto target);

  @Operation(
      description = "Endpoint for delete single organization by it's identifier",
      parameters = {
        @Parameter(
            name = "organizationId",
            description = "Organization's identifier",
            in = ParameterIn.PATH)
      },
      responses = {
        @ApiResponse(responseCode = "204"),
        @ApiResponse(
            responseCode = "400",
            description = "If organization does not exist with given identifier")
      })
  ResponseEntity<?> deleteById(@Parameter(hidden = true) UUID organizationId);

  @Operation(
      summary = "Upload organization logo",
      description = "Endpoint for uploading organization logo image through the file service")
  ResponseEntity<OrganizationDto> uploadLogo(UUID organizationId, MultipartFile file);
}
