package com.fathom.services.organization.controllers.doc;

import com.fathom.services.organization.model.dto.CityCountryDto;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;

public interface DateConfigurationControllerDoc {
  @Operation(
      summary = "Get all cites, counties, iso name, iso3 name",
      description = "Get all cites, counties, iso name, iso3 name")
  List<CityCountryDto> getCities();
}
