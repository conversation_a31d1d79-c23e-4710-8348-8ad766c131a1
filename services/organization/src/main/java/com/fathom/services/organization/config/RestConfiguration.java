package com.fathom.services.organization.config;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestConfiguration {

  @Bean
  @LoadBalanced
  @Profile("!docker")
  RestTemplate balancedRestTemplate() {
    return new RestTemplateBuilder().build();
  }

  @Bean
  @Primary
  @Profile("docker")
  RestTemplate restTemplate() {
    return new RestTemplateBuilder().build();
  }
}
