package com.fathom.services.organization.model.dto;

import com.fathom.services.organization.validations.ValidationCreateGroup;
import com.fathom.services.organization.validations.ValidationUpdateGroup;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationDto {

  @NotNull(groups = {ValidationUpdateGroup.class})
  private UUID id;

  // Explanation on the regex lengths:
  // Frontend validation is requiring at least one character for the organization
  // Subsequently the frontend will add 8 character uniqueness string
  // So for example when user creates organizaiton "org", the request here wil land with name
  // "org_cg238pz"
  // This explains why the regex minimum is length 9 - because of the uniqueness string + minimum
  // length of 1
  // The message is not updated to reflect this to preserve UI consistency
  @Pattern(
      regexp = "^[a-zA-Z0-9!@#$%^&*()_+\\-={}:;\"'<>,.?/\\[\\]\\\\| ]{9,38}$",
      message =
          "Name must be 1-30 characters long and can contain uppercase/lowercase letters, numbers, special characters, and spaces.")
  private String name;

  private String displayName;
  private String description;

  @NotNull(groups = {ValidationUpdateGroup.class})
  private String ownerEmail;

  private String logo;

  private String industry;
  private String headquarters;

  @NotNull(groups = {ValidationCreateGroup.class})
  private Double longitude;

  @NotNull(groups = {ValidationCreateGroup.class})
  private Double latitude;

  private LocalDateTime createdDate;
  private LocalDateTime updatedDate;
}
