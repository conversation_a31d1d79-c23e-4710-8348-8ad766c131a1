package com.fathom.services.organization.controllers;

import com.fathom.services.organization.controllers.doc.OrganizationControllerV1Doc;
import com.fathom.services.organization.model.dto.OrganizationDto;
import com.fathom.services.organization.service.OrganizationService;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import java.util.List;
import java.util.UUID;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/v1/organizations")
public class OrganizationControllerV1 implements OrganizationControllerV1Doc {
  public static final String EMAIL_HEADER = "X-Email";

  private final OrganizationService organizationService;

  public OrganizationControllerV1(OrganizationService organizationService) {
    this.organizationService = organizationService;
  }

  @Override
  @GetMapping
  public ResponseEntity<List<OrganizationDto>> findAllByUser(
      @RequestHeader(EMAIL_HEADER) @Parameter(required = true) String email) {
    return ResponseEntity.ok(organizationService.findAvailableOrganizations(email));
  }

  @Override
  @GetMapping("/{organizationId}")
  public ResponseEntity<OrganizationDto> findById(@PathVariable UUID organizationId) {
    return organizationService
        .findById(organizationId)
        .map(ResponseEntity::ok)
        .orElseGet(() -> ResponseEntity.notFound().build());
  }

  @Override
  @GetMapping(path = "/name", params = "name")
  public ResponseEntity<OrganizationDto> findByName(@RequestParam String name) {
    return organizationService
        .findByName(name)
        .map(ResponseEntity::ok)
        .orElseGet(() -> ResponseEntity.notFound().build());
  }

  @Override
  @GetMapping("/{name}/existsByName")
  public boolean existsByName(@PathVariable String name) {
    return organizationService.checkOrganizationExistenceByName(name);
  }

  @Override
  @PostMapping
  public ResponseEntity<OrganizationDto> save(
      @RequestHeader(EMAIL_HEADER) String email, @RequestBody @Valid OrganizationDto target) {
    return ResponseEntity.status(HttpStatus.CREATED).body(organizationService.save(target, email));
  }

  @Override
  @PutMapping
  public ResponseEntity<OrganizationDto> update(
      @RequestHeader(EMAIL_HEADER) String email, @RequestBody @Valid OrganizationDto target) {
    return ResponseEntity.ok(organizationService.update(target, email));
  }

  @Override
  @DeleteMapping("/{organizationId}")
  public ResponseEntity<?> deleteById(@PathVariable UUID organizationId) {
    organizationService.deleteById(organizationId);
    return ResponseEntity.noContent().build();
  }

  @Override
  @PostMapping(path = "/{organizationId}/logo", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResponseEntity<OrganizationDto> uploadLogo(
      @PathVariable UUID organizationId, @RequestPart MultipartFile file) {
    return ResponseEntity.ok(organizationService.uploadLogo(organizationId, file));
  }
}
