package com.fathom.services.organization.service;

import com.fathom.lib.common.model.page.CommonPageDTO;
import com.fathom.services.organization.model.dto.OrganizationDto;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.web.multipart.MultipartFile;

public interface OrganizationService {

  List<OrganizationDto> findAvailableOrganizations(final String email);

  CommonPageDTO<OrganizationDto> findAllByEmail(
      final String email, Integer pageNumber, Integer pageSize);

  Optional<OrganizationDto> findById(final UUID organizationId);

  Optional<OrganizationDto> findByName(final String name);

  boolean checkOrganizationExistenceByName(String name);

  OrganizationDto save(final OrganizationDto target, final String ownerEmail);

  OrganizationDto update(final OrganizationDto target, final String ownerEmail);

  void deleteById(final UUID organizationId);

  OrganizationDto uploadLogo(final UUID organizationId, final MultipartFile file);
}
