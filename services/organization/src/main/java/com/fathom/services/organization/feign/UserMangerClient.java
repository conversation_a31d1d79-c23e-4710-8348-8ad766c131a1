package com.fathom.services.organization.feign;

import com.fathom.services.organization.model.dto.OrganizationUserEmailDto;
import java.util.List;
import java.util.UUID;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${services.usermanagement.name}")
public interface UserMangerClient {

  @PostMapping(path = "users/roles/admin")
  void createSuperAdmin(
      @RequestHeader("x-organizationId") UUID organizationId,
      @RequestHeader("x-email") String email);

  @GetMapping(path = "users/roles")
  List<OrganizationUserEmailDto> getUserRoles(@RequestHeader("x-email") String email);
}
