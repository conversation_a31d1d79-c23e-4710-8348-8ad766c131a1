package com.fathom.services.organization.repositories;

import com.fathom.services.organization.model.entity.Organization;
import com.fathom.services.organization.repositories.customrepository.CustomOrganizationRepository;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface OrganizationRepository
    extends JpaRepository<Organization, UUID>, CustomOrganizationRepository {

  Optional<Organization> findByName(final String name);

  boolean existsByName(final String name);

  List<Organization> findAllByOwnerEmail(final String ownerEmail);

  @Query("select o from Organization o where o.id in ?1")
  List<Organization> findByIdIn(Collection<UUID> ids);
}
