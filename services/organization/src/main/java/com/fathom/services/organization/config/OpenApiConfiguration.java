package com.fathom.services.organization.config;

import org.springdoc.core.customizers.OpenApiCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfiguration {
  @Bean
  OpenApiCustomizer openApiCustomiser(@Value("${fathom.version}") String version) {
    return openApi -> openApi.getInfo().setVersion(version);
  }
}
