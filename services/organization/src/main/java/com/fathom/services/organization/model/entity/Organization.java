package com.fathom.services.organization.model.entity;

import jakarta.persistence.*;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import lombok.Data;

@Data
@Entity
@Table(name = "organizations", schema = "organization")
public class Organization implements Serializable {

  @Serial private static final long serialVersionUID = 1;

  @Id private UUID id = UUID.randomUUID();

  @Column(name = "name", unique = true)
  private String name;

  @Column(name = "display_name")
  private String displayName;

  @Column(name = "owner_email", updatable = false, nullable = false)
  private String ownerEmail;

  @Column(name = "description")
  private String description;

  @Column(name = "logo_url")
  private String logoUrl;

  @Column(name = "industry")
  private String industry;

  @Column(name = "headquarters")
  private String headquarters;

  @Column(name = "longitude")
  private Double longitude;

  @Column(name = "latitude")
  private Double latitude;

  @Column(name = "created_date", nullable = false, updatable = false)
  private LocalDateTime createdDate;

  @Column(name = "updated_date")
  private LocalDateTime updatedDate;

  @ElementCollection
  @MapKeyColumn(name = "file_key")
  @Column(name = "file_value")
  @CollectionTable(
      name = "organization_related_files",
      joinColumns = @JoinColumn(name = "organization_id"))
  private Map<String, String> relatedFiles;

  @PrePersist
  private void basePrePersist() {
    this.createdDate = LocalDateTime.now();
    this.updatedDate = LocalDateTime.now();
  }

  @PreUpdate
  private void basePreUpdate() {
    this.updatedDate = LocalDateTime.now();
  }
}
