package com.fathom.services.organization.service.impl;

import com.fathom.services.organization.model.dto.CityCountryDto;
import com.fathom.services.organization.service.DateService;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class DateServiceImpl implements DateService {

  private final ResourceLoader resourceLoader;

  @Override
  public List<CityCountryDto> getCities() {

    try {
      InputStream inputStream =
          resourceLoader.getResource("classpath:cities/world_cities_txt.txt").getInputStream();
      File tmp = File.createTempFile(this.getClass().getSimpleName(), null);
      FileUtils.copyInputStreamToFile(inputStream, tmp);
      Set<String> cites = new LinkedHashSet<>(FileUtils.readLines(tmp, StandardCharsets.UTF_8));

      return cites.stream()
          .map(x -> x.split(","))
          .map(x -> new CityCountryDto(x[0], x[1], x[2], Integer.parseInt(x[3])))
          .toList();

    } catch (IOException ioex) {
      log.error(
          " Cannot read file with cities. Will use full IANA list of timezones. IOException is thrown : {}",
          ioex.getMessage());
    }
    return List.of();
  }
}
