package com.fathom.services.organization.service;

import com.fathom.services.organization.feign.FileClient;
import com.fathom.services.organization.model.dto.OrganizationDto;
import com.fathom.services.organization.model.dto.feign.FileInfoDto;
import com.fathom.services.organization.model.dto.feign.TokenFileIdDto;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationExtraInformationService {
  private final FileClient fileClient;

  public void loadExtraInformation(List<OrganizationDto> organizationList) {
    if (organizationList == null || organizationList.isEmpty()) {
      return;
    }

    // Get organization IDs for file lookup
    Set<UUID> organizationIds =
        organizationList.stream()
            .map(OrganizationDto::getId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

    if (organizationIds.isEmpty()) {
      return;
    }

    try {
      // Fetch file information for organizations
      List<FileInfoDto> logoFiles = fileClient.findFileInformationByUUIDs(organizationIds);

      if (logoFiles.isEmpty()) {
        return;
      }

      // Extract file IDs for token retrieval
      Set<String> logoFileIds =
          logoFiles.stream().map(FileInfoDto::getId).collect(Collectors.toSet());

      log.info(
          "Fetching download token for organization logo files with ids: {}",
          logoFileIds.stream().collect(Collectors.joining(",", "[", "]")));

      // Get download tokens for logo files
      List<TokenFileIdDto> tokenFileIdList = fileClient.getFilesDownloadTokens(logoFileIds);

      // Create a map for quick lookup: organizationId -> token
      Map<UUID, String> organizationLogoTokenMap = new HashMap<>();

      for (FileInfoDto logoFile : logoFiles) {
        TokenFileIdDto tokenFileIdDto =
            tokenFileIdList.stream()
                .filter(token -> token.getId().equals(logoFile.getId()))
                .findFirst()
                .orElse(null);

        if (tokenFileIdDto != null) {
          organizationLogoTokenMap.put(logoFile.getObjectUUID(), tokenFileIdDto.getToken());
        }
      }

      // Set logo tokens for organizations
      for (OrganizationDto organization : organizationList) {
        String logoToken = organizationLogoTokenMap.get(organization.getId());
        if (logoToken != null) {
          organization.setLogo(logoToken);
        }
      }

    } catch (Exception e) {
      log.error("Error loading extra information for organizations", e);
      // Don't fail the main operation if logo loading fails
    }
  }

  public void loadExtraInformation(OrganizationDto organization) {
    if (organization != null) {
      loadExtraInformation(List.of(organization));
    }
  }
}
