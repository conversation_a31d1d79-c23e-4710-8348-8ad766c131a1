package com.fathom.services.organization.controllers;

import com.fathom.services.organization.controllers.doc.DateConfigurationControllerDoc;
import com.fathom.services.organization.model.dto.CityCountryDto;
import com.fathom.services.organization.service.DateService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class DateConfigurationControllerV1 implements DateConfigurationControllerDoc {

  private final DateService timeZoneService;

  @Override
  @GetMapping("/api/v1/organizations/data-configuration/cities")
  public List<CityCountryDto> getCities() {
    return timeZoneService.getCities();
  }
}
