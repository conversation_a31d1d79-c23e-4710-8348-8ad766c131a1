package com.fathom.services.organization.model.dto.schema;

import com.fathom.services.organization.model.dto.OrganizationDto;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.UUID;

@Schema(hidden = true, description = "Schema used for create an organization")
public class CreateOrganizationSchema extends OrganizationDto {

  @Hidden
  @Override
  public UUID getId() {
    return super.getId();
  }

  @Hidden
  @Override
  public LocalDateTime getCreatedDate() {
    return super.getCreatedDate();
  }

  @Hidden
  @Override
  public LocalDateTime getUpdatedDate() {
    return super.getUpdatedDate();
  }

  @Hidden
  @Override
  public String getOwnerEmail() {
    return super.getOwnerEmail();
  }
}
