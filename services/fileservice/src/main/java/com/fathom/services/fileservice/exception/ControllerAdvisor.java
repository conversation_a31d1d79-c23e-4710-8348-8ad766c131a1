package com.fathom.services.fileservice.exception;

import static com.fathom.lib.common.exception.RestException.handleRestException;

import com.fathom.lib.common.exception.RestException;
import com.fathom.lib.common.model.exception.ErrorResponse;
import java.time.LocalDateTime;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

@Slf4j
@RestControllerAdvice
public class ControllerAdvisor {

  @ExceptionHandler(value = {Exception.class})
  public ResponseEntity<ErrorResponse> globalHandler(Exception ex, WebRequest request)
      throws Exception {

    log.error(ex.getMessage(), ex);

    if (ex instanceof RestException exception) {
      return handleRestException(exception, request);
    } else if (ex instanceof MethodArgumentNotValidException exception) {
      return handleMethodArgumentNotValidException(exception, request);
    } else if (ex instanceof ConstraintViolationException exception) {
      return handleConstraintViolationException(exception, request);
    } else if (ex instanceof DataIntegrityViolationException exception) {
      return handleDataIntegrityViolationException(exception, request);
    } else {
      throw ex;
    }
  }

  private static ResponseEntity<ErrorResponse> handleMethodArgumentNotValidException(
      MethodArgumentNotValidException ex, WebRequest request) {
    StringBuilder sb = new StringBuilder();

    for (ObjectError error : ex.getBindingResult().getAllErrors()) {
      FieldError fieldError = (FieldError) error;
      sb.append("Field ")
          .append("'")
          .append(fieldError.getField())
          .append("'")
          .append(" from ")
          .append("'")
          .append(error.getObjectName())
          .append("'")
          .append(" ")
          .append(error.getDefaultMessage())
          .append(". ");
    }

    ErrorResponse errorResponse = badRequestErrorResponse(sb.toString());
    return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
  }

  private static ResponseEntity<ErrorResponse> handleConstraintViolationException(
      ConstraintViolationException ex, WebRequest request) {
    StringBuilder sb = new StringBuilder();

    for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {

      sb.append(violation.getRootBeanClass().getName())
          .append(" ")
          .append(violation.getPropertyPath())
          .append(": ")
          .append(violation.getMessage())
          .append("\n");
    }

    ErrorResponse errorResponse = badRequestErrorResponse(sb.toString());

    return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
  }

  private static ResponseEntity<ErrorResponse> handleDataIntegrityViolationException(
      DataIntegrityViolationException ex, WebRequest request) {
    ErrorResponse errorResponse = badRequestErrorResponse(ex.getRootCause().getMessage());
    return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
  }

  private static ErrorResponse noContentErrorResponse(String errorMessage) {
    return ErrorResponse.newBuilder()
        .setTimestamp(LocalDateTime.now())
        .setStatus(HttpStatus.NO_CONTENT.value())
        .setError(HttpStatus.NO_CONTENT.getReasonPhrase())
        .setMessage(errorMessage)
        .build();
  }

  private static ErrorResponse badRequestErrorResponse(String errorMessage) {
    return ErrorResponse.newBuilder()
        .setTimestamp(LocalDateTime.now())
        .setStatus(HttpStatus.BAD_REQUEST.value())
        .setError(HttpStatus.BAD_REQUEST.getReasonPhrase())
        .setMessage(errorMessage)
        .build();
  }

  private static ErrorResponse notFoundErrorResponse(String errorMessage) {
    return ErrorResponse.newBuilder()
        .setTimestamp(LocalDateTime.now())
        .setStatus(HttpStatus.NOT_FOUND.value())
        .setError(HttpStatus.NOT_FOUND.getReasonPhrase())
        .setMessage(errorMessage)
        .build();
  }

  private static ErrorResponse internalServerErrorResponse(String errorMessage) {
    return ErrorResponse.newBuilder()
        .setTimestamp(LocalDateTime.now())
        .setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value())
        .setError(HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase())
        .setMessage(errorMessage)
        .build();
  }
}
