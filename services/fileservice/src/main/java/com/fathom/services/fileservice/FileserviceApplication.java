package com.fathom.services.fileservice;

import com.fathom.diagnostics.configuration.DiagnosticProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@EnableConfigurationProperties(DiagnosticProperties.class)
@ComponentScan(basePackages = {
        "com.fathom.services.fileservice",
        "com.fathom.diagnostics"
})
public class FileserviceApplication {
  public static void main(String[] args) {
    SpringApplication.run(FileserviceApplication.class, args);
  }
}
