package com.fathom.services.fileservice.service.impl;

import static com.fathom.services.fileservice.exception.ExMessages.*;

import com.fathom.lib.common.exception.RestException;
import com.fathom.lib.common.model.file.FathomFileDTO;
import com.fathom.lib.common.model.file.FileUploadAttributesDTO;
import com.fathom.lib.common.model.page.CommonPageDTO;
import com.fathom.lib.common.utils.extractor.FieldExtractor;
import com.fathom.lib.filters.model.Filter;
import com.fathom.services.fileservice.dto.FathomFileWithContent;
import com.fathom.services.fileservice.dto.GetFilesRequestBody;
import com.fathom.services.fileservice.dto.TokenFileIdDto;
import com.fathom.services.fileservice.entity.FathomFile;
import com.fathom.services.fileservice.mapper.FathomFileMapper;
import com.fathom.services.fileservice.repository.FathomFileRepository;
import com.fathom.services.fileservice.service.FathomFileService;
import com.fathom.services.fileservice.service.JwtService;
import com.mongodb.BasicDBObject;
import com.mongodb.client.gridfs.model.GridFSFile;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
@Qualifier("mongo")
public class MongoFathomFileServiceImpl implements FathomFileService {

  private final GridFsTemplate gridFsTemplate;
  private final FathomFileRepository fathomFileRepository;
  private final JwtService jwtService;

  @Value("${upload.common-upload.max-file-size}")
  private Long commonUploadMaxFileSize;

  @Value("${upload.direct-upload.max-file-size}")
  private Long directUploadMaxFileSize;

  @Value("${upload.direct-upload.base-url}")
  private String baseUrl;

  private static final String METADATA_MD5_DIGEST_KEY = "md5";
  private static final String GRID_FS_ID_FIELD_NAME = "_id";

  public FathomFileDTO addFile(
      MultipartFile file,
      String uuid,
      String description,
      String classification,
      String displayName) {
    try (InputStream inputStream = file.getInputStream()) {
      String md5 = DigestUtils.md5DigestAsHex(file.getBytes());
      GridFSFile existing = findByHash(md5);

      ObjectId newFileObjectId;

      if (existing != null) {
        log.info(
            "File with same md5 digest is found. Already existing file will be used. MD5 digest: '{}'",
            md5);
        newFileObjectId = existing.getObjectId();
      } else {
        log.info("Trying to persist new file. MD5 digest: '{}'", md5);
        BasicDBObject metadata = new BasicDBObject();
        metadata.put(METADATA_MD5_DIGEST_KEY, md5);
        newFileObjectId = gridFsTemplate.store(inputStream, file.getOriginalFilename(), metadata);
      }

      FathomFile fathomFile =
          FathomFile.builder()
              .objectUUID(uuid)
              .fileId(newFileObjectId.toString())
              .fileName(file.getOriginalFilename())
              .classification(classification)
              .description(description)
              .date(LocalDateTime.now())
              .displayName(displayName)
              .content(extractContent(file))
              .contentType(file.getContentType())
              .fileSize(file.getSize())
              .build();

      fathomFile = fathomFileRepository.save(fathomFile);

      return FathomFileMapper.INSTANCE.entityToNewDTO(fathomFile);
    } catch (IOException e) {
      throw new RestException(HttpStatus.INTERNAL_SERVER_ERROR, FILE_SAVE_FAILURE, e.getMessage());
    }
  }

  @Override
  public FathomFileDTO uploadFile(MultipartFile file, FathomFileDTO fileInfoDTO) {
    Long fileSize = file.getSize();

    if (commonUploadMaxFileSize < fileSize) {
      throw new RestException(
          HttpStatus.BAD_REQUEST, FILE_SIZE_LIMIT_EXCEEDED, commonUploadMaxFileSize, fileSize);
    }

    return saveFile(file, fileInfoDTO);
  }

  @Override
  public FileUploadAttributesDTO generateUploadAttributes(FathomFileDTO fileInfoDTO) {
    Long fileSize = fileInfoDTO.getFileSize();

    if (directUploadMaxFileSize < fileSize) {
      throw new RestException(
          HttpStatus.BAD_REQUEST, FILE_SIZE_LIMIT_EXCEEDED, directUploadMaxFileSize, fileSize);
    }

    return FileUploadAttributesDTO.builder()
        .token(jwtService.generateToken(fileInfoDTO))
        .url(baseUrl)
        .build();
  }

  @Override
  @SuppressWarnings("unchecked")
  public Object getFilesWithFiltering(GetFilesRequestBody requestBody) {
    Object result;

    List<Filter> filterList = requestBody.getFilters();
    List<String> fieldNames = requestBody.getFields();
    int pageNumber = requestBody.getPageNumber();
    int pageSize = requestBody.getPageSize();

    if (!requestBody.isPaged()) {
      List<FathomFileDTO> fathomFiles =
          fathomFileRepository
              .findFathomFilesWithFiltering(filterList, requestBody.getSortingOptions()).stream()
              .map(FathomFileMapper.INSTANCE::entityToNewDTO)
              .toList();

      if (!CollectionUtils.isEmpty(fieldNames)) {
        result = FieldExtractor.getFieldValuesInList(fathomFiles, fieldNames);
      } else {
        result = fathomFiles;
      }
    } else {
      Page<FathomFile> fathomFilesPage =
          fathomFileRepository.findFathomFilesWithFilteringPaged(
              filterList,
              requestBody.getSortingOptions(),
              PageRequest.of(pageNumber - 1, pageSize));

      List<FathomFileDTO> pageContent =
          fathomFilesPage.getContent().stream()
              .map(FathomFileMapper.INSTANCE::entityToNewDTO)
              .toList();

      CommonPageDTO pageDTO =
          new CommonPageDTO<>(
              pageContent,
              fathomFilesPage.getNumber() + 1,
              fathomFilesPage.getSize(),
              fathomFilesPage.getTotalPages(),
              fathomFilesPage.getTotalElements());

      // If fieldNames is specified we trying to extract specific fields from model.
      if (!CollectionUtils.isEmpty(fieldNames)) {
        pageDTO.setContent(FieldExtractor.getFieldValuesInList(pageContent, fieldNames));
      }

      result = pageDTO;
    }

    return result;
  }

  @Override
  public FathomFileDTO uploadFileDirectly(String token, MultipartFile file) {
    FathomFileDTO fathomFileDTO = jwtService.validateAndGetBody(token);

    Long fileSize = file.getSize();

    if (directUploadMaxFileSize < fileSize) {
      throw new RestException(
          HttpStatus.BAD_REQUEST, FILE_SIZE_LIMIT_EXCEEDED, directUploadMaxFileSize, fileSize);
    }

    return saveFile(file, fathomFileDTO);
  }

  public FathomFileDTO updateFileInfo(FathomFileDTO fileDTO) {
    FathomFile existingFathomFile =
        fathomFileRepository
            .findById(fileDTO.getId())
            .orElseThrow(
                () -> new RestException(HttpStatus.NOT_FOUND, FILE_NOT_EXISTS, fileDTO.getId()));

    FathomFileMapper.INSTANCE.dtoToExistingEntity(fileDTO, existingFathomFile);

    existingFathomFile = fathomFileRepository.save(existingFathomFile);

    return FathomFileMapper.INSTANCE.entityToNewDTO(existingFathomFile);
  }

  /**
   * Safely deletes all files linked to resource UUID specified.
   *
   * @param uuid external resource uuid (asset, template etc.).
   * @return number of deleted files.
   */
  public int deleteByUUID(String uuid) {
    List<FathomFile> filesToDelete = fathomFileRepository.findAllByObjectUUID(uuid);

    for (FathomFile fileToDelete : filesToDelete) {
      if (!isFileInterlinked(fileToDelete)) {
        gridFsTemplate.delete(
            new Query(Criteria.where(GRID_FS_ID_FIELD_NAME).is(fileToDelete.getFileId())));
      } else {
        log.debug(
            "File with id '{}' is used by another resources. Skipping.", fileToDelete.getFileId());
      }

      fathomFileRepository.delete(fileToDelete);
    }

    return filesToDelete.size();
  }

  /**
   * Safely deletes link between resource UUID and file, then checks if file linked to another
   * resource and deletes it otherwise
   *
   * @param id link id
   */
  public void deleteByID(final String id) {
    Optional<FathomFile> fathomFileOptional = fathomFileRepository.findById(id);

    if (fathomFileOptional.isPresent()) {
      FathomFile fathomFile = fathomFileOptional.get();
      String fileId = fathomFile.getFileId();

      if (!isFileInterlinked(fathomFile)) {
        log.debug("File with id {} has no other links then {}. Deleting safely.", fileId, id);
        gridFsTemplate.delete(new Query(Criteria.where(GRID_FS_ID_FIELD_NAME).is(fileId)));
      } else {
        log.info(
            "File with id {} attached to link with id {} is used somewhere else. Keeping it in DB",
            fileId,
            id);
      }

      fathomFileRepository.deleteById(id);
    }
  }

  public FathomFileDTO getFileByID(String id) {
    return fathomFileRepository
        .findById(id)
        .map(FathomFileMapper.INSTANCE::entityToNewDTO)
        .orElse(null);
  }

  public Page<FathomFileDTO> findFathomFilesByUUIDWithFiltering(
      Pageable page, Map<String, String> filter) {
    Page<FathomFile> fileDescriptions =
        fathomFileRepository.findFathomFilesWithFilteringPaged(page, filter);
    return fileDescriptions.map(FathomFileMapper.INSTANCE::entityToNewDTO);
  }

  public Optional<FathomFileWithContent> downloadFileByID(String id) throws IOException {
    var maybeFile = fathomFileRepository.findById(id);

    if (maybeFile.isEmpty()) {
      return Optional.empty();
    }

    var fathomFile = maybeFile.get();

    GridFSFile gridFSFile =
        gridFsTemplate.findOne(
            new Query(Criteria.where(GRID_FS_ID_FIELD_NAME).is(fathomFile.getFileId())));

    GridFsResource resource = null;

    if (Objects.nonNull(gridFSFile)) {
      resource = gridFsTemplate.getResource(gridFSFile);
    }

    if (Objects.nonNull(resource)) {
      String contentType = fathomFile.getContentType();
      String fileName = resource.getFilename();
      try (InputStream inputStream = resource.getInputStream()) {
        if ((Objects.isNull(contentType) || contentType.isEmpty()) && Objects.nonNull(fileName)) {
          Tika tika = new Tika();

          contentType = tika.detect(inputStream);
          fathomFile.setContentType(contentType);

          var fileWithMetadata =
              new FathomFileWithContent(
                  FathomFileMapper.INSTANCE.entityToNewDTO(fathomFile), inputStream);

          return Optional.of(fileWithMetadata);
        }
      } catch (IOException e) {
        log.warn("Caught exception", e);
        throw new IOException("Exception reading from mongo");
      }
    }
    return Optional.empty();
  }

  // TODO check usages and potentially remove
  @Override
  public Optional<FathomFileWithContent> downloadByToken(String token) {
    return Optional.empty();
  }

  @Override
  public List<TokenFileIdDto> generateDownloadToken(Set<String> id) {
    return null;
  }

  @Override
  public List<FathomFileDTO> getManyByUUID(Set<String> uuidList) {
    return null;
  }

  private FathomFileDTO saveFile(MultipartFile file, FathomFileDTO fileInfoDTO) {
    try (InputStream inputStream = file.getInputStream()) {
      String md5 = DigestUtils.md5DigestAsHex(file.getBytes());
      GridFSFile existing = findByHash(md5);

      ObjectId newFileObjectId;

      if (existing != null) {
        log.info(
            "File with same md5 digest is found. Already existing file will be used. MD5 digest: '{}'",
            md5);
        newFileObjectId = existing.getObjectId();
      } else {
        log.info("Trying to persist new file. MD5 digest: '{}'", md5);
        BasicDBObject metadata = new BasicDBObject();
        metadata.put(METADATA_MD5_DIGEST_KEY, md5);
        newFileObjectId = gridFsTemplate.store(inputStream, file.getOriginalFilename(), metadata);
      }

      FathomFile fathomFile = FathomFileMapper.INSTANCE.dtoToNewEntity(fileInfoDTO);
      fathomFile.setFileId(newFileObjectId.toString());
      fathomFile.setFileName(file.getOriginalFilename());
      fathomFile.setDate(LocalDateTime.now());
      fathomFile.setContent(extractContent(file));
      fathomFile.setContentType(file.getContentType());
      fathomFile.setFileSize(file.getSize());

      fathomFile = fathomFileRepository.save(fathomFile);

      return FathomFileMapper.INSTANCE.entityToNewDTO(fathomFile);
    } catch (IOException e) {
      throw new RestException(HttpStatus.INTERNAL_SERVER_ERROR, FILE_SAVE_FAILURE, e.getMessage());
    }
  }

  /**
   * Checks if file is linked with some another resources
   *
   * @param fileToDelete file for deletion entity
   * @return boolean check result value
   */
  private boolean isFileInterlinked(FathomFile fileToDelete) {
    String fileId = fileToDelete.getFileId();
    String fileToDeleteUUID = fileToDelete.getObjectUUID();

    List<FathomFile> allLinkedFiles = fathomFileRepository.findByFileId(fileId);

    for (FathomFile linkToFile : allLinkedFiles) {
      if (!linkToFile.getObjectUUID().equals(fileToDeleteUUID)) {
        return true;
      }
    }

    return false;
  }

  private GridFSFile findByHash(String hash) {
    return gridFsTemplate.findOne(new Query(Criteria.where("metadata.md5").is(hash)));
  }

  public static String extractContent(MultipartFile file) {
    Tika tika = new Tika();

    String content;
    try (InputStream inputStream = file.getInputStream()) {
      content = tika.parseToString(inputStream);
    } catch (Exception e) {
      throw new RestException(HttpStatus.BAD_REQUEST, CONTENT_PARSING_FAILURE, e.getMessage());
    }

    return content;
  }
}
