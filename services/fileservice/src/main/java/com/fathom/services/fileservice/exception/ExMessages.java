package com.fathom.services.fileservice.exception;

public final class ExMessages {

  public static final String FILE_SAVE_FAILURE = "Can`t save file. Cause: '%s'";
  public static final String INVALID_TOPIC_NAME =
      "If you have set marker 'sendToKafkaTopic' to true, you should "
          + "specify existent kafka topic name with parameter 'topicName' in other cases you will see this message."
          + "Specified topic name: '%s'.";
  public static final String FILE_SIZE_LIMIT_EXCEEDED =
      "File size limit exceeded. Max file size: '%s' bytes. " + "Actual file size: '%s' bytes.";
  public static final String FILE_NOT_EXISTS = "File with id: '%s' not exists.";
  public static final String FILE_DOWNLOAD_FAILURE =
      "File downloading failed due to internal server issues. " + "Cause: '%s'";
  public static final String FILE_DELETE_FAILURE =
      "File deleting failed due to internal server issues. " + "Cause: '%s'";
  public static final String OBJECT_SERIALIZATION_FAILURE =
      "Can`t serialize object '%s' to json. Message: '%s'.";
  public static final String CLAIMS_PARSING_FAILURE =
      "Can`t parse jwt token claims. Message: '%s'.";
  public static final String CLAIM_PARSING_FAILURE =
      "Can`t parse value of claim with name: '%s'. Root cause: '%s'.";
  public static final String CONTENT_TYPE_PARSING_FAILURE =
      "Сan`t determine content type of specified file. Root cause: '%s'";
  public static final String CONTENT_PARSING_FAILURE =
      "Сan`t determine content of specified file. Root cause: '%s'";

  private ExMessages() {}
}
