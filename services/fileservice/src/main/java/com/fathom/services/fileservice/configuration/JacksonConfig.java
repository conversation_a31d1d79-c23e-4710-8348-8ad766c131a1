package com.fathom.services.fileservice.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fathom.lib.common.jackson.MapperSingleton;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JacksonConfig {

  @Bean
  public ObjectMapper objectMapper() {
    return MapperSingleton.getMapper();
  }
}
