package com.fathom.services.fileservice.service.impl;

import static com.fathom.services.fileservice.exception.ExMessages.*;

import com.fathom.lib.common.exception.RestException;
import com.fathom.lib.common.model.file.FathomFileDTO;
import com.fathom.lib.common.model.file.FileUploadAttributesDTO;
import com.fathom.lib.common.model.page.CommonPageDTO;
import com.fathom.lib.common.utils.extractor.FieldExtractor;
import com.fathom.lib.filters.model.Filter;
import com.fathom.services.fileservice.dto.FathomFileWithContent;
import com.fathom.services.fileservice.dto.GetFilesRequestBody;
import com.fathom.services.fileservice.dto.TokenFileIdDto;
import com.fathom.services.fileservice.entity.FathomFile;
import com.fathom.services.fileservice.exception.MinioException;
import com.fathom.services.fileservice.mapper.FathomFileMapper;
import com.fathom.services.fileservice.repository.FathomFileRepository;
import com.fathom.services.fileservice.service.FathomFileService;
import com.fathom.services.fileservice.service.JwtService;
import com.fathom.services.fileservice.service.MinioService;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
@Qualifier("minio")
@Primary
public class MinioFathomFileServiceImpl implements FathomFileService {
  private final FathomFileMapper fathomFileMapper;
  private final MinioService minioService;
  private final FathomFileRepository fathomFileRepository;
  private final JwtService jwtService;

  @Value("${upload.common-upload.max-file-size}")
  private Long commonUploadMaxFileSize;

  @Value("${upload.direct-upload.max-file-size}")
  private Long directUploadMaxFileSize;

  @Value("${upload.direct-upload.base-url}")
  private String baseUrl;

  public FathomFileDTO addFile(
      MultipartFile file,
      String uuid,
      String description,
      String classification,
      String displayName)
      throws IOException {
    Path path = Path.of(uuid, file.getOriginalFilename());
    try {
      minioService.upload(
          path, new InputStreamResource(file.getInputStream()), file.getContentType());
    } catch (MinioException e) {
      throw new IOException(
          "The file cannot be upload on the internal storage. Please retry later", e);
    }

    FathomFile fathomFile =
        FathomFile.builder()
            .objectUUID(uuid)
            .fileId(path.toString())
            .fileName(file.getOriginalFilename())
            .classification(classification)
            .description(description)
            .date(LocalDateTime.now())
            .displayName(displayName)
            .content(extractContent(file))
            .contentType(file.getContentType())
            .fileSize(file.getSize())
            .build();

    fathomFile = fathomFileRepository.save(fathomFile);

    return FathomFileMapper.INSTANCE.entityToNewDTO(fathomFile);
  }

  @Override
  public FathomFileDTO uploadFile(MultipartFile file, FathomFileDTO fileInfoDTO)
      throws IOException {
    Long fileSize = file.getSize();

    if (commonUploadMaxFileSize < fileSize) {
      throw new RestException(
          HttpStatus.BAD_REQUEST, FILE_SIZE_LIMIT_EXCEEDED, commonUploadMaxFileSize, fileSize);
    }

    try {
      return saveFile(file, fileInfoDTO);
    } catch (MinioException ex) {
      throw new IOException("error when saving file to minio:" + ex);
    }
  }

  @Override
  public FileUploadAttributesDTO generateUploadAttributes(FathomFileDTO fileInfoDTO) {
    Long fileSize = fileInfoDTO.getFileSize();

    if (directUploadMaxFileSize < fileSize) {
      throw new RestException(
          HttpStatus.BAD_REQUEST, FILE_SIZE_LIMIT_EXCEEDED, directUploadMaxFileSize, fileSize);
    }

    return FileUploadAttributesDTO.builder()
        .token(jwtService.generateToken(fileInfoDTO))
        .url(baseUrl)
        .build();
  }

  @Override
  @SuppressWarnings("unchecked")
  public Object getFilesWithFiltering(GetFilesRequestBody requestBody) {
    Object result;

    List<Filter> filterList = requestBody.getFilters();
    List<String> fieldNames = requestBody.getFields();
    int pageNumber = requestBody.getPageNumber();
    int pageSize = requestBody.getPageSize();

    if (!requestBody.isPaged()) {
      List<FathomFileDTO> fathomFiles =
          fathomFileRepository
              .findFathomFilesWithFiltering(filterList, requestBody.getSortingOptions()).stream()
              .map(FathomFileMapper.INSTANCE::entityToNewDTO)
              .toList();

      if (!CollectionUtils.isEmpty(fieldNames)) {
        result = FieldExtractor.getFieldValuesInList(fathomFiles, fieldNames);
      } else {
        result = fathomFiles;
      }
    } else {
      Page<FathomFile> fathomFilesPage =
          fathomFileRepository.findFathomFilesWithFilteringPaged(
              filterList,
              requestBody.getSortingOptions(),
              PageRequest.of(pageNumber - 1, pageSize));

      List<FathomFileDTO> pageContent =
          fathomFilesPage.getContent().stream()
              .map(FathomFileMapper.INSTANCE::entityToNewDTO)
              .toList();

      CommonPageDTO pageDTO =
          new CommonPageDTO<>(
              pageContent,
              fathomFilesPage.getNumber() + 1,
              fathomFilesPage.getSize(),
              fathomFilesPage.getTotalPages(),
              fathomFilesPage.getTotalElements());

      // If fieldNames is specified we trying to extract specific fields from model.
      if (!CollectionUtils.isEmpty(fieldNames)) {
        pageDTO.setContent(FieldExtractor.getFieldValuesInList(pageContent, fieldNames));
      }

      result = pageDTO;
    }

    return result;
  }

  @Override
  public FathomFileDTO uploadFileDirectly(String token, MultipartFile file) throws IOException {
    FathomFileDTO fathomFileDTO = jwtService.validateAndGetBody(token);

    Long fileSize = file.getSize();

    if (directUploadMaxFileSize < fileSize) {
      throw new RestException(
          HttpStatus.BAD_REQUEST, FILE_SIZE_LIMIT_EXCEEDED, directUploadMaxFileSize, fileSize);
    }

    try {
      return saveFile(file, fathomFileDTO);
    } catch (MinioException ex) {
      log.warn("Caught exception", ex);
      throw new IOException("Exception writing to minio:", ex);
    }
  }

  public FathomFileDTO updateFileInfo(FathomFileDTO fileDTO) {
    FathomFile existingFathomFile =
        fathomFileRepository
            .findById(fileDTO.getId())
            .orElseThrow(
                () -> new RestException(HttpStatus.NOT_FOUND, FILE_NOT_EXISTS, fileDTO.getId()));

    FathomFileMapper.INSTANCE.dtoToExistingEntity(fileDTO, existingFathomFile);

    existingFathomFile = fathomFileRepository.save(existingFathomFile);

    return FathomFileMapper.INSTANCE.entityToNewDTO(existingFathomFile);
  }

  /**
   * Safely deletes all files linked to resource UUID specified.
   *
   * @param uuid external resource uuid (asset, template etc.).
   * @param notifyGeneralTopic marker which specifies should we send message to general topic or not
   * @return number of deleted files.
   */
  public int deleteByUUID(String uuid) {
    List<FathomFile> filesToDelete = fathomFileRepository.findAllByObjectUUID(uuid);

    for (FathomFile fileToDelete : filesToDelete) {
      try {
        minioService.remove(Path.of(fileToDelete.getFileId()));
        fathomFileRepository.delete(fileToDelete);
      } catch (MinioException ex) {
        log.error(FILE_DELETE_FAILURE, ex);
      }
    }

    return filesToDelete.size();
  }

  /**
   * Safely deletes link between resource UUID and file, then checks if file linked to another
   * resource and deletes it otherwise
   *
   * @param id link id
   */
  public void deleteByID(final String id) {
    Optional<FathomFile> fathomFileOptional = fathomFileRepository.findById(id);

    if (fathomFileOptional.isPresent()) {
      FathomFile fathomFile = fathomFileOptional.get();
      String fileId = fathomFile.getFileId();
      try {
        minioService.remove(Path.of(fileId));
      } catch (MinioException e) {
        throw new RestException(
            HttpStatus.INTERNAL_SERVER_ERROR, FILE_DELETE_FAILURE, e.getMessage());
      }
      fathomFileRepository.deleteById(id);
    }
  }

  public FathomFileDTO getFileByID(String id) {
    return fathomFileRepository
        .findById(id)
        .map(FathomFileMapper.INSTANCE::entityToNewDTO)
        .orElse(null);
  }

  public Page<FathomFileDTO> findFathomFilesByUUIDWithFiltering(
      Pageable page, Map<String, String> filter) {
    Page<FathomFile> fileDescriptions =
        fathomFileRepository.findFathomFilesWithFilteringPaged(page, filter);
    return fileDescriptions.map(FathomFileMapper.INSTANCE::entityToNewDTO);
  }

  public Optional<FathomFileWithContent> downloadFileByID(String id) throws IOException {
    var maybeFile = fathomFileRepository.findById(id);

    if (maybeFile.isEmpty()) {
      return Optional.empty();
    }

    var fathomFile = maybeFile.get();
    InputStream inputStream;

    try {
      inputStream = minioService.get(Path.of(fathomFile.getFileId()));
      String contentType = fathomFile.getContentType();
      if ((Objects.isNull(contentType) || contentType.isEmpty())
          && Objects.nonNull(fathomFile.getFileName())) {
        Tika tika = new Tika();
        try (BufferedInputStream bufferedStream = new BufferedInputStream(inputStream)) {
          bufferedStream.mark(Integer.MAX_VALUE); // Mark to reset later
          contentType = tika.detect(bufferedStream);
          bufferedStream.reset(); // Reset stream to beginning after detection
          inputStream = bufferedStream;
        }
        fathomFile.setContentType(contentType);
      }

      var fileWithMetadata =
          new FathomFileWithContent(
              FathomFileMapper.INSTANCE.entityToNewDTO(fathomFile), inputStream);

      return Optional.of(fileWithMetadata);

    } catch (MinioException e) {
      log.warn("Caught exception", e);
      throw new IOException("Exception reading from minio:", e);
    }
  }

  @Override
  public Optional<FathomFileWithContent> downloadByToken(String token) throws IOException {
    FathomFileDTO fathomFileDTO = jwtService.validateAndGetBody(token);
    return downloadFileByID(fathomFileDTO.getId());
  }

  @Override
  public List<TokenFileIdDto> generateDownloadToken(Set<String> id) {
    Set<FathomFile> fathomFiles = fathomFileRepository.findByIdIn(id);

    if (id.size() > fathomFiles.size()) {
      id.removeAll(fathomFiles.stream().map(FathomFile::getId).collect(Collectors.toSet()));

      throw new RestException(
          HttpStatus.NOT_FOUND,
          "Files with ids: %s does not exits ",
          id.stream().collect(Collectors.joining(", ", "[", "]")));
    }

    var fileDTOS = fathomFiles.stream().map(FathomFileMapper.INSTANCE::entityToNewDTO).toList();

    var fileToken =
        fileDTOS.stream()
            .map(
                x ->
                    FileUploadAttributesDTO.builder()
                        .token(jwtService.generateToken(x))
                        .url(baseUrl)
                        .build())
            .toList();

    List<TokenFileIdDto> result = new ArrayList<>();

    if (fileToken.isEmpty()) {
      return result;
    }

    log.info("generated {} tokens with base url {}", fileToken.size(), fileToken.get(0).getUrl());

    if (fileDTOS.size() != fileToken.size()) {
      throw new IllegalArgumentException(
          "The size of fileDTOS and fileToken lists must be the same.");
    }

    for (int i = 0; i < fileDTOS.size(); i++) {
      var fileId = fileDTOS.get(i).getId();
      var token = fileToken.get(i).getToken();

      if (fileId == null) {
        log.info("fileDTOS.get({}).getId() returned null.", i);

        throw new NullPointerException("fileDTOS.get(" + i + ").getId() returned null.");
      }
      if (token == null) {
        log.info("fileToken.get({}).getToken() returned null.", i);

        throw new NullPointerException("fileToken.get(" + i + ").getToken() returned null.");
      }

      result.add(new TokenFileIdDto(fileId, token));
    }

    return result;
  }

  @Override
  public List<FathomFileDTO> getManyByUUID(Set<String> uuidList) {
    return fathomFileRepository.findAllByObjectUUIDIn(uuidList).stream()
        .map(fathomFileMapper::entityToNewDTO)
        .toList();
  }

  private FathomFileDTO saveFile(MultipartFile file, FathomFileDTO fileInfoDTO)
      throws IOException, MinioException {
    Objects.requireNonNull(file.getOriginalFilename(), "File name cannot be null");

    Path path = Path.of(fileInfoDTO.getObjectUUID(), file.getOriginalFilename());
    minioService.upload(
        path, new InputStreamResource(file.getInputStream()), file.getContentType());

    FathomFile fathomFile = FathomFileMapper.INSTANCE.dtoToNewEntity(fileInfoDTO);
    fathomFile.setFileId(path.toString());
    fathomFile.setFileName(file.getOriginalFilename());
    fathomFile.setDate(LocalDateTime.now());
    fathomFile.setContent(extractContent(file));
    fathomFile.setContentType(file.getContentType());
    fathomFile.setFileSize(file.getSize());

    fathomFile = fathomFileRepository.save(fathomFile);

    return FathomFileMapper.INSTANCE.entityToNewDTO(fathomFile);
  }

  public static String extractContent(MultipartFile file) {
    Tika tika = new Tika();

    String content;
    try (InputStream inputStream = file.getInputStream()) {
      content = tika.parseToString(inputStream);
    } catch (Exception e) {
      throw new RestException(HttpStatus.BAD_REQUEST, CONTENT_PARSING_FAILURE, e.getMessage());
    }

    return content;
  }
}
