package com.fathom.services.fileservice.api.doc;

import com.fathom.lib.common.model.file.FathomFileDTO;
import com.fathom.services.fileservice.dto.TokenFileIdDto;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

@OpenAPIDefinition(
    info =
        @Info(
            title = "File service application",
            description =
                "This application is used to store files related to Fathom resources (assets, templates, etc.)",
            license = @License(name = "Apache 2.0", url = "https://fathoms.io"),
            contact =
                @Contact(
                    name = "Fathom Solutions",
                    email = "<EMAIL>",
                    url = "https://fathoms.io")))
public interface FathomFileControllerApi {

  @Operation(
      summary = "Get files with filtering",
      parameters = {@Parameter(name = "filter", description = "Filter for files")},
      description = "Get file list with filtering")
  Page<FathomFileDTO> getFiles(Pageable page, Map<String, String> filter);

  @Operation(
      summary = "Download file",
      description = "Downloads file in to http response input stream by it's ID.")
  ResponseEntity<StreamingResponseBody> downloadFile(String id) throws IOException;

  @Operation(summary = "Download file by token", description = "Downloads file by token.")
  ResponseEntity<StreamingResponseBody> downloadByToken(String token) throws IOException;

  @Operation(summary = "Generate download token", description = "Generate download token.")
  ResponseEntity<List<TokenFileIdDto>> generateDownloadToken(Set<String> idsSet);

  @Operation(summary = "Get file info", description = "Get files metadata by file ID")
  ResponseEntity<FathomFileDTO> getFile(String id);

  @Operation(
      summary = "Uploads file",
      description =
          "Uploads file for specified resource ID with optional description and classification.")
  ResponseEntity<FathomFileDTO> addFile(
      MultipartFile file,
      String uuid,
      String description,
      String classification,
      String displayName)
      throws IOException;

  @Operation(summary = "Update file info file", description = "Update file info.")
  ResponseEntity<FathomFileDTO> updateFileInfo(FathomFileDTO fileDTO);

  @Operation(summary = "Delete file by ID", description = "Deletes file by it's ID")
  void deleteByID(String id);

  @Operation(
      summary = "Delete files by resource ID",
      description = "Deletes all files related to specified resource ID.")
  int deleteByUUID(String uuid);

  @Operation(
      summary = "Get many file information by list of uuid. ",
      description = "Get many file information by list of uuid. ")
  ResponseEntity<List<FathomFileDTO>> getManyByUUID(Set<String> uuidList);
}
