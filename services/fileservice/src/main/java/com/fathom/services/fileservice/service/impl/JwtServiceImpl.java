package com.fathom.services.fileservice.service.impl;

import static com.fathom.services.fileservice.exception.ExMessages.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fathom.lib.common.exception.RestException;
import com.fathom.lib.common.model.file.FathomFileDTO;
import com.fathom.services.fileservice.service.JwtService;
import io.jsonwebtoken.*;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class JwtServiceImpl implements JwtService {

  @Value("${upload.direct-upload.token.expiration-hours}")
  private Integer expirationHours;

  @Value("${upload.direct-upload.token.secret}")
  private String secret;

  @Value("${upload.direct-upload.token.fileInfoClaimName}")
  private String fileInfoClaimName;

  private final ObjectMapper objectMapper;

  @Override
  public String generateToken(FathomFileDTO fileInfoDTO) {
    LocalDateTime issuedAt = LocalDateTime.now();
    LocalDateTime expiration = issuedAt.plusHours(expirationHours);

    String fileInfoDTOJson;

    try {
      fileInfoDTOJson = objectMapper.writeValueAsString(fileInfoDTO);
    } catch (JsonProcessingException e) {
      throw new RestException(
          HttpStatus.INTERNAL_SERVER_ERROR,
          OBJECT_SERIALIZATION_FAILURE,
          "FathomFileDTO",
          e.getMessage());
    }

    var key = Keys.hmacShaKeyFor(Decoders.BASE64.decode(secret));

    return Jwts.builder()
        .issuedAt(Timestamp.valueOf(issuedAt))
        .expiration(Timestamp.valueOf(expiration))
        .claim(fileInfoClaimName, fileInfoDTOJson)
        .signWith(key, Jwts.SIG.HS256)
        .compact();
  }

  @Override
  public FathomFileDTO validateAndGetBody(String token) {
    var key = Keys.hmacShaKeyFor(Decoders.BASE64.decode(secret));
    var jwtParser = Jwts.parser().verifyWith(key).build();

    Jws<Claims> claims;

    try {
      claims = jwtParser.parseSignedClaims(token);
    } catch (Exception e) {
      throw new RestException(HttpStatus.BAD_REQUEST, CLAIMS_PARSING_FAILURE, e.getMessage());
    }

    String fileInfo = claims.getPayload().get(fileInfoClaimName).toString();

    FathomFileDTO fathomFileDTO;

    try {
      fathomFileDTO = objectMapper.readValue(fileInfo, FathomFileDTO.class);
    } catch (JsonProcessingException e) {
      throw new RestException(
          HttpStatus.BAD_REQUEST, CLAIM_PARSING_FAILURE, fileInfoClaimName, e.getMessage());
    }

    return fathomFileDTO;
  }
}
