package com.fathom.services.fileservice.service;

import com.fathom.services.fileservice.exception.MinioException;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import java.io.InputStream;
import java.nio.file.Path;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

@Service
public class MinioService {
  private final MinioClient client;

  private final String bucket;

  public MinioService(MinioClient client, @Value("${minio.bucket}") String bucket) {
    this.client = client;
    this.bucket = bucket;
  }

  public void upload(Path path, Resource resource, String contentType) throws MinioException {
    try {
      client.putObject(
          PutObjectArgs.builder().bucket(bucket).object(path.toString()).contentType(contentType)
              .stream(resource.getInputStream(), -1, 5 * FileUtils.ONE_MB)
              .build());
    } catch (Exception e) {
      throw new MinioException("there was an error putting object in the bucket", e);
    }
  }

  public void remove(Path path) throws MinioException {
    try {
      client.removeObject(
          RemoveObjectArgs.builder().bucket(bucket).object(path.toString()).build());
    } catch (Exception e) {
      throw new MinioException("there was an error removing object in the bucket", e);
    }
  }

  public InputStream get(Path path) throws MinioException {
    try {
      return client.getObject(
          GetObjectArgs.builder().bucket(bucket).object(path.toString()).build());
    } catch (Exception e) {
      throw new MinioException("there was an error getting the object from bucket", e);
    }
  }
}
