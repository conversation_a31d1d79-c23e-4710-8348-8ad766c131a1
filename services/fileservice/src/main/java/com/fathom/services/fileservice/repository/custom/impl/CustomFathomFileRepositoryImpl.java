package com.fathom.services.fileservice.repository.custom.impl;

import static com.fathom.lib.filters.filtering.FilterUtils.filterMapToList;

import com.fathom.lib.filters.model.Filter;
import com.fathom.lib.filters.model.SortOption;
import com.fathom.services.fileservice.entity.FathomFile;
import com.fathom.services.fileservice.repository.custom.CustomFathomFileRepository;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;

public class CustomFathomFileRepositoryImpl extends CustomAbstractRepository<FathomFile>
    implements CustomFathomFileRepository {

  @Override
  public Page<FathomFile> findFathomFilesWithFilteringPaged(
      Pageable page, Map<String, String> filter) {
    Query query = new Query().with(page);

    addFiltersToQuery(query, filterMapToList(filter));

    List<FathomFile> listWithFiltering = mongoTemplate.find(query, FathomFile.class);

    return getPage(listWithFiltering, page);
  }

  @Override
  public Page<FathomFile> findFathomFilesWithFilteringPaged(
      List<Filter> filterList, List<SortOption> sortOptions, Pageable pageable) {
    return getPage(getFathomFilesFilteredAndSorted(filterList, sortOptions), pageable);
  }

  @Override
  public List<FathomFile> findFathomFilesWithFiltering(
      List<Filter> filterList, List<SortOption> sortOptions) {
    return getFathomFilesFilteredAndSorted(filterList, sortOptions);
  }

  private List<FathomFile> getFathomFilesFilteredAndSorted(
      List<Filter> filterList, List<SortOption> sortOptions) {
    Query query = new Query();

    // Adding criteria for filtering
    if (CollectionUtils.isEmpty(sortOptions)) {
      addFiltersToQuery(query, filterList);
    } else {
      addFiltersAndSortingToQuery(query, filterList, sortOptions);
    }

    return mongoTemplate.find(query, FathomFile.class);
  }
}
