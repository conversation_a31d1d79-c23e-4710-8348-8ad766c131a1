package com.fathom.services.fileservice.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.filters.model.Filterable;
import jakarta.validation.constraints.Min;
import java.io.Serial;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetFilesRequestBody extends Filterable {

  @Serial private static final long serialVersionUID = 7626914219260967454L;

  private boolean paged = false;

  private List<String> fields = new ArrayList<>();

  @Min(1)
  private int pageNumber = 1;

  @Min(1)
  private int pageSize = 5;
}
