package com.fathom.services.fileservice.service;

import com.fathom.lib.common.model.file.FathomFileDTO;
import com.fathom.lib.common.model.file.FileUploadAttributesDTO;
import com.fathom.services.fileservice.dto.FathomFileWithContent;
import com.fathom.services.fileservice.dto.GetFilesRequestBody;
import com.fathom.services.fileservice.dto.TokenFileIdDto;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

public interface FathomFileService {

  FathomFileDTO addFile(
      MultipartFile file,
      String uuid,
      String description,
      String classification,
      String displayName)
      throws IOException;

  FathomFileDTO uploadFile(MultipartFile file, FathomFileDTO fileInfoDTO) throws IOException;

  FileUploadAttributesDTO generateUploadAttributes(FathomFileDTO fileInfoDTO);

  Object getFilesWithFiltering(GetFilesRequestBody requestBody);

  FathomFileDTO uploadFileDirectly(String token, MultipartFile file) throws IOException;

  FathomFileDTO updateFileInfo(FathomFileDTO fileDTO);

  int deleteByUUID(String uuid);

  void deleteByID(final String id);

  FathomFileDTO getFileByID(String id);

  Page<FathomFileDTO> findFathomFilesByUUIDWithFiltering(Pageable page, Map<String, String> filter);

  Optional<FathomFileWithContent> downloadFileByID(String id) throws IOException;

  Optional<FathomFileWithContent> downloadByToken(String token) throws IOException;

  List<TokenFileIdDto> generateDownloadToken(Set<String> id);

  List<FathomFileDTO> getManyByUUID(Set<String> uuidList);
}
