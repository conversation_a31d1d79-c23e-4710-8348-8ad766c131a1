package com.fathom.services.fileservice.repository.custom;

import com.fathom.lib.filters.model.Filter;
import com.fathom.lib.filters.model.SortOption;
import com.fathom.services.fileservice.entity.FathomFile;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface CustomFathomFileRepository extends CustomBaseRepository<FathomFile> {

  Page<FathomFile> findFathomFilesWithFilteringPaged(Pageable page, Map<String, String> filter);

  Page<FathomFile> findFathomFilesWithFilteringPaged(
      List<Filter> filterList, List<SortOption> sortOptions, Pageable pageable);

  List<FathomFile> findFathomFilesWithFiltering(
      List<Filter> filterList, List<SortOption> sortOptions);
}
