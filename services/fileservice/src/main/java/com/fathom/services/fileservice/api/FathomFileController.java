package com.fathom.services.fileservice.api;

import com.fathom.lib.common.model.file.FathomFileDTO;
import com.fathom.lib.common.model.file.FileUploadAttributesDTO;
import com.fathom.lib.common.model.validation.group.Create;
import com.fathom.services.fileservice.api.doc.FathomFileControllerApi;
import com.fathom.services.fileservice.dto.GetFilesRequestBody;
import com.fathom.services.fileservice.dto.TokenFileIdDto;
import com.fathom.services.fileservice.service.FathomFileService;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

@Slf4j
@RestController
@RequiredArgsConstructor
public class FathomFileController implements FathomFileControllerApi {

  public static final String TOKEN_HEADER_NAME = "x-token";

  private final FathomFileService fathomFileService;

  /**
   * Saves a new file for specified UUID of external entity. In case of success returns status code
   * 201 Created.
   *
   * @param file File for uploading {@link org.springframework.web.multipart.MultipartFile}
   * @param uuid uuid of related resource (asset, template etc.). Required.
   * @param description file description string. Optional.
   * @param classification file classification string. Optional.
   * @param displayName file display name string. Optional.
   * @return Json representation of created file {@link
   *     com.fathom.lib.common.model.file.FathomFileDTO}
   */
  @Override
  @PostMapping("files")
  public ResponseEntity<FathomFileDTO> addFile(
      @RequestPart MultipartFile file,
      @RequestParam String uuid,
      @RequestParam(required = false) String description,
      @RequestParam(required = false) String classification,
      @RequestParam(required = false) String displayName)
      throws IOException {
    FathomFileDTO fathomFileDTO =
        fathomFileService.addFile(file, uuid, description, classification, displayName);

    return new ResponseEntity<>(fathomFileDTO, HttpStatus.CREATED);
  }

  @PostMapping(path = "files/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResponseEntity<FathomFileDTO> uploadFile(
      @RequestPart MultipartFile file,
      @Validated(value = Create.class) @RequestPart FathomFileDTO fileInfoDTO)
      throws IOException {
    return new ResponseEntity<>(
        fathomFileService.uploadFile(file, fileInfoDTO), HttpStatus.CREATED);
  }

  @PostMapping(path = "files/upload/request")
  public ResponseEntity<FileUploadAttributesDTO> generateUploadAttributes(
      @Validated(value = Create.class) @RequestPart @RequestBody FathomFileDTO fileInfoDTO) {
    return ResponseEntity.ok(fathomFileService.generateUploadAttributes(fileInfoDTO));
  }

  @PostMapping(path = "files/upload/direct", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResponseEntity<FathomFileDTO> uploadFileDirectly(
      @RequestHeader(TOKEN_HEADER_NAME) String token, @RequestPart MultipartFile file)
      throws IOException {
    return new ResponseEntity<>(
        fathomFileService.uploadFileDirectly(token, file), HttpStatus.CREATED);
  }

  @PostMapping(path = "/files/filtered")
  public Object getFilesFiltered(@Valid @RequestBody GetFilesRequestBody requestBody) {
    return fathomFileService.getFilesWithFiltering(requestBody);
  }

  /**
   * Method for update of existing file info record. In case of success returns status code 200 OK.
   *
   * @param fileInfoDTO File info dto {@link com.fathom.lib.common.model.file.FathomFileDTO} for
   *     update.
   * @return Json representation of updated file {@link
   *     com.fathom.lib.common.model.file.FathomFileDTO}
   */
  @Override
  @PutMapping("files")
  public ResponseEntity<FathomFileDTO> updateFileInfo(@RequestBody FathomFileDTO fileInfoDTO) {
    return ResponseEntity.ok(fathomFileService.updateFileInfo(fileInfoDTO));
  }

  /**
   * Safely deletes file by ID. Will affect all uuid tied to the file. Method for update of existing
   * file info record. In case of success returns status code 204 NoContent.
   *
   * @param id file ID
   */
  @Override
  @DeleteMapping("file/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteByID(@PathVariable String id) {
    fathomFileService.deleteByID(id);
  }

  /**
   * Safely deletes all files tied to specified UUID of external entity (asset, template etc.). Use
   * when resource removed from system.
   *
   * @param uuid uuid of external entity
   * @return number of deleted files
   */
  @Override
  @DeleteMapping("files/{uuid}")
  public int deleteByUUID(@PathVariable String uuid) {
    return fathomFileService.deleteByUUID(uuid);
  }

  @Override
  @PostMapping("files/byUUID")
  public ResponseEntity<List<FathomFileDTO>> getManyByUUID(@RequestBody Set<String> uuidList) {
    return ResponseEntity.ok(fathomFileService.getManyByUUID(uuidList));
  }

  /**
   * Gets the file info for specified file ID. In case of success returns status code 200 OK. If not
   * exist returns status 404 NotFound.
   *
   * @param id file id
   * @return Existing fathom file info representation {@link
   *     com.fathom.lib.common.model.file.FathomFileDTO}
   */
  @Override
  @GetMapping("file/{id}/info")
  public ResponseEntity<FathomFileDTO> getFile(@PathVariable String id) {
    FathomFileDTO fathomFileDTO = fathomFileService.getFileByID(id);

    return Objects.nonNull(fathomFileDTO)
        ? new ResponseEntity<>(fathomFileDTO, HttpStatus.OK)
        : new ResponseEntity<>(HttpStatus.NOT_FOUND);
  }

  /**
   * Return list of files with old filtering approach.
   *
   * @param page page configuration
   * @param filter map with values for filtering
   * @return returns {@link org.springframework.data.domain.Page} object with {@link
   *     com.fathom.lib.common.model.file.FathomFileDTO} objects
   */
  @Override
  @GetMapping("files")
  public Page<FathomFileDTO> getFiles(
      @PageableDefault(size = 50) Pageable page,
      @RequestParam(required = false) Map<String, String> filter) {
    return fathomFileService.findFathomFilesByUUIDWithFiltering(page, filter);
  }

  /**
   * @param id of the file - as in hibernate entity id
   * @return returns an array of bytes that will be rendered as attachement. Returns 404 if the
   *     entity is not found by id
   * @throws IOException there can be problems with minio, or buffer copies. The exception should be
   *     mapped to 500 internal server error
   */
  @Override
  @GetMapping("file/{id}")
  public @ResponseBody ResponseEntity<StreamingResponseBody> downloadFile(@PathVariable String id)
      throws IOException {
    var maybeFile = fathomFileService.downloadFileByID(id);

    if (maybeFile.isEmpty()) {
      return ResponseEntity.notFound().build();
    }
    var file = maybeFile.get();

    StreamingResponseBody responseBody =
        outputStream -> {
          try (var inputStream = file.data()) {
            inputStream.transferTo(outputStream);
          }
        };

    return ResponseEntity.ok()
        .header(
            HttpHeaders.CONTENT_DISPOSITION,
            "attachment; filename=" + file.metadata().getFileName())
        .header(HttpHeaders.CONTENT_TYPE, file.metadata().getContentType())
        .body(responseBody);
  }

  @Override
  @GetMapping("file/download/{token}")
  public ResponseEntity<StreamingResponseBody> downloadByToken(@PathVariable String token)
      throws IOException {
    var maybeFile = fathomFileService.downloadByToken(token);

    if (maybeFile.isEmpty()) {
      return ResponseEntity.notFound().build();
    }
    var file = maybeFile.get();

    StreamingResponseBody responseBody =
        outputStream -> {
          try (var inputStream = file.data()) {
            inputStream.transferTo(outputStream);
          }
        };

    return ResponseEntity.ok()
        .header(
            HttpHeaders.CONTENT_DISPOSITION,
            "attachment; filename=" + file.metadata().getFileName())
        .header(HttpHeaders.CONTENT_TYPE, file.metadata().getContentType())
        .body(responseBody);
  }

  @Override
  @PostMapping("file/generate-download-token")
  public ResponseEntity<List<TokenFileIdDto>> generateDownloadToken(
      @RequestBody Set<String> idsSet) {
    return ResponseEntity.ok(fathomFileService.generateDownloadToken(idsSet));
  }
}
