package com.fathom.services.fileservice.configuration;

import java.util.ArrayList;
import java.util.List;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.ResourceHttpMessageConverter;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebHttpConfiguration implements WebMvcConfigurer {

  @Bean
  public StandardServletMultipartResolver multipartResolver() {
    return new StandardServletMultipartResolver();
  }

  @Override
  public void addCorsMappings(CorsRegistry registry) {
    registry.addMapping("/**").allowedOrigins("*").allowedMethods("*");
  }

  @Override
  public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
    converters.add(resourceHttpMessageConverter());
  }

  private ResourceHttpMessageConverter resourceHttpMessageConverter() {
    ResourceHttpMessageConverter converter = new ResourceHttpMessageConverter();
    converter.setSupportedMediaTypes(getSupportedMediaTypes());
    return converter;
  }

  private List<MediaType> getSupportedMediaTypes() {
    List<MediaType> list = new ArrayList<>();
    list.add(MediaType.ALL);
    return list;
  }
}
