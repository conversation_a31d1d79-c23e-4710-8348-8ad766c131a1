package com.fathom.services.fileservice.mapper;

import com.fathom.lib.common.model.file.FathomFileDTO;
import com.fathom.services.fileservice.entity.FathomFile;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class FathomFileMapper {

  public static final FathomFileMapper INSTANCE = Mappers.getMapper(FathomFileMapper.class);

  public abstract FathomFileDTO entityToNewDTO(FathomFile fathomFile);

  @Mappings({
    @Mapping(target = "id", ignore = true),
    @Mapping(target = "fileId", ignore = true),
    @Mapping(target = "date", ignore = true),
    @Mapping(target = "fileName", ignore = true),
    @Mapping(target = "contentType", ignore = true)
  })
  public abstract FathomFile dtoToNewEntity(FathomFileDTO fathomFileDTO);

  @Mappings({
    @Mapping(target = "id", ignore = true),
    @Mapping(target = "fileId", ignore = true),
    @Mapping(target = "date", ignore = true),
    @Mapping(target = "fileName", ignore = true),
    @Mapping(target = "contentType", ignore = true)
  })
  public abstract void dtoToExistingEntity(
      FathomFileDTO fathomFileDTO, @MappingTarget FathomFile fathomFile);
}
