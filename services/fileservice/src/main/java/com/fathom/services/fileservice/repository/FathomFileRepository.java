package com.fathom.services.fileservice.repository;

import com.fathom.services.fileservice.entity.FathomFile;
import com.fathom.services.fileservice.repository.custom.CustomFathomFileRepository;
import java.util.List;
import java.util.Set;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FathomFileRepository
    extends MongoRepository<FathomFile, String>, CustomFathomFileRepository {
  List<FathomFile> findByFileId(String fileId);

  List<FathomFile> findAllByObjectUUID(String uuid);

  List<FathomFile> findAllByObjectUUIDIn(Set<String> uuid);

  Set<FathomFile> findByIdIn(Set<String> ids);

  void deleteById(String id);
}
