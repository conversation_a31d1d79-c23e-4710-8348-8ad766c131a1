package com.fathom.services.fileservice.entity;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.annotation.Id;

@Data
@Builder
@AllArgsConstructor
public class FathomFile {

  @Id private String id;
  private String fileId;
  private LocalDateTime date;
  private String fileName;
  private String displayName;
  private String description;
  private String objectUUID;
  private String classification;
  private String content;
  private String contentType;
  private Boolean sendToGeneralKafkaTopic = false;
  private Boolean sendToKafkaTopic = false;
  private Long fileSize;
}
