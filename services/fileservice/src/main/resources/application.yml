server:
  port: ${FILESERVICE_PORT:8015}

spring:
  servlet:
    multipart:
      max-file-size: 2048MB
      max-request-size: 2048MB
  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017}
      database: ${MONGODB_DATABASE:dev}
      auto-index-creation: false

minio:
  url: ${AWS_ENDPOINT_URL:http://minio:9010}
  bucket: ${AWS_S3_BUCKET:fileservice}
  access-key: ${AWS_ACCESS_KEY_ID}
  secret-key: ${AWS_SECRET_ACCESS_KEY}

logging:
  level:
    com.fathom: DEBUG
  pattern:
    # Logging pattern containing traceId and spanId; no longer provided through Sleuth by default
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"

management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: ALWAYS

upload:
  common-upload:
    max-file-size: ${COMMON_UPLOAD_MAX_FILE_SIZE:10485760}
  direct-upload:
    max-file-size: ${DIRECT_UPLOAD_MAX_FILE_SIZE:**********}
    base-url: ${BASE_URL:https://neo-files.fathom-solutions.com}
    token:
      expiration-hours: ${JWT_EXPIRATION_HOURS:6}
      secret: ${JWT_SECRET:Yn2kjibddFAWtnPJ2AFlL8WXmohJMCvigQggaEypa5E=}
      fileInfoClaimName: ${JWT_FILE_INFO_CLAIM_NAME:fileInfo}

springdoc:
  api-docs:
    version: "openapi_3_0"

diagnostics:
  service-name: "fileservice"
  version: ${VERSION:0.0.1-dev}
  build-timestamp: ${BUILD_TIMESTAMP:1970-01-01T00:00:00Z}
  git-hash: ${GIT_HASH:unknown}
  git-branch: ${GIT_BRANCH:main}
  git-commit-timestamp: ${GIT_COMMIT_TIMESTAMP:1970-01-01T00:00:00Z}
