package com.fathom.services.fileservice;

import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.MinIOContainer;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.kafka.ConfluentKafkaContainer;
import org.testcontainers.utility.DockerImageName;

@SpringBootTest
@Testcontainers
public abstract class IntegrationTestBase {
  @Autowired protected MinioClient minioclient;

  @Container
  static MinIOContainer minio =
      new MinIOContainer("minio/minio:RELEASE.2023-09-04T19-57-37Z").withExposedPorts(9000);

  @Container static MongoDBContainer mongo = new MongoDBContainer("mongo:4.0.10");

  @Container
  static ConfluentKafkaContainer kafka =
      new ConfluentKafkaContainer(DockerImageName.parse("confluentinc/cp-kafka:7.5.2"));

  @BeforeEach
  void setUp() throws Exception {
    var doesBucketExist =
        minioclient.bucketExists(BucketExistsArgs.builder().bucket("fileservice").build());
    if (!doesBucketExist) {
      minioclient.makeBucket(MakeBucketArgs.builder().bucket("fileservice").build());
    }
  }

  @DynamicPropertySource
  static void configureProperties(DynamicPropertyRegistry registry) {
    registry.add("minio.url", minio::getS3URL);
    registry.add("minio.access-key", minio::getUserName);
    registry.add("minio.secret-key", minio::getPassword);

    registry.add("spring.data.mongodb.uri", mongo::getConnectionString);

    registry.add("spring.kafka.bootstrap-servers", kafka::getBootstrapServers);
  }
}
