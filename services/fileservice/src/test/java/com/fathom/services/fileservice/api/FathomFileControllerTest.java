package com.fathom.services.fileservice.api;

import static io.restassured.RestAssured.given;
import static io.restassured.RestAssured.when;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.*;

import com.fathom.services.fileservice.IntegrationTestBase;
import io.restassured.RestAssured;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class FathomFileControllerTest extends IntegrationTestBase {
  @LocalServerPort int randomServerPort;

  @BeforeEach
  void setUp() {
    RestAssured.port = randomServerPort;
  }

  @Test
  void happy_path_add_file_list_files_receive_file_and_delete_file() {
    var classLoader = getClass().getClassLoader();
    var file = new File(Objects.requireNonNull(classLoader.getResource("test_file")).getFile());

    // POST new file
    var fileId =
        given()
            .multiPart(file)
            .formParam("uuid", "c0c0c0c0c0c0")
            .formParam("description", "a test file")
            .formParam("displayName", "test_3")
            .when()
            .post("files")
            .then()
            .statusCode(201)
            .extract()
            .path("fileId");

    // GET list endpoint - check if posted file is there
    var listResult = given().get("files").then().statusCode(200).assertThat().extract().response();

    var listFileId = listResult.path("content[0].fileId");
    var entityId = listResult.path("content[0].id");

    assertThat(fileId).isEqualTo(listFileId);

    // GET file by id and check contents
    var gotBytes =
        when()
            .get("file/" + entityId)
            .then()
            .statusCode(200)
            .header("Content-Type", containsString("application/octet-stream")) //
            .extract()
            .asByteArray();

    var text = new String(gotBytes, StandardCharsets.UTF_8);

    assertThat(text).containsSequence("Like a diamond in the sky");

    // DELETE file by id
    when().delete("file/" + entityId).then().statusCode(204);

    // GET by id should return 404 now
    when().get("file/" + entityId).then().statusCode(404);
  }

  @Test
  void token_generation_allows_to_download_a_file_via_jwt_token() {
    var classLoader = getClass().getClassLoader();
    var file = new File(Objects.requireNonNull(classLoader.getResource("test_file")).getFile());

    // POST new file
    var fileId =
        given()
            .multiPart(file)
            .formParam("uuid", "c0c0c0c0c0c0")
            .formParam("description", "a test file")
            .formParam("displayName", "test_5")
            .when()
            .post("files")
            .then()
            .statusCode(201)
            .extract()
            .path("id");

    // GET token for file
    var token =
        given()
            .contentType("application/json")
            .body("[\"" + fileId + "\"]")
            .when()
            .post("file/generate-download-token")
            .then()
            .statusCode(200)
            .extract()
            .path("[0].token");

    // GET file by token and check contents
    var gotBytes =
        given()
            .get("file/download/" + token)
            .then()
            .statusCode(200)
            .header("Content-Type", containsString("application/octet-stream")) //
            .extract()
            .asByteArray();

    var text = new String(gotBytes, StandardCharsets.UTF_8);

    assertThat(text).containsSequence("Like a diamond in the sky");
  }
}
