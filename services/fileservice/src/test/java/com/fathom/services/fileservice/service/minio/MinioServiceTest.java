package com.fathom.services.fileservice.service.minio;

import static org.assertj.core.api.Assertions.*;

import com.fathom.services.fileservice.IntegrationTestBase;
import com.fathom.services.fileservice.service.MinioService;
import io.minio.GetObjectArgs;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;

class MinioServiceTest extends IntegrationTestBase {
  @Autowired MinioService service;

  @Test
  void when_file_is_uploaded_does_not_fail_and_has_correct_contents() throws Exception {
    // given
    try (var inputStream = getClass().getClassLoader().getResourceAsStream("test_file")) {
      assertThat(inputStream).isNotNull();
      // when
      service.upload(Path.of("test_1"), new InputStreamResource(inputStream), "text/plain");
    }

    // then
    var got =
        minioclient.getObject(
            GetObjectArgs.builder().bucket("fileservice").object("test_1").build());
    var text = new String(got.readAllBytes(), StandardCharsets.UTF_8);

    assertThat(got).isNotNull();
    assertThat(text).containsSequence("Like a diamond in the sky");
  }

  @Test
  void when_file_is_deleted_it_is_no_longer_present() throws Exception {
    // given
    try (var inputStream = getClass().getClassLoader().getResourceAsStream("test_file")) {
      assertThat(inputStream).isNotNull();
      service.upload(Path.of("test_2"), new InputStreamResource(inputStream), "text/plain");
    }
    // when
    service.remove(Path.of("test_2"));

    // then
    assertThatThrownBy(
            () -> {
              minioclient.getObject(
                  GetObjectArgs.builder().bucket("fileservice").object("test_2").build());
            })
        .hasMessageContaining("The specified key does not exist");
  }
}
