## Running the service locally

You need docker installation on your machine.
* In the root directory: `docker compse up`
* In the root directory `./mvnw spring-boot:run -Drun.jvmArguments="-Dspring.profiles.active=local"`
You can achieve the same and the debugger by modifying JVM options in intelliJ

## Running the tests

You need docker installation on your machine.
You can run maven `test` task and all tests are going to be executed
The tests will spin up docker containers that are ephemeral (all data is lost after tests are executed) under `default` profile

