# -*- MakeFile -*-
$(eval VERSION := $(shell mvn org.apache.maven.plugins:maven-help-plugin:evaluate -Dexpression=project.version -q -DforceStdout))
$(eval ARTIFACT_ID := $(shell mvn org.apache.maven.plugins:maven-help-plugin:evaluate -Dexpression=project.artifactId -q -DforceStdout))
$(eval JAR_NAME := $(ARTIFACT_ID)-$(VERSION).jar)
export FILESERVICE_TAG := $(shell ./mvnw -q -Dexec.executable="echo" -Dexec.args='$${project.version}' --non-recursive exec:exec)

.PHONY: info
info:
	wget https://fathomsjenkinsdeployment.s3.eu-central-1.amazonaws.com/scripts/Java/buildBanner.sh
	wget https://fathomsjenkinsdeployment.s3.eu-central-1.amazonaws.com/scripts/Java/getAppProp.sh
	wget https://fathomsjenkinsdeployment.s3.eu-central-1.amazonaws.com/scripts/Java/getLastCommit.sh
	wget https://fathomsjenkinsdeployment.s3.eu-central-1.amazonaws.com/scripts/Java/getVersion.sh
	chmod +x buildBanner.sh getAppProp.sh getLastCommit.sh getVersion.sh
	./buildBanner.sh

.PHONY: clean
clean: mvnw mvnw.cmd info
	mvn clean   ``

.PHONY: compile
compile: mvnw mvnw.cmd
	mvn compile

.PHONY: package
package: compile
	mvn package -Dmaven.test.skip=true

.PHONY: repackage
repackage: clean package

.PHONY: build-artifact
build-artifact:
	echo "Build fileservice service jar"
	./mvnw clean install

.PHONY: build-image
build-image:
	echo "Build fileservice image"
	skaffold build -f ./ops/skaffold.yaml --file-output=./target/images.json

.PHONY: deploy-service
deploy-service:
	echo "Deploy fileservice service"
	skaffold deploy -f ./ops/skaffold.yaml --build-artifacts=./target/images.json

.PHONY: run-service
run-service:
	echo "Run fileservice service"
	skaffold run -f ./ops/skaffold.yaml

.PHONY: debug-service
debug-service:
	echo "Debug fileservice service"
	skaffold debug -f ./ops/skaffold.yaml

.PHONY: delete-service
delete-service:
	echo "Delete fileservice service"
	skaffold delete -f ./ops/skaffold.yaml

.PHONY: deploy-dependencies
deploy-dependencies:
	echo "Deploy fileservice dependencies"
	skaffold deploy -f ./ops/skaffold-dependencies.yaml

.PHONY: deploy-service-with-dependencies
deploy-service-with-dependencies: deploy-dependencies deploy-service

.PHONY: run-dependencies
run-dependencies:
	echo "Run fileservice dependencies"
	skaffold run -f ./ops/skaffold-dependencies.yaml

.PHONY: run-service-with-dependencies
run-service-with-dependencies: run-dependencies run-service

.PHONY: debug-service-with-dependencies
debug-service-with-dependencies: deploy-dependencies debug-service

.PHONY: delete-dependencies
delete-dependencies:
	echo "Delete fileservice dependencies"
	skaffold delete -f ./ops/skaffold-dependencies.yaml

.PHONY: delete-service-with-dependencies
delete-service-with-dependencies: delete-service delete-dependencies

.PHONY: docker
docker: 
	docker build -f ./ops/Dockerfile -t b4ft1bt3.gra7.container-registry.ovh.net/library/$(ARTIFACT_ID):latest --build-arg JAR_PATH="target/$(JAR_NAME)" .

.PHONY: docker-latest
docker-latest: 
	docker tag b4ft1bt3.gra7.container-registry.ovh.net/library/$(ARTIFACT_ID):latest b4ft1bt3.gra7.container-registry.ovh.net/library/$(ARTIFACT_ID):latest

.PHONY: docker-nexus
docker-nexus: 
	docker push b4ft1bt3.gra7.container-registry.ovh.net/library/$(ARTIFACT_ID):latest