services:
  minio:
    image: docker.io/bitnami/minio:2022
    ports:
      - '9000:9000'
      - '9001:9001'
    volumes:
      - 'minio_data:/data'
    environment:
      - MINIO_ROOT_USER=minio
      - MINIO_ROOT_PASSWORD=minio12345
      - MINIO_DEFAULT_BUCKETS=fileservice

  mongo:
    image: mongo
    restart: always
    ports:
      - '27017:27017'

  mongo-express:
    image: mongo-express
    restart: always
    ports:
      - '8081:8081'
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: root
      ME_CONFIG_MONGODB_ADMINPASSWORD: example
      ME_CONFIG_MONGODB_URL: mongodb://mongo:27017/
      ME_CONFIG_BASICAUTH: false

volumes:
  minio_data:
    driver: local