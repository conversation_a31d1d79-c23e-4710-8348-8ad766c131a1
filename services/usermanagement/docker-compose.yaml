version: "3.4"
services:
  postgres:
    image: postgres:12
    ports:
      - 5432:5432
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - ./init-fathom-db.sql:/docker-entrypoint-initdb.d/init-fathom-db.sql

  minio:
    image: 'bitnami/minio:latest'
    environment:
      - MINIO_ROOT_USER=minio
      - MINIO_ROOT_PASSWORD=minio123
    ports:
      - '9000:9000'
      - '9001:9001'
    volumes:
      - data3-1:/data1
      - data3-2:/data2

  userprofile:
    image: ghcr.io/fathom-io/library/userprofile:latest
    ports:
      - 8089:8089
    environment:
      - MONGO_HOST=mongodb
      - MONGO_PORT=27017
      - MONGO_DB=dev
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=fathom
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - KAF<PERSON>_BROKER_LIST="kafka:9092"

  fileservice:
    image: ghcr.io/fathom-io/library/fileservice:latest
    ports:
      - 8015:8015
    environment:
      - MONGO_HOST=mongodb
      - MONGO_PORT=27017
      - MONGODB_DATABASE=dev
      - KAFKA_BROKER_LIST=kafka:9092
      - AWS_ENDPOINT_URL=http://minio:9000
      - MINIO_LOGIN=minio
      - MINIO_PASSWORD=minio123


volumes:
  data3-1:
  data3-2:
  redis-data:
    driver: local