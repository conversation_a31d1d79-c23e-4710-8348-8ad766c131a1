package com.fathom.services.usermanagement.controllers;

import static io.restassured.RestAssured.given;
import static org.assertj.core.api.Assertions.assertThat;

import com.fathom.services.usermanagement.IntegrationTestBase;
import com.fathom.services.usermanagement.model.dto.AddUserDTO;
import com.fathom.services.usermanagement.model.dto.SetupPasswordDTO;
import com.fathom.services.usermanagement.model.dto.UserDto;
import com.fathom.services.usermanagement.repositories.RoleRepository;
import io.restassured.RestAssured;
import jakarta.ws.rs.core.MediaType;
import java.util.*;
import java.util.stream.IntStream;
import org.apache.commons.lang.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class KeycloakRegistrationTest extends IntegrationTestBase {

  @Autowired private RoleRepository roleRepository;

  @BeforeEach
  void setUp() {
    RestAssured.port = port;
  }

  @Test
  void userRegistrationDelegatesCreationToKeycloakAndCompletesSuccessfully() {
    // given: random user data
    var firstName = RandomStringUtils.random(5, true, false);
    var lastName = RandomStringUtils.random(5, true, false);
    var email = String.format("%<EMAIL>", firstName, lastName);

    // when: user is created
    var addUser = AddUserDTO.builder().firstName(firstName).lastName(lastName).email(email).build();

    var user =
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(addUser)
            .when()
            .post("/users")
            .then()
            .log()
            .ifError()
            .statusCode(200)
            .extract()
            .as(UserDto.class);

    // then: response from usermanagement API
    assertThat(user.getId()).isNotNull();
    assertThat(user.getFirstName()).isEqualTo(firstName);
    assertThat(user.getLastName()).isEqualTo(lastName);
    assertThat(user.getFullName()).isEqualTo(firstName + " " + lastName);
    assertThat(user.getEmail()).isEqualToIgnoringCase(email);

    // and then: user is present in Keycloak
    var kcAdmin = keycloak.getKeycloakAdminClient();
    var kcUser =
        kcAdmin
            .realms()
            .realm("FathomDevRealm")
            .users()
            .get(user.getId().toString())
            .toRepresentation();

    assertThat(kcUser).isNotNull();
    assertThat(kcUser.getFirstName()).isEqualTo(firstName);
    assertThat(kcUser.getLastName()).isEqualTo(lastName);
    assertThat(kcUser.getEmail()).isEqualToIgnoringCase(email);
  }

  @Test
  void addUserWithEmptyFirstNameReturnsBadRequest() {
    // given: a user with empty first name
    var lastName = RandomStringUtils.random(5, true, false);
    var email = lastName + "@fathom.io";
    var addUser = AddUserDTO.builder().firstName("").lastName(lastName).email(email).build();

    // when we send the request and then response is bad request
    given()
        .contentType(MediaType.APPLICATION_JSON)
        .body(addUser)
        .when()
        .post("/users")
        .then()
        .log()
        .ifError()
        .statusCode(400);

    // and then no keycloak user is created with this email
    var kcAdmin = keycloak.getKeycloakAdminClient();
    var kcUser = kcAdmin.realms().realm("FathomDevRealm").users().search(email);
    assertThat(kcUser).isEmpty();
  }

  @Test
  void sendingPasswordResetWithEmptyPasswordReturnsBadRequest() {
    given()
        .contentType(MediaType.APPLICATION_JSON)
        .body(Map.of("passworddddddddd", "okoń"))
        .when()
        .post("/users/<EMAIL>/setup-password")
        .then()
        .log()
        .ifError()
        .statusCode(400);
  }

  @Test
  void userCanResetTheirPasswordViaCallingUserControllerEndpoint() {
    // given: a user exists in keycloak
    var firstName = RandomStringUtils.random(5, true, false);
    var lastName = RandomStringUtils.random(5, true, false);
    var email = String.format("%<EMAIL>", firstName, lastName);

    var addUser = AddUserDTO.builder().firstName(firstName).lastName(lastName).email(email).build();

    var addedUser =
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(addUser)
            .when()
            .post("/users")
            .then()
            .log()
            .ifError()
            .statusCode(200)
            .extract()
            .as(UserDto.class);

    // when endpoint for reset password is called
    var password = SetupPasswordDTO.builder().password("ExtremePasswords999**").build();
    given()
        .contentType(MediaType.APPLICATION_JSON)
        .body(password)
        .when()
        .post("/users/" + email + "/setup-password")
        .then()
        .log()
        .ifError()
        .statusCode(204);

    // then: user has credential setup
    var kcAdmin = keycloak.getKeycloakAdminClient();
    var creds =
        kcAdmin
            .realms()
            .realm("FathomDevRealm")
            .users()
            .get(addedUser.getId().toString())
            .credentials();
    assertThat(creds).hasSize(1);
  }

  @Test
  void twoPeopleWithVerySimilarEmailsDontGenerateProblemWithKCSearchAsItIsNotExact() {
    // given: a user exists in keycloak
    var random = new Random();
    var number = 9 + random.nextInt(90);
    var firstName1 = "abelard" + number;
    var lastName1 = "giza";
    var email1 = String.format("%<EMAIL>", firstName1, lastName1);

    var addUser1 =
        AddUserDTO.builder().firstName(firstName1).lastName(lastName1).email(email1).build();

    var user1 =
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(addUser1)
            .when()
            .post("/users")
            .then()
            .log()
            .ifError()
            .statusCode(200)
            .extract()
            .as(UserDto.class);

    // and another very similar user
    var firstName2 = "abelard" + (number + 1);
    var lastName2 = "giza";
    var email2 = String.format("%<EMAIL>", firstName2, lastName2);

    var addUser2 =
        AddUserDTO.builder().firstName(firstName2).lastName(lastName2).email(email2).build();

    var user2 =
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(addUser2)
            .when()
            .post("/users")
            .then()
            .log()
            .ifError()
            .statusCode(200)
            .extract()
            .as(UserDto.class);

    // when: reseting password for partial match it should not succeed
    var password = SetupPasswordDTO.builder().password("ExtremePasswords999**").build();
    given()
        .contentType(MediaType.APPLICATION_JSON)
        .body(password)
        .when()
        .post("/users/" + "<EMAIL>" + "/setup-password")
        .then()
        .log()
        .ifError()
        .statusCode(400);

    // when: resetting password for exact match it should succeed
    given()
        .contentType(MediaType.APPLICATION_JSON)
        .body(password)
        .when()
        .post("/users/" + email1 + "/setup-password")
        .then()
        .log()
        .ifError()
        .statusCode(204);

    // then: user1 has credentials
    var kcAdmin = keycloak.getKeycloakAdminClient();
    var creds1 =
        kcAdmin
            .realms()
            .realm("FathomDevRealm")
            .users()
            .get(user1.getId().toString())
            .credentials();
    assertThat(creds1).hasSize(1);

    // and then: user2 has no credentials setup
    var creds2 =
        kcAdmin
            .realms()
            .realm("FathomDevRealm")
            .users()
            .get(user2.getId().toString())
            .credentials();
    assertThat(creds2).isEmpty();
  }

  @Test
  void setupPasswordIsCalledNTimesDoesNotMeanUserHasNPasswords() {
    // given: random user data
    var firstName = RandomStringUtils.random(5, true, false);
    var lastName = RandomStringUtils.random(5, true, false);
    var email = String.format("%<EMAIL>", firstName, lastName);

    var addUser = AddUserDTO.builder().firstName(firstName).lastName(lastName).email(email).build();

    var user =
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(addUser)
            .when()
            .post("/users")
            .then()
            .log()
            .ifError()
            .statusCode(200)
            .extract()
            .as(UserDto.class);

    // when user calls setup password more than 1 time
    var password = SetupPasswordDTO.builder().password("ExtremePasswords999**").build();
    IntStream.range(0, 4)
        .forEachOrdered(
            n -> {
              given()
                  .contentType(MediaType.APPLICATION_JSON)
                  .body(password)
                  .when()
                  .post("/users/" + user.getEmail() + "/setup-password")
                  .then()
                  .log()
                  .ifError()
                  .statusCode(204);
            });

    // then: user has one set of credentials
    var kcAdmin = keycloak.getKeycloakAdminClient();
    var creds =
        kcAdmin.realms().realm("FathomDevRealm").users().get(user.getId().toString()).credentials();
    assertThat(creds).hasSize(1);
  }

  @Test
  void keycloakPasswordPolicyIsBubbledInUserController() {
    // given: random user data
    var firstName = RandomStringUtils.random(5, true, false);
    var lastName = RandomStringUtils.random(5, true, false);
    var email = String.format("%<EMAIL>", firstName, lastName);

    var addUser = AddUserDTO.builder().firstName(firstName).lastName(lastName).email(email).build();

    given()
        .contentType(MediaType.APPLICATION_JSON)
        .body(addUser)
        .when()
        .post("/users")
        .then()
        .log()
        .ifError()
        .statusCode(200)
        .extract()
        .as(UserDto.class);

    // when: setting a password that doesn't fit password policy
    var password = SetupPasswordDTO.builder().password("0").build();
    var response =
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(password)
            .when()
            .post("/users/" + email + "/setup-password")
            .then()
            .log()
            .ifError()
            .statusCode(400)
            .extract()
            .body()
            .asString();
    // then: response contains error message

    assertThat(response).contains("policy");
  }

  @Test
  void deleteCurrentUserSuccessfullyRemovesUserFromKeycloakAndDatabase() {
    // given: a user exists in keycloak and database
    var firstName = RandomStringUtils.random(5, true, false);
    var lastName = RandomStringUtils.random(5, true, false);
    var email = String.format("%<EMAIL>", firstName, lastName);

    var addUser = AddUserDTO.builder().firstName(firstName).lastName(lastName).email(email).build();

    var createdUser =
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(addUser)
            .when()
            .post("/users")
            .then()
            .log()
            .ifError()
            .statusCode(200)
            .extract()
            .as(UserDto.class);

    // verify user exists in Keycloak before deletion
    var kcAdmin = keycloak.getKeycloakAdminClient();
    var kcUserBefore =
        kcAdmin
            .realms()
            .realm("FathomDevRealm")
            .users()
            .get(createdUser.getId().toString())
            .toRepresentation();
    assertThat(kcUserBefore).isNotNull();
    assertThat(kcUserBefore.getEmail()).isEqualToIgnoringCase(email);

    // when: user deletes their own account
    given()
        .header("x-email", email)
        .when()
        .delete("/users/account")
        .then()
        .log()
        .ifError()
        .statusCode(204);

    // then: user is removed from Keycloak
    var kcUsersAfter = kcAdmin.realms().realm("FathomDevRealm").users().searchByEmail(email, true);
    assertThat(kcUsersAfter).isEmpty();
  }

  @Test
  void deleteCurrentUserReturns400WhenUserNotFoundInKeycloak() {
    // given: a non-existent user email
    var email = "<EMAIL>";

    // when: attempting to delete non-existent user
    var response =
        given()
            .header("x-email", email)
            .when()
            .delete("/users/account")
            .then()
            .log()
            .ifError()
            .statusCode(400)
            .extract()
            .body()
            .asString();

    // then: error message indicates user deletion failed
    assertThat(response).contains("Failed to delete user account");
  }

  @Test
  void deleteCurrentUserRequiresEmailHeader() {
    // when: attempting to delete user without email header
    given()
        .when()
        .delete("/users/account")
        .then()
        .log()
        .ifError()
        .statusCode(400);
  }

  @Test
  void deleteCurrentUserAlsoDeletesAssociatedRoles() {
    // given: a user exists with roles in keycloak and database
    var firstName = RandomStringUtils.random(5, true, false);
    var lastName = RandomStringUtils.random(5, true, false);
    var email = String.format("%<EMAIL>", firstName, lastName);
    var organizationId = UUID.randomUUID();

    var addUser = AddUserDTO.builder().firstName(firstName).lastName(lastName).email(email).build();

    var createdUser =
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(addUser)
            .when()
            .post("/users")
            .then()
            .log()
            .ifError()
            .statusCode(200)
            .extract()
            .as(UserDto.class);

    // create a role for the user
    given()
        .header("x-organizationId", organizationId)
        .header("x-email", email)
        .when()
        .post("/users/roles/admin")
        .then()
        .log()
        .ifError()
        .statusCode(200);

    // verify role exists before deletion
    var roleBeforeDeletion = roleRepository.findByOrganizationIdAndUsers_Email(organizationId, email);
    assertThat(roleBeforeDeletion).isNotNull();
    assertThat(roleBeforeDeletion.getUsers().getEmail()).isEqualToIgnoringCase(email);

    // when: user deletes their own account
    given()
        .header("x-email", email)
        .when()
        .delete("/users/account")
        .then()
        .log()
        .ifError()
        .statusCode(204);

    // then: user is removed from Keycloak
    var kcAdmin = keycloak.getKeycloakAdminClient();
    var kcUsersAfter = kcAdmin.realms().realm("FathomDevRealm").users().searchByEmail(email, true);
    assertThat(kcUsersAfter).isEmpty();

    // and: associated roles are also deleted from database (due to CascadeType.ALL)
    var roleAfterDeletion = roleRepository.findByOrganizationIdAndUsers_Email(organizationId, email);
    assertThat(roleAfterDeletion).isNull();
  }
}
