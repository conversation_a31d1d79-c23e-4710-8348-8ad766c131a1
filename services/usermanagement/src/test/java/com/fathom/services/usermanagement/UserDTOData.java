package com.fathom.services.usermanagement;

import com.fathom.services.usermanagement.model.dto.UserDto;
import java.util.Set;
import java.util.UUID;

public interface UserDTOData {
  default UserDto.UserDtoBuilder aUser() {
    return UserDto.builder()
        .id(UUID.randomUUID())
        .firstName("John")
        .lastName("Locke")
        .fullName("<PERSON> Locke")
        .email("<EMAIL>")
        .groups(Set.of("group1", "group2"))
        .role("member");
  }
}
