package com.fathom.services.usermanagement.controllers;

import static com.fathom.services.usermanagement.util.StaticProperties.EMAIL_HEADER;
import static com.fathom.services.usermanagement.util.StaticProperties.ORGANIZATION_HEADER;
import static io.restassured.RestAssured.given;
import static org.assertj.core.api.Assertions.*;

import com.fathom.lib.advancedfilters.model.CustomFilter;
import com.fathom.lib.advancedfilters.model.SortOrder;
import com.fathom.services.usermanagement.IntegrationTestBase;
import com.fathom.services.usermanagement.UserManagementOperations;
import com.fathom.services.usermanagement.model.dto.*;
import com.fathom.services.usermanagement.model.enums.RoleType;
import com.fathom.services.usermanagement.services.UserManagerService;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import jakarta.ws.rs.core.MediaType;

import java.util.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

// Information about user profile - like first name last name can be set in wiremock config -
// the file is in test/resources/mocks-config-userprofile.json
// the endpoint match many by email can be used to add more users and their data
class UserFilteringTest extends IntegrationTestBase implements UserManagementOperations {

    private final UUID organizationId = UUID.randomUUID();
    private final int pageNumber = 0;
    private final int pageSize = 100;
    private final String adminEmail = "<EMAIL>";
    private final List<String> memberEmails = Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>");

    @Autowired
    private UserManagerService userManagerService;

    @BeforeEach
    public void setUp() {
        RestAssured.port = port;

        userManagerService.createOwnerRole(organizationId, RoleType.SUPER_ADMIN, adminEmail);

        for (String member : memberEmails) {
            addUserAsserting200(member, organizationId, adminEmail);
        }
    }

    @Test
    void whenFilteringForUsersTheInformationFromUserProfileIsPresent() {
        var customFilter = new CustomFilter();
        customFilter.setFilter(
                Map.of("@and", List.of(Map.of("@eq", Map.of("path", "role", "value", "member")))));

        List<UserDto> users =
                given()
                        .contentType(ContentType.JSON)
                        .header(ORGANIZATION_HEADER, organizationId)
                        .queryParam("pageNumber", pageNumber)
                        .queryParam("pageSize", pageSize)
                        .body(customFilter)
                        .when()
                        .post("/users/filtered")
                        .then()
                        .log()
                        .ifError()
                        .statusCode(200)
                        .extract()
                        .body()
                        .jsonPath()
                        .getList("content", UserDto.class);

        assertThat(users).hasSize(3);

        assertThat(users).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> {
                    assertThat(user.getFirstName()).isEqualTo("Andrew");
                    assertThat(user.getLastName()).isEqualTo("Piper");
                    assertThat(user.getFullName()).isEqualTo("Andrew Piper");
                });
        assertThat(users).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> {
                    assertThat(user.getFirstName()).isEqualTo("Rafael");
                    assertThat(user.getLastName()).isEqualTo("Telecom");
                    assertThat(user.getFullName()).isEqualTo("Rafael Telecom");
                });
        assertThat(users).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> {
                    assertThat(user.getFirstName()).isEqualTo("Brenda");
                    assertThat(user.getLastName()).isEqualTo("Egglayer");
                    assertThat(user.getFullName()).isEqualTo("Brenda Egglayer");
                });
    }

    @Test
    void filterByEmailValueReturnsOneRecord() {

        CustomFilter customFilter = new CustomFilter();
        customFilter.setFilter(
                Map.of(
                        "@and", List.of(Map.of("@eq", Map.of("path", "email", "value", "<EMAIL>")))));

        CustomFilter.CustomSort customSort = new CustomFilter.CustomSort();
        customSort.setBy("id");
        customSort.setOrd(SortOrder.ASCENDING);
        customFilter.setSort(customSort);

        List<UserDto> users =
                given()
                        .contentType(ContentType.JSON)
                        .header(ORGANIZATION_HEADER, organizationId)
                        .queryParam("pageNumber", pageNumber)
                        .queryParam("pageSize", pageSize)
                        .body(customFilter)
                        .when()
                        .post("/users/filtered")
                        .then()
                        .log()
                        .ifError()
                        .statusCode(200)
                        .extract()
                        .body()
                        .jsonPath()
                        .getList("content", UserDto.class);

        assertThat(users).hasSize(1);
    }

    @Test
    void findByRoleAdminOrEmailValueReturnsCorrectResults() {
        var customFilter = new CustomFilter();
        customFilter.setFilter(
                Map.of(
                        "@and",
                        List.of(
                                Map.of(
                                        "@or",
                                        List.of(
                                                Map.of("@eq", Map.of("path", "role", "value", "admin")),
                                                Map.of("@eq", Map.of("path", "email", "value", "<EMAIL>")))))));

        CustomFilter.CustomSort customSort = new CustomFilter.CustomSort();
        customSort.setBy("id");
        customSort.setOrd(SortOrder.ASCENDING);
        customFilter.setSort(customSort);

        // Call the controller method to get filtered users
        List<UserDto> users =
                given()
                        .contentType(ContentType.JSON)
                        .header(ORGANIZATION_HEADER, organizationId)
                        .queryParam("pageNumber", pageNumber)
                        .queryParam("pageSize", pageSize)
                        .body(customFilter)
                        .when()
                        .post("/users/filtered")
                        .then()
                        .log()
                        .ifError()
                        .statusCode(200)
                        .extract()
                        .body()
                        .jsonPath()
                        .getList("content", UserDto.class);


        assertThat(users).hasSize(1);
    }


    @Test
    void advancedFilterNotIsAnArray() {
        var testJson = """
                {
                    "filter": {
                        "@and": [
                            {
                                "@not": [
                                    {
                                        "@eq": {
                                            "path": "role",
                                            "value": "super_admin"
                                        }
                                    }
                                ]
                            }
                        ]
                    },
                    "sort": {
                        "by": "role",
                        "ord": "asc"
                    }
                }
                """;

        var users = getUsersFilteredAsserting200(testJson, organizationId, pageNumber, pageSize);

        assertThat(users).isNotNull();
        assertThat(users).hasSize(3);
        assertThat(users).map(UserDto::getEmail).containsExactlyInAnyOrderElementsOf(memberEmails);

    }

    @Test
    void advancedFiltersRequireNotConditionToBeAListOfMap() {
        var jsonThatHasNotAsObject = """
                {
                    "filter": {
                        "@and": [
                            {
                                "@not": {
                                    "@eq": {
                                        "path": "role",
                                        "value": "super_admin"
                                    }
                                }
                            }
                        ]
                    },
                    "sort": {
                        "by": "role",
                        "ord": "asc"
                    }
                }
                """;

        var users = getUsersFilteredAsserting200(jsonThatHasNotAsObject, organizationId, pageNumber, pageSize);

        assertThat(users).isNotNull();
        assertThat(users).hasSize(3);
        assertThat(users).map(UserDto::getEmail).containsExactlyInAnyOrderElementsOf(memberEmails);

    }

    @Test
    void filterShouldAcceptEqAsItsOnlyCondition() {
        var jsonThatHasNotAsObject = """
                {
                    "filter": {
                        "@eq": {
                            "path": "role",
                            "value": "member"
                        }
                    },
                    "sort": {
                        "by": "role",
                        "ord": "ASC"
                    }
                }
                """;

        var users = getUsersFilteredAsserting200(jsonThatHasNotAsObject, organizationId, pageNumber, pageSize);

        assertThat(users).isNotNull();
        assertThat(users).hasSize(3);
        assertThat(users).map(UserDto::getEmail).containsExactlyInAnyOrderElementsOf(memberEmails);

    }

    @Test
    void ensureChangingRolesAffectsFilteredUsers() {
        // when: filtering users on group contains
        var roleFilter = """
                {
                  "filter": {
                    "@eq": {
                      "path": "role",
                      "value": "member"
                    }
                  }
                }
                """;

        var users = getUsersFilteredAsserting200(roleFilter, organizationId, pageNumber, pageSize);

        // then: filter works correctly
        assertThat(users).isNotNull();
        assertThat(users).hasSize(3);
        assertThat(users).map(UserDto::getEmail).containsExactlyInAnyOrderElementsOf(memberEmails);

        // and when roles are changed
        given()
                .header(ORGANIZATION_HEADER, organizationId)
                .header(EMAIL_HEADER, adminEmail)
                .param("assigneeRole", RoleType.ADMIN)
                .post("users/roles/<EMAIL>")
                .then()
                .log()
                .ifError()
                .statusCode(200);

        // and when filtering again
        var filteredEmailsAgain = getUsersFilteredAsserting200(roleFilter, organizationId, pageNumber, pageSize);

        // then: filter works correctly
        assertThat(filteredEmailsAgain).isNotNull();
        assertThat(filteredEmailsAgain).hasSize(2);
        assertThat(filteredEmailsAgain).map(UserDto::getEmail).containsExactlyInAnyOrder("<EMAIL>", "<EMAIL>");

        // and when: filtering for admins
        var filterAdmin = """
                {
                  "filter": {
                    "@eq": {
                      "path": "role",
                      "value": "admin"
                    }
                  }
                }
                """;

        // and when filtering for admins
        var admin = getUsersFilteredAsserting200(filterAdmin, organizationId, pageNumber, pageSize);

        // then: filter works correctly
        assertThat(admin).isNotNull();
        assertThat(admin).hasSize(1);
        assertThat(admin).map(UserDto::getEmail).contains("<EMAIL>");

    }

    @Test
    void ensureChangingGroupsAffectsFilteredUsers() {
        // given: groups exist
        var groups = List.of(
                new GroupCreateUpdateDto("A", "desc"),
                new GroupCreateUpdateDto("B", "desc"),
                new GroupCreateUpdateDto("C", "desc")
        );

        var groupIds = createGroupsAsserting200(groups, organizationId, adminEmail);

        // and given: users are added to groups
        for (var u : memberEmails) {
            addUsersToGroup(groupIds, u);
        }


        // when: filtering users on group contains
        var contains = """
                {
                  "filter": {
                    "@contains": {
                      "path": "groups",
                      "value": [
                        "A"
                      ]
                    }
                  }
                }
                """;

        var users = getUsersFilteredAsserting200(contains, organizationId, pageNumber, pageSize);

        // then: filter works correctly
        assertThat(users).isNotNull();
        assertThat(users).hasSize(3);
        assertThat(users).map(UserDto::getEmail).containsExactlyInAnyOrderElementsOf(memberEmails);

        // and when: groups are removed
        var deleteMembersInGroup = AddMembersToGroupDto.builder()
                .memberEmails((new HashSet<>(memberEmails)))
                .build();
        for (var g : groupIds) {
            given()
                    .contentType(MediaType.APPLICATION_JSON)
                    .header(ORGANIZATION_HEADER, organizationId)
                    .header(EMAIL_HEADER, adminEmail)
                    .body(deleteMembersInGroup)
                    .delete("/groups/members/" + g.getId())
                    .then()
                    .log()
                    .ifError()
                    .statusCode(200);
        }

        // and when: groups are changed
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("A")).toList(), "<EMAIL>");

        // and when: filtering users on group contains
        var filteredEmailsAgain = getUsersFilteredAsserting200(contains, organizationId, pageNumber, pageSize);

        // then: filter works correctly
        assertThat(filteredEmailsAgain).isNotNull();
        assertThat(filteredEmailsAgain).hasSize(1);
        assertThat(filteredEmailsAgain).map(UserDto::getEmail).contains("<EMAIL>");

    }

    @Test
    void filteringOnFieldsWithNotEqThatReNullShouldNotDeleteResultsAndNotReturnDuplicates() {
        var input = """
                {
                  "filter": {
                    "@and": [
                      {
                        "@or": [
                          {
                            "@containsignorecase": {
                              "path": "email",
                              "value": ""
                            }
                          },
                          {
                            "@containsignorecase": {
                              "path": "firstName",
                              "value": ""
                            }
                          },
                          {
                            "@containsignorecase": {
                              "path": "lastName",
                              "value": ""
                            }
                          }
                        ]
                      }
                    ]
                  },
                  "sort": null
                }
                """;
        // when: filtering users on group contains
        var filtered = getUsersFilteredAsserting200(input, organizationId, pageNumber, pageSize);

        // then: filter works correctly
        assertThat(filtered).isNotNull();
        assertThat(filtered).hasSize(3);
        assertThat(filtered).map(UserDto::getEmail)
                .containsExactlyInAnyOrderElementsOf(memberEmails);
    }

    @Test
    void filteringOnFieldsThatAreEmptyShouldTreatThemAsNotExistant() {
        var input = """
                {
                  "filter": {
                    "@and": [
                      {
                        "@or": [
                          {
                            "@containsignorecase": {
                              "path": "email",
                              "value": ""
                            }
                          },
                          {
                            "@containsignorecase": {
                              "path": "firstName",
                              "value": ""
                            }
                          },
                          {
                            "@containsignorecase": {
                              "path": "lastName",
                              "value": ""
                            }
                          }
                        ]
                      }
                    ]
                  }
                }
                """;
        // when: filtering users on group contains
        var filtered = getUsersFilteredAsserting200(input, organizationId, pageNumber, pageSize);

        // then: filter works correctly
        assertThat(filtered).isNotNull();
        assertThat(filtered).hasSize(3);
        assertThat(filtered).map(UserDto::getEmail).containsExactlyInAnyOrderElementsOf(memberEmails.stream().toList());
    }

    @Test
    void sortingUsersByGroupShouldSortByAmountOfGroups() {
        // given: groups exist
        var groups = List.of(
                new GroupCreateUpdateDto("A", "desc"),
                new GroupCreateUpdateDto("B", "desc"),
                new GroupCreateUpdateDto("C", "desc")
        );

        var groupIds = createGroupsAsserting200(groups, organizationId, adminEmail);

        // and given: users are added to groups
        addUsersToGroup(groupIds.stream().limit(3).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().limit(2).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().limit(1).toList(), "<EMAIL>");

        // and given: sorting by group
        var input = """
                {
                  "sort": {
                    "by": "groups",
                    "ord": "asc"
                  }
                }
                """;

        // when: applying the sorting
        var users = getUsersFilteredAsserting200(input, organizationId, pageNumber, pageSize);

        // then: filter works correctly
        assertThat(users).isNotNull();
        assertThat(users).hasSize(3);
        assertThat(users).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> {
                    assertThat(user.getGroups()).hasSize(3);
                });
        assertThat(users).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> {
                    assertThat(user.getGroups()).hasSize(2);
                });
        assertThat(users).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> {
                    assertThat(user.getGroups()).hasSize(1);
                });
        assertThat(users).map(UserDto::getEmail).containsExactly(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
        );
    }

    @Test
    void whenFilteringForMultipleGroupsItShouldReturnCorrectResults() {
        // given: groups exist
        var groups = List.of(
                new GroupCreateUpdateDto("A", "desc"),
                new GroupCreateUpdateDto("B", "desc"),
                new GroupCreateUpdateDto("C", "desc"),
                new GroupCreateUpdateDto("D", "desc"),
                new GroupCreateUpdateDto("E", "desc"),
                new GroupCreateUpdateDto("F", "desc")
        );

        var groupIds = createGroupsAsserting200(groups, organizationId, adminEmail);

        // and given: users are added to groups
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("A")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("B")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("C")).toList(), "<EMAIL>");

        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("A")).toList(), "<EMAIL>");

        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("A")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("B")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("C")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("D")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("E")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("F")).toList(), "<EMAIL>");

        // and given: filtering for one group
        var oneGroup = """
                {
                    "filter": {
                        "@contains": {
                            "path": "groups",
                            "value": [
                                "A"
                            ]
                        }
                    }
                }
                """;

        // when: applying the filter
        var users = getUsersFilteredAsserting200(oneGroup, organizationId, pageNumber, pageSize);


        // then: filter works correctly
        assertThat(users).isNotNull();
        assertThat(users).hasSize(3);
        assertThat(users).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> assertThat(user.getGroups()).hasSize(3));
        assertThat(users).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> assertThat(user.getGroups()).hasSize(1));
        assertThat(users).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> assertThat(user.getGroups()).hasSize(6));
        assertThat(users).map(UserDto::getEmail).containsExactlyInAnyOrder(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
        );

        // and given: filtering for multiple groups
        var abcGroups = """
                {
                    "filter": {
                        "@contains": {
                            "path": "groups",
                            "value": [
                                "A",
                                "B",
                                "C"
                            ]
                        }
                    }
                }
                """;

        // when: applying the filter
        var users2 = getUsersFilteredAsserting200(abcGroups, organizationId, pageNumber, pageSize);

        // then: filter works correctly
        assertThat(users2).isNotNull();
        assertThat(users2).hasSize(2);
        assertThat(users2).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> assertThat(user.getGroups()).hasSize(3));
        assertThat(users2).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> assertThat(user.getGroups()).hasSize(6));
        assertThat(users2).map(UserDto::getEmail).containsExactlyInAnyOrder(
                "<EMAIL>",
                "<EMAIL>"
        );

        // and given: filtering for DEF groups

        var defGroups = """
                {
                    "filter": {
                        "@contains": {
                            "path": "groups",
                            "value": [
                                "D",
                                "E",
                                "F"
                            ]
                        }
                    }
                }
                """;

        // when: applying the filter
        var users3 = getUsersFilteredAsserting200(defGroups, organizationId, pageNumber, pageSize);
        // then: filter works correctly

        assertThat(users3).isNotNull();
        assertThat(users3).hasSize(1);
        assertThat(users3).map(UserDto::getEmail).containsExactly("<EMAIL>");
        assertThat(users3).map(UserDto::getGroups).containsExactlyInAnyOrder(Set.of("A", "B", "C", "D", "E", "F"));


    }

    @Test
    void whenFilteringWithContainsTheResultsAreCaseSensitive() {
        // given: groups exist
        var groups = List.of(
                new GroupCreateUpdateDto("A", "desc"),
                new GroupCreateUpdateDto("B", "desc"),
                new GroupCreateUpdateDto("C", "desc"),
                new GroupCreateUpdateDto("D", "desc"),
                new GroupCreateUpdateDto("E", "desc"),
                new GroupCreateUpdateDto("F", "desc")
        );

        var groupIds = createGroupsAsserting200(groups, organizationId, adminEmail);

        // and given: users are added to groups
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("A")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("B")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("C")).toList(), "<EMAIL>");

        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("A")).toList(), "<EMAIL>");

        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("A")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("B")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("C")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("D")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("E")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("F")).toList(), "<EMAIL>");

        // and given: filtering for one group
        var oneGroup = """
                {
                    "filter": {
                        "@contains": {
                            "path": "groups",
                            "value": [
                                "a"
                            ]
                        }
                    }
                }
                """;

        // when: applying the filter
        var users = getUsersFilteredAsserting200(oneGroup, organizationId, pageNumber, pageSize);


        // then: filter works correctly
        assertThat(users).isNotNull();
        assertThat(users).hasSize(0);

        // and given: filtering for multiple groups
        var abcGroups = """
                {
                    "filter": {
                        "@contains": {
                            "path": "groups",
                            "value": [
                                "a",
                                "b",
                                "c"
                            ]
                        }
                    }
                }
                """;

        // when: applying the filter
        var users2 = getUsersFilteredAsserting200(abcGroups, organizationId, pageNumber, pageSize);

        // then: filter works correctly
        assertThat(users2).isNotNull();
        assertThat(users2).hasSize(0);

        // and given: filtering for DEF groups
        var defGroups = """
                {
                    "filter": {
                        "@contains": {
                            "path": "groups",
                            "value": [
                                "d",
                                "e",
                                "f"
                            ]
                        }
                    }
                }
                """;

        // when: applying the filter
        var users3 = getUsersFilteredAsserting200(defGroups, organizationId, pageNumber, pageSize);
        // then: filter works correctly

        assertThat(users3).isNotNull();
        assertThat(users3).hasSize(0);
    }


    @Test
    void whenFilteringUsingContainsIgnoreCaseTheResultsIncludeCorrectEntries() {
        // given: groups exist
        var groups = List.of(
                new GroupCreateUpdateDto("A", "desc"),
                new GroupCreateUpdateDto("B", "desc"),
                new GroupCreateUpdateDto("C", "desc"),
                new GroupCreateUpdateDto("D", "desc"),
                new GroupCreateUpdateDto("E", "desc"),
                new GroupCreateUpdateDto("F", "desc")
        );

        var groupIds = createGroupsAsserting200(groups, organizationId, adminEmail);

        // and given: users are added to groups
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("A")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("B")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("C")).toList(), "<EMAIL>");

        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("A")).toList(), "<EMAIL>");

        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("A")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("B")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("C")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("D")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("E")).toList(), "<EMAIL>");
        addUsersToGroup(groupIds.stream().filter(g -> g.getName().equals("F")).toList(), "<EMAIL>");

        // and given: filtering for one group
        var oneGroup = """
                {
                    "filter": {
                        "@containsignorecase": {
                            "path": "groups",
                            "value": [
                                "a"
                            ]
                        }
                    }
                }
                """;

        // when: applying the filter
        var users = getUsersFilteredAsserting200(oneGroup, organizationId, pageNumber, pageSize);


        // then: filter works correctly
        assertThat(users).isNotNull();
        assertThat(users).hasSize(3);
        assertThat(users).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> assertThat(user.getGroups()).hasSize(3));
        assertThat(users).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> assertThat(user.getGroups()).hasSize(1));
        assertThat(users).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> assertThat(user.getGroups()).hasSize(6));
        assertThat(users).map(UserDto::getEmail).containsExactlyInAnyOrder(
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
        );

        // and given: filtering for multiple groups
        var abcGroups = """
                {
                    "filter": {
                        "@containsignorecase": {
                            "path": "groups",
                            "value": [
                                "a",
                                "b",
                                "c"
                            ]
                        }
                    }
                }
                """;

        // when: applying the filter
        var users2 = getUsersFilteredAsserting200(abcGroups, organizationId, pageNumber, pageSize);

        // then: filter works correctly
        assertThat(users2).isNotNull();
        assertThat(users2).hasSize(2);
        assertThat(users2).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> assertThat(user.getGroups()).hasSize(3));
        assertThat(users2).filteredOn(u -> u.getEmail().equals("<EMAIL>"))
                .hasSize(1)
                .first()
                .satisfies(user -> assertThat(user.getGroups()).hasSize(6));
        assertThat(users2).map(UserDto::getEmail).containsExactlyInAnyOrder(
                "<EMAIL>",
                "<EMAIL>"
        );

        // and given: filtering for DEF groups

        var defGroups = """
                {
                    "filter": {
                        "@containsignorecase": {
                            "path": "groups",
                            "value": [
                                "d",
                                "e",
                                "f"
                            ]
                        }
                    }
                }
                """;

        // when: applying the filter
        var users3 = getUsersFilteredAsserting200(defGroups, organizationId, pageNumber, pageSize);
        // then: filter works correctly

        assertThat(users3).isNotNull();
        assertThat(users3).hasSize(1);
        assertThat(users3).map(UserDto::getEmail).containsExactly("<EMAIL>");
        assertThat(users3).map(UserDto::getGroups).containsExactlyInAnyOrder(Set.of("A", "B", "C", "D", "E", "F"));


    }

    @Test
    void sortingByFullNameShouldSortUsersByEmailInSecondOrder() {
        // user1 full name is: Andrew Piper
        // user2 full name is: Rafael Telecom
        // user3 full name is: Brenda Egglayer

        //given some users without first and last name exist
        addUserAsserting200("<EMAIL>", organizationId, adminEmail);
        addUserAsserting200("<EMAIL>", organizationId, adminEmail);

        var input = """
                {
                  "sort": {
                    "by": "fullName",
                    "ord": "asc"
                  }
                }
                """;

        var users = getUsersFilteredAsserting200(input, organizationId, pageNumber, pageSize);

        assertThat(users).isNotNull();
        assertThat(users).hasSize(5);
        assertThat(users).map(UserDto::getEmail).containsExactlyElementsOf(
                List.of("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>")
        );
    }

    @Test
    void sortingAndFilteringByNothingByFullNameShouldSortUsersByEmailInSecondOrder() {
        // user1 full name is: Andrew Piper
        // user2 full name is: Rafael Telecom
        // user3 full name is: Brenda Egglayer

        //given some users without first and last name exist
        addUserAsserting200("<EMAIL>", organizationId, adminEmail);
        addUserAsserting200("<EMAIL>", organizationId, adminEmail);

        var input = """
                {
                  "filter": {
                    "@and": []
                  },
                  "sort": {
                    "by": "fullName",
                    "ord": "asc"
                  }
                }
                """;

        var users = getUsersFilteredAsserting200(input, organizationId, pageNumber, pageSize);

        assertThat(users).isNotNull();
        assertThat(users).hasSize(5);
        assertThat(users).map(UserDto::getEmail).containsExactlyElementsOf(
                List.of("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>")
        );
    }

    @Test
    void sortingByFullnameSecondByEmailShouldBeCaseInsensitiveAndOrderCorrectly() {
        // given emails with different upper characters
        addUserAsserting200("<EMAIL>", organizationId, adminEmail);
        addUserAsserting200("<EMAIL>", organizationId, adminEmail);
        addUserAsserting200("<EMAIL>", organizationId, adminEmail);
        addUserAsserting200("<EMAIL>", organizationId, adminEmail);


        // when sorting by full name
        var input = """
                {
                  "sort": {
                    "by": "fullName",
                    "ord": "asc"
                  }
                }
                """;

        var users = getUsersFilteredAsserting200(input, organizationId, pageNumber, pageSize);

        // then the users with names should come first, and after them case insetively by emails
        // note in test sortingAndFilteringByNothingByFullNameShouldSortUsersByEmailInSecondOrder
        // what are the names of users
        assertThat(users).isNotNull();
        assertThat(users).hasSize(7);
        assertThat(users).map(UserDto::getEmail).containsExactlyElementsOf(
                List.of(
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>",
                        "<EMAIL>"

                )
        );

    }

    @Test
    void filteringOnAdminEmailShouldWorkProperly() {
        var input = """
                {
                    "filter": {
                        "@eq": {
                            "path": "email",
                            "value": "<EMAIL>"
                        }
                    },
                    "sort": {
                        "by": "email",
                        "ord": "ASC"
                    }
                }
                """;

        var users = getUsersFilteredAsserting200(input, organizationId, pageNumber, pageSize);

        assertThat(users).isNotNull();
        assertThat(users).isEmpty();
    }


    private void addUsersToGroup(List<GroupDto> groupIds, String email) {
        var addMembersToGroupDto = AddMembersToGroupDto.builder()
                .memberEmails(Collections.singleton(email))
                .build();

        for (var g : groupIds) {
            addUserToGrupsAsserting200(addMembersToGroupDto, organizationId, adminEmail, g.getId());
        }
    }

}


