package com.fathom.services.usermanagement.model.dto.sorting;

import static org.assertj.core.api.Assertions.assertThat;

import com.fathom.services.usermanagement.UserDTOData;
import com.fathom.services.usermanagement.model.dto.UserDto;
import java.time.LocalDateTime;
import java.util.*;
import org.junit.jupiter.api.Test;

class SortingTest implements UserDTOData {

  @Test
  void groupsShouldBeOrderedByLength() {
    var user1 = aUser().groups(Set.of("group1", "group2")).build();
    var user2 = aUser().groups(Set.of("group1")).build();
    var comparator = UserDtoComparatorFactory.getComparator("groups");

    int result = comparator.compare(user1, user2);

    assertThat(result).isGreaterThan(0);
  }

  @Test
  void emailShouldBeOrderedAlphabetically() {
    var user1 = aUser().email("<EMAIL>").build();
    var user2 = aUser().email("<EMAIL>").build();
    var comparator = UserDtoComparatorFactory.getComparator("email");

    int result = comparator.compare(user1, user2);

    assertThat(result).isGreaterThan(0);
  }

  @Test
  void firstNameShouldBeOrderedAlphabetically() {
    var user1 = aUser().firstName("Bob").build();
    var user2 = aUser().firstName("Alice").build();
    var comparator = UserDtoComparatorFactory.getComparator("firstName");

    int result = comparator.compare(user1, user2);

    assertThat(result).isGreaterThan(0);
  }

  @Test
  void lastNameShouldBeOrderedAlphabetically() {
    var user1 = aUser().lastName("Smith").build();
    var user2 = aUser().lastName("Jones").build();
    var comparator = UserDtoComparatorFactory.getComparator("lastName");

    int result = comparator.compare(user1, user2);

    assertThat(result).isGreaterThan(0);
  }

  @Test
  void fullNameShouldBeOrderedAlphabetically() {
    var user1 = aUser().fullName("Bob Smith").build();
    var user2 = aUser().fullName("Alice Jones").build();
    var comparator = UserDtoComparatorFactory.getComparator("fullName");

    int result = comparator.compare(user1, user2);

    assertThat(result).isGreaterThan(0);
  }

  @Test
  void roleShouldBeOrderedAlphabetically() {
    var user1 = aUser().role("USER").build();
    var user2 = aUser().role("ADMIN").build();
    var comparator = UserDtoComparatorFactory.getComparator("role");

    int result = comparator.compare(user1, user2);

    assertThat(result).isGreaterThan(0);
  }

  @Test
  void activeShouldBeOrdered() {
    var user1 = aUser().active(true).build();
    var user2 = aUser().active(false).build();
    var comparator = UserDtoComparatorFactory.getComparator("active");

    int result = comparator.compare(user1, user2);

    assertThat(result).isGreaterThan(0);
  }

  @Test
  void invitationStatusShouldBeOrderedAlphabetically() {
    var user1 = aUser().invitationStatus("SENT").build();
    var user2 = aUser().invitationStatus("PENDING").build();
    var comparator = UserDtoComparatorFactory.getComparator("invitationStatus");

    int result = comparator.compare(user1, user2);

    assertThat(result).isGreaterThan(0);
  }

  @Test
  void invitationCreatedDateShouldBeOrderedChronologically() {
    LocalDateTime later = LocalDateTime.now();
    LocalDateTime earlier = later.minusDays(1);

    var user1 = aUser().invitationCreatedDate(later).build();
    var user2 = aUser().invitationCreatedDate(earlier).build();
    var comparator = UserDtoComparatorFactory.getComparator("invitationCreatedDate");

    int result = comparator.compare(user1, user2);

    assertThat(result).isGreaterThan(0);
  }

  @Test
  void invitationUpdateDateShouldBeOrderedChronologically() {
    LocalDateTime later = LocalDateTime.now();
    LocalDateTime earlier = later.minusHours(1);

    var user1 = aUser().invitationUpdateDate(later).build();
    var user2 = aUser().invitationUpdateDate(earlier).build();
    var comparator = UserDtoComparatorFactory.getComparator("invitationUpdateDate");

    int result = comparator.compare(user1, user2);

    assertThat(result).isGreaterThan(0);
  }

  @Test
  void nullValuesShouldComeAfterNonNull() {
    var user1 = aUser().email(null).build();
    var user2 = aUser().email("<EMAIL>").build();
    var comparator = UserDtoComparatorFactory.getComparator("email");

    int result = comparator.compare(user1, user2);

    assertThat(result).isGreaterThan(0);
  }

  @Test
  void fullnameShouldSortByNameFirstAndThenByEmail() {
    var bob =
        aUser()
            .firstName("Bob")
            .lastName("Vance")
            .fullName("Bob Vance")
            .email("<EMAIL>")
            .build();

    var mike =
        aUser()
            .firstName("Prison")
            .lastName("Mike")
            .fullName("Prison Mike")
            .email("<EMAIL>")
            .build();

    var dwight =
        aUser()
            .firstName(null)
            .lastName(null)
            .fullName(null)
            .email("<EMAIL>")
            .build();

    var mose =
        aUser().firstName(null).lastName(null).fullName(null).email("<EMAIL>").build();

    var comparator = UserDtoComparatorFactory.getComparator("fullName");

    List<UserDto> users = new ArrayList<>(List.of(dwight, mike, mose, bob));
    users.sort(comparator);

    assertThat(users).containsExactly(bob, mike, dwight, mose);
  }
}
