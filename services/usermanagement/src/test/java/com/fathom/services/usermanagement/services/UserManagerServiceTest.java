package com.fathom.services.usermanagement.services;

import static org.assertj.core.api.Assertions.*;

import com.fathom.lib.advancedfilters.model.CustomFilter;
import com.fathom.lib.advancedfilters.model.FilterCriteria;
import com.fathom.lib.advancedfilters.model.SortOrder;
import com.fathom.services.usermanagement.IntegrationTestBase;
import com.fathom.services.usermanagement.model.dto.UserDto;
import com.fathom.services.usermanagement.model.enums.RoleType;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class UserManagerServiceTest extends IntegrationTestBase {
  @Autowired private UserManagerService userManagerService;

  private final UUID organizationId = UUID.randomUUID();

  @BeforeEach
  public void setUp() {
    var adminEmail = "<EMAIL>";

    // Create admin role
    userManagerService.createOwnerRole(organizationId, RoleType.SUPER_ADMIN, adminEmail);

    List<String> memberEmails =
        Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>");

    for (String member : memberEmails) {
      userManagerService.createRole(organizationId, RoleType.MEMBER, adminEmail, member);
    }
  }

  @Test
  public void customFilterGeneratedInJavaWorks() {
    CustomFilter filter = new CustomFilter();
    filter.setFilter(
        FilterCriteria.filter()
            .and(FilterCriteria.filter().not(FilterCriteria.eq("role", "super_admin")))
            .build());
    CustomFilter.CustomSort sort = new CustomFilter.CustomSort();
    sort.setBy("email");
    sort.setOrd(SortOrder.DESCENDING);
    filter.setSort(sort);

    var got = userManagerService.getUsersFiltered(organizationId, filter, 0, 10);

    assertThat(got.getContent()).hasSize(3);
    assertThat(got.getTotalElements()).isEqualTo(3);
    assertThat(got.getContent())
        .map(UserDto::getEmail)
        .containsExactlyInAnyOrder("<EMAIL>", "<EMAIL>", "<EMAIL>");
  }
}
