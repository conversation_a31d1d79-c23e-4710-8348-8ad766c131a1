package com.fathom.services.usermanagement;

import static com.fathom.services.usermanagement.util.StaticProperties.EMAIL_HEADER;
import static com.fathom.services.usermanagement.util.StaticProperties.ORGANIZATION_HEADER;
import static io.restassured.RestAssured.given;

import com.fathom.services.usermanagement.model.dto.AddMembersToGroupDto;
import com.fathom.services.usermanagement.model.dto.GroupCreateUpdateDto;
import com.fathom.services.usermanagement.model.dto.GroupDto;
import com.fathom.services.usermanagement.model.dto.UserDto;
import com.fathom.services.usermanagement.model.enums.RoleType;
import io.restassured.http.ContentType;
import jakarta.ws.rs.core.MediaType;
import java.util.List;
import java.util.UUID;

public interface UserManagementOperations {
  default List<UserDto> getUsersFilteredAsserting200(
      String jsonFilter, UUID organizationId, int pageNumber, int pageSize) {
    return given()
        .contentType(ContentType.JSON)
        .header(ORGANIZATION_HEADER, organizationId)
        .queryParam("pageNumber", pageNumber)
        .queryParam("pageSize", pageSize)
        .body(jsonFilter)
        .when()
        .post("/users/filtered")
        .then()
        .log()
        .ifError()
        .statusCode(200)
        .extract()
        .body()
        .jsonPath()
        .getList("content", UserDto.class);
  }

  default List<GroupDto> createGroupsAsserting200(
      List<GroupCreateUpdateDto> groups, UUID organizationId, String adminEmail) {
    return given()
        .contentType(MediaType.APPLICATION_JSON)
        .header(ORGANIZATION_HEADER, organizationId)
        .header(EMAIL_HEADER, adminEmail)
        .body(groups)
        .when()
        .post("/groups")
        .then()
        .log()
        .ifError()
        .statusCode(200)
        .extract()
        .jsonPath()
        .getList(".", GroupDto.class);
  }

  default void addUserToGrupsAsserting200(
      AddMembersToGroupDto users, UUID organizationId, String adminEmail, UUID groupId) {
    given()
        .contentType(MediaType.APPLICATION_JSON)
        .header(ORGANIZATION_HEADER, organizationId)
        .header(EMAIL_HEADER, adminEmail)
        .body(users)
        .put("/groups/members/" + groupId)
        .then()
        .log()
        .ifError()
        .statusCode(200);
  }

  default void addUserAsserting200(String email, UUID organizationId, String adminEmail) {
    given()
        .header(ORGANIZATION_HEADER, organizationId)
        .header(EMAIL_HEADER, adminEmail)
        .param("assigneeRole", RoleType.MEMBER)
        .post("users/roles/" + email)
        .then()
        .log()
        .ifError()
        .statusCode(200);
  }
}
