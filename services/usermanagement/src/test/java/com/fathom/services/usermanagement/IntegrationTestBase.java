package com.fathom.services.usermanagement;

import dasniko.testcontainers.keycloak.KeycloakContainer;
import org.junit.jupiter.api.BeforeAll;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.utility.DockerImageName;
import org.wiremock.integrations.testcontainers.WireMockContainer;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public abstract class IntegrationTestBase {
  @LocalServerPort protected int port;

  @BeforeAll
  public static void beforeAll() {
    postgres.start();
    keycloak.start();
    wiremockFileservice.start();
    wiremockUserprofile.start();
    wiremockInvitation.start();
  }

  protected static final PostgreSQLContainer<?> postgres =
      new PostgreSQLContainer<>("postgres:12")
          .withExposedPorts(5432)
          .withReuse(true)
          .withLabel("reuse-id", "pgusermanagement");

  protected static final KeycloakContainer keycloak =
      new KeycloakContainer()
          .withRealmImportFile("kc-test-realm.json")
          .withAdminUsername("testadmin")
          .withAdminPassword("testpassword")
          .withReuse(true)
          .withLabel("reuse-id", "keycloakusermanagement");

  protected static WireMockContainer wiremockFileservice =
      new WireMockContainer("wiremock/wiremock:3.6.0")
          .withMappingFromResource("mocks-config-fileservice.json")
          .withReuse(true)
          .withLabel("reuse-id", "wiremockfsusermanagement");

  protected static WireMockContainer wiremockUserprofile =
      new WireMockContainer("wiremock/wiremock:3.6.0")
          .withMappingFromResource("mocks-config-userprofile.json")
          .withReuse(true)
          .withLabel("reuse-id", "wiremockprofileusermanagement");

  protected static WireMockContainer wiremockInvitation =
      new WireMockContainer("wiremock/wiremock:3.6.0")
          .withMappingFromResource("mocks-config-invitation.json")
          .withReuse(true)
          .withLabel("reuse-id", "wiremockinvitationusermanagement");

  @DynamicPropertySource
  static void configureProperties(DynamicPropertyRegistry registry) {
    registry.add("spring.flyway.url", postgres::getJdbcUrl);
    registry.add("spring.flyway.user", postgres::getUsername);
    registry.add("spring.flyway.password", postgres::getPassword);

    registry.add("spring.datasource.url", postgres::getJdbcUrl);
    registry.add("spring.datasource.username", postgres::getUsername);
    registry.add("spring.datasource.password", postgres::getPassword);

    registry.add("services.fileservice.name", wiremockFileservice::getBaseUrl);
    registry.add("services.userprofile.name", wiremockUserprofile::getBaseUrl);
    registry.add("services.invitation.name", wiremockInvitation::getBaseUrl);
    registry.add("keycloak.auth-server-url", keycloak::getAuthServerUrl);
  }
}
