package com.fathom.services.usermanagement.controllers;

import static io.restassured.RestAssured.when;

import com.fathom.services.usermanagement.IntegrationTestBase;
import io.restassured.RestAssured;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class OpenApiTest extends IntegrationTestBase {

  @BeforeEach
  public void setUp() {
    RestAssured.port = port;
  }

  @Test
  void testSwaggerUIReturns200() {
    when().get("/v3/api-docs").then().log().ifError().statusCode(200);
  }
}
