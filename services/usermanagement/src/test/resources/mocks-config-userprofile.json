{"mappings": [{"name": "Update user profile", "request": {"method": "PUT", "urlPattern": "/profiles/[a-f0-9\\-]{36}", "headers": {"X-Email": {"matches": ".+@.+"}, "Content-Type": {"equalTo": "application/json"}}, "bodyPatterns": [{"matchesJsonPath": "$.firstName"}, {"matchesJsonPath": "$.lastName"}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"profileId": "{{request.pathSegments.[1]}}", "firstName": "{{jsonPath request.body '$.firstName'}}", "lastName": "{{jsonPath request.body '$.lastName'}}", "email": "<EMAIL>", "updatedAt": "2023-01-02T10:00:00Z"}}}, {"name": "Create user profile", "request": {"method": "GET", "url": "/profiles", "headers": {"X-Email": {"matches": ".+@.+"}, "Content-Type": {"equalTo": "application/json"}}}, "response": {"status": 201, "headers": {"Content-Type": "application/json"}, "jsonBody": {"profileId": "550e8400-e29b-41d4-a716-446655440000", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "createdAt": "2023-01-01T10:00:00Z", "updatedAt": "2023-01-01T10:00:00Z"}}}, {"name": "Delete user profile", "request": {"method": "DELETE", "urlPattern": "/profiles/[a-f0-9\\-]{36}", "headers": {"X-Email": {"matches": ".+@.+"}}}, "response": {"status": 204}}, {"name": "View all user profiles", "request": {"method": "GET", "url": "/profiles/retrieveAll", "headers": {"X-Email": {"matches": ".+@.+"}}, "queryParameters": {"pageNumber": {"matches": "[0-9]+"}, "pageSize": {"matches": "[0-9]+"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"content": [{"profileId": "550e8400-e29b-41d4-a716-446655440000", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"profileId": "550e8400-e29b-41d4-a716-446655440001", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>"}], "pageNumber": 1, "totalPages": 1, "totalElements": 2}}}, {"name": "View user profile by email", "request": {"method": "GET", "url": "/profiles", "headers": {"X-Email": {"matches": ".+@.+"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"profileId": "550e8400-e29b-41d4-a716-446655440000", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "createdAt": "2023-01-01T10:00:00Z", "updatedAt": "2023-01-01T10:00:00Z"}}}, {"name": "View user profiles by emails", "request": {"method": "POST", "url": "/profiles/retrieveManyByEmails", "bodyPatterns": [{"matchesJsonPath": "$[*]"}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": [{"profileId": "550e8400-e29b-41d4-a716-446655440000", "firstName": "<PERSON>", "lastName": "Piper", "email": "<EMAIL>"}, {"profileId": "550e8400-e29b-41d4-a716-446655440001", "firstName": "<PERSON>", "lastName": "Telecom", "email": "<EMAIL>"}, {"profileId": "550e8400-e29b-41d4-a716-446655440002", "firstName": "<PERSON>", "lastName": "Egglayer", "email": "<EMAIL>"}]}}, {"name": "Find subordinates", "request": {"method": "GET", "urlPattern": "/profiles/[a-f0-9\\-]{36}/subordinates", "headers": {"X-Email": {"matches": ".+@.+"}, "X-Organization-Id": {"matches": "[a-f0-9\\-]{36}"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"content": [{"profileId": "550e8400-e29b-41d4-a716-446655440002", "firstName": "<PERSON>", "lastName": "<PERSON>", "email": "<EMAIL>"}], "pageNumber": 1, "totalPages": 1, "totalElements": 1}}}, {"name": "Add subordinates", "request": {"method": "POST", "urlPattern": "/profiles/[a-f0-9\\-]{36}/subordinates", "headers": {"X-Email": {"matches": ".+@.+"}, "X-Organization-Id": {"matches": "[a-f0-9\\-]{36}"}, "Content-Type": {"equalTo": "application/json"}}}, "response": {"status": 201}}, {"name": "Delete subordinates", "request": {"method": "DELETE", "urlPattern": "/profiles/[a-f0-9\\-]{36}/subordinates", "headers": {"X-Email": {"matches": ".+@.+"}, "X-Organization-Id": {"matches": "[a-f0-9\\-]{36}"}}}, "response": {"status": 204}}, {"name": "View all emails", "request": {"method": "GET", "url": "/profiles/subordinates/contacts/available", "headers": {"X-Email": {"matches": ".+@.+"}, "X-Organization-Id": {"matches": "[a-f0-9\\-]{36}"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": [{"profileId": "550e8400-e29b-41d4-a716-446655440002", "email": "<EMAIL>", "fullName": "<PERSON>"}, {"profileId": "550e8400-e29b-41d4-a716-446655440003", "email": "<EMAIL>", "fullName": "<PERSON>"}]}}, {"name": "Find departments", "request": {"method": "GET", "urlPattern": "/profiles/[a-f0-9\\-]{36}/departments", "headers": {"X-Email": {"matches": ".+@.+"}, "X-Organization-Id": {"matches": "[a-f0-9\\-]{36}"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"content": [{"profileId": "550e8400-e29b-41d4-a716-446655440010", "name": "Engineering", "description": "Software Engineering Department"}, {"profileId": "550e8400-e29b-41d4-a716-446655440011", "name": "Marketing", "description": "Marketing Department"}], "pageNumber": 1, "totalPages": 1, "totalElements": 2}}}, {"name": "Add departments", "request": {"method": "POST", "urlPattern": "/profiles/[a-f0-9\\-]{36}/departments", "headers": {"X-Email": {"matches": ".+@.+"}, "X-Organization-Id": {"matches": "[a-f0-9\\-]{36}"}, "Content-Type": {"equalTo": "application/json"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"MANAGER": [{"profileId": "550e8400-e29b-41d4-a716-446655440010", "name": "Engineering", "description": "Software Engineering Department"}], "MEMBER": [{"profileId": "550e8400-e29b-41d4-a716-446655440011", "name": "Marketing", "description": "Marketing Department"}]}}}, {"name": "Delete departments", "request": {"method": "DELETE", "urlPattern": "/profiles/[a-f0-9\\-]{36}/departments", "headers": {"X-Email": {"matches": ".+@.+"}, "X-Organization-Id": {"matches": "[a-f0-9\\-]{36}"}}}, "response": {"status": 204}}]}