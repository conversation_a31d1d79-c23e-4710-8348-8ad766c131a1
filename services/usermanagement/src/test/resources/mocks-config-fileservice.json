{"mappings": [{"request": {"method": "GET", "url": "/files", "queryParameters": {"page": {"matches": ".*"}, "size": {"matches": ".*"}}}, "response": {"status": 200, "jsonBody": {"content": [{"id": "file-id-1", "uuid": "resource-uuid-1", "fileName": "example1.pdf", "contentType": "application/pdf", "description": "Sample file 1", "classification": "DOCUMENT"}, {"id": "file-id-2", "uuid": "resource-uuid-2", "fileName": "example2.jpg", "contentType": "image/jpeg", "description": "Sample file 2", "classification": "IMAGE"}], "pageable": {"pageNumber": 0, "pageSize": 50, "sort": {"sorted": false}}, "totalElements": 2, "totalPages": 1}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "GET", "urlPattern": "/file/([^/]+)"}, "response": {"status": 200, "headers": {"Content-Type": "application/octet-stream", "Content-Disposition": "attachment; filename=example.pdf"}, "base64Body": "JVBERi0xLjMNCiXi48/TDQoNCjEgMCBvYmoNCjw8DQovVHlwZSAvQ2F0YWxvZw0KL091dGxpbmVzIDIgMCBSDQovUGFnZXMgMyAwIFINCj4+DQplbmRvYmoNCg=="}}, {"request": {"method": "GET", "urlPattern": "/file/download/([^/]+)"}, "response": {"status": 200, "headers": {"Content-Type": "application/octet-stream", "Content-Disposition": "attachment; filename=example.pdf"}, "base64Body": "JVBERi0xLjMNCiXi48/TDQoNCjEgMCBvYmoNCjw8DQovVHlwZSAvQ2F0YWxvZw0KL091dGxpbmVzIDIgMCBSDQovUGFnZXMgMyAwIFINCj4+DQplbmRvYmoNCg=="}}, {"request": {"method": "POST", "url": "/file/generate-download-token"}, "response": {"status": 200, "jsonBody": [{"fileId": "file-id-1", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmaWxlSWQiOiJmaWxlLWlkLTEifQ.token1"}, {"fileId": "file-id-2", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmaWxlSWQiOiJmaWxlLWlkLTIifQ.token2"}], "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "GET", "urlPattern": "/file/([^/]+)/info"}, "response": {"status": 200, "jsonBody": {"id": "file-id-1", "uuid": "resource-uuid-1", "fileName": "example.pdf", "contentType": "application/pdf", "description": "Sample file", "classification": "DOCUMENT", "displayName": "Example Document", "size": 12345, "createdAt": "2023-01-01T12:00:00Z", "updatedAt": "2023-01-01T12:00:00Z"}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "POST", "urlPattern": "/files(/.*)?", "multipartPatterns": [{"name": "file"}]}, "response": {"status": 201, "jsonBody": {"id": "new-file-id", "uuid": "resource-uuid", "fileName": "uploaded-file.pdf", "contentType": "application/pdf", "description": "Newly uploaded file", "classification": "DOCUMENT", "displayName": "Uploaded Document", "size": 12345, "createdAt": "2023-01-01T12:00:00Z", "updatedAt": "2023-01-01T12:00:00Z"}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "PUT", "url": "/files"}, "response": {"status": 200, "jsonBody": {"id": "file-id-1", "uuid": "resource-uuid-1", "fileName": "updated-file.pdf", "contentType": "application/pdf", "description": "Updated description", "classification": "DOCUMENT", "displayName": "Updated Document", "size": 12345, "createdAt": "2023-01-01T12:00:00Z", "updatedAt": "2023-01-01T13:00:00Z"}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "DELETE", "urlPattern": "/file/([^/]+)"}, "response": {"status": 204}}, {"request": {"method": "DELETE", "urlPattern": "/files/([^/]+)"}, "response": {"status": 200, "body": "2", "headers": {"Content-Type": "text/plain"}}}, {"request": {"method": "POST", "url": "/files/byUUID"}, "response": {"status": 200, "jsonBody": [{"id": "file-id-1", "uuid": "resource-uuid-1", "fileName": "example1.pdf", "contentType": "application/pdf", "description": "Sample file 1", "classification": "DOCUMENT"}, {"id": "file-id-2", "uuid": "resource-uuid-1", "fileName": "example2.jpg", "contentType": "image/jpeg", "description": "Sample file 2", "classification": "IMAGE"}], "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "POST", "url": "/files/upload", "multipartPatterns": [{"name": "file"}]}, "response": {"status": 201, "jsonBody": {"id": "new-file-id", "uuid": "resource-uuid", "fileName": "uploaded-file.pdf", "contentType": "application/pdf", "description": "Newly uploaded file", "classification": "DOCUMENT"}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "POST", "url": "/files/upload/request"}, "response": {"status": 200, "jsonBody": {"uploadUrl": "https://example.com/upload", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.token", "expiration": "2023-01-02T12:00:00Z"}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "POST", "url": "/files/upload/direct", "headers": {"X-Upload-Token": {"matches": ".*"}}, "multipartPatterns": [{"name": "file"}]}, "response": {"status": 201, "jsonBody": {"id": "new-file-id", "uuid": "resource-uuid", "fileName": "directly-uploaded-file.pdf", "contentType": "application/pdf", "description": "Directly uploaded file", "classification": "DOCUMENT"}, "headers": {"Content-Type": "application/json"}}}, {"request": {"method": "POST", "url": "/files/filtered"}, "response": {"status": 200, "jsonBody": {"content": [{"id": "file-id-1", "uuid": "resource-uuid-1", "fileName": "filtered1.pdf", "contentType": "application/pdf", "description": "Filtered file 1", "classification": "DOCUMENT"}, {"id": "file-id-2", "uuid": "resource-uuid-2", "fileName": "filtered2.jpg", "contentType": "image/jpeg", "description": "Filtered file 2", "classification": "IMAGE"}], "pageable": {"pageNumber": 0, "pageSize": 50, "sort": {"sorted": false}}, "totalElements": 2, "totalPages": 1}, "headers": {"Content-Type": "application/json"}}}]}