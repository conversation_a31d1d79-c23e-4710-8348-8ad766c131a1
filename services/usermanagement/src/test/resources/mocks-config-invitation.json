{"mappings": [{"name": "Send Invitations", "request": {"method": "POST", "url": "/user/invitation/send", "headers": {"x-organizationId": {"matches": "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "x-email": {"matches": ".+@.+\\..+"}}, "queryParameters": {"organizationName": {"equalTo": "Test Organization"}, "sender": {"matches": ".*"}}}, "response": {"status": 200, "jsonBody": [{"id": "e7f45ed1-bada-4f3a-93fb-2e4f5a295ec5", "organizationId": "e7f45ed1-bada-4f3a-93fb-2e4f5a295ec5", "email": "<EMAIL>", "sender": "<EMAIL>", "invited": "<PERSON>", "role": "ADMIN", "groups": ["group1", "group2"], "organizationName": "Test Organization", "createdDate": "2023-01-01T12:00:00", "updateDate": "2023-01-01T12:00:00", "status": "PENDING", "notes": "Please join our organization", "sendCount": 1}], "headers": {"Content-Type": "application/json"}}}, {"name": "Verify Invitation", "request": {"method": "GET", "url": "/user/invitation/verify", "headers": {"x-organizationId": {"matches": "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}}, "queryParameters": {"token": {"matches": "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "email": {"matches": ".+@.+\\..+"}}}, "response": {"status": 200, "jsonBody": {"id": "e7f45ed1-bada-4f3a-93fb-2e4f5a295ec5", "organizationId": "e7f45ed1-bada-4f3a-93fb-2e4f5a295ec5", "email": "<EMAIL>", "sender": "<EMAIL>", "invited": "<PERSON>", "role": "ADMIN", "groups": ["group1", "group2"], "organizationName": "Test Organization", "createdDate": "2023-01-01T12:00:00", "updateDate": "2023-01-01T12:00:00", "status": "PENDING", "notes": "Please join our organization", "sendCount": 1}, "headers": {"Content-Type": "application/json"}}}, {"name": "Accept Invitation", "request": {"method": "POST", "url": "/user/invitation/accept", "headers": {"x-organizationId": {"matches": "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}}, "queryParameters": {"token": {"matches": "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "email": {"matches": ".+@.+\\..+"}}}, "response": {"status": 200, "jsonBody": {"id": "e7f45ed1-bada-4f3a-93fb-2e4f5a295ec5", "organizationId": "e7f45ed1-bada-4f3a-93fb-2e4f5a295ec5", "email": "<EMAIL>", "sender": "<EMAIL>", "invited": "<PERSON>", "role": "ADMIN", "groups": ["group1", "group2"], "organizationName": "Test Organization", "createdDate": "2023-01-01T12:00:00", "updateDate": "2023-01-01T12:00:00", "status": "ACCEPTED", "notes": "Please join our organization", "sendCount": 1}, "headers": {"Content-Type": "application/json"}}}, {"name": "Reject Invitation", "request": {"method": "POST", "url": "/user/invitation/reject", "headers": {"x-organizationId": {"matches": "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}}, "queryParameters": {"token": {"matches": "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "email": {"matches": ".+@.+\\..+"}}}, "response": {"status": 200, "jsonBody": {"id": "e7f45ed1-bada-4f3a-93fb-2e4f5a295ec5", "organizationId": "e7f45ed1-bada-4f3a-93fb-2e4f5a295ec5", "email": "<EMAIL>", "sender": "<EMAIL>", "invited": "<PERSON>", "role": "ADMIN", "groups": ["group1", "group2"], "organizationName": "Test Organization", "createdDate": "2023-01-01T12:00:00", "updateDate": "2023-01-01T12:00:00", "status": "REJECTED", "notes": "Please join our organization", "sendCount": 1}, "headers": {"Content-Type": "application/json"}}}, {"name": "Get Filtered Invitations", "request": {"method": "POST", "url": "/user/invitations/filtered", "headers": {"x-organizationId": {"matches": "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}}, "queryParameters": {"sortBy": {"equalTo": "CREATED_DATE"}, "pageNumber": {"equalTo": "1"}, "pageSize": {"equalTo": "10"}}}, "response": {"status": 200, "jsonBody": {"content": [], "pageable": {"pageNumber": 0, "pageSize": 10, "sort": {"unsorted": false, "sorted": true, "empty": false}, "offset": 0, "paged": true, "unpaged": false}, "totalElements": 1, "totalPages": 1, "last": true, "size": 10, "number": 0, "sort": {"unsorted": false, "sorted": true, "empty": false}, "numberOfElements": 1, "first": true, "empty": false}, "headers": {"Content-Type": "application/json"}}}, {"name": "Update Invitation", "request": {"method": "PUT", "url": "/user/invitation", "headers": {"x-organizationId": {"matches": "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}}}, "response": {"status": 200, "jsonBody": {"id": "e7f45ed1-bada-4f3a-93fb-2e4f5a295ec5", "organizationId": "e7f45ed1-bada-4f3a-93fb-2e4f5a295ec5", "email": "<EMAIL>", "sender": "<EMAIL>", "invited": "<PERSON>", "role": "MANAGER", "groups": ["group1", "group2", "group3"], "organizationName": "Test Organization", "createdDate": "2023-01-01T12:00:00", "updateDate": "2023-01-02T12:00:00", "status": "PENDING", "notes": "Updated notes", "sendCount": 1}, "headers": {"Content-Type": "application/json"}}}, {"name": "Latest Invitations Per User", "request": {"method": "POST", "url": "/user/invitation/latest-invitations", "headers": {"x-organizationId": {"matches": "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}}}, "response": {"status": 200, "jsonBody": [], "headers": {"Content-Type": "application/json"}}}]}