{"id": "FathomRealm", "realm": "FathomDevRealm", "displayName": "", "displayNameHtml": "", "notBefore": 1594644922, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 432000, "accessTokenLifespanForImplicitFlow": 432000, "ssoSessionIdleTimeout": 86400, "ssoSessionMaxLifespan": 86400, "ssoSessionIdleTimeoutRememberMe": 86400, "ssoSessionMaxLifespanRememberMe": 86400, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 7200, "clientSessionMaxLifespan": 86400, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 3600, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 3000, "actionTokenGeneratedByAdminLifespan": 1200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "none", "registrationAllowed": true, "registrationEmailAsUsername": true, "rememberMe": true, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": true, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxTemporaryLockouts": 0, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "2d7f8c67-ebba-4404-8f77-4116021f8ad6", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "FathomRealm", "attributes": {}}, {"id": "a45304bc-55a1-4390-b2a5-f135de8cdec1", "name": "default-roles-fathomrealm", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["uma_authorization", "user"], "client": {"realm-management": ["view-users", "manage-users"], "account": ["view-profile", "manage-account"]}}, "clientRole": false, "containerId": "FathomRealm", "attributes": {}}, {"id": "bdbdbbd6-0401-445a-ad13-ffcc289473cc", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "FathomRealm", "attributes": {}}, {"id": "55df2eb8-76b0-4a10-8dbd-96b6bfcba37a", "name": "user", "description": "Regular user", "composite": false, "clientRole": false, "containerId": "FathomRealm", "attributes": {}}], "client": {"realm-management": [{"id": "9ccba25d-fc06-4f46-bd46-1a510816c909", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "3dc73f71-a569-4211-b319-2eb720b8351a", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "9b603ccf-c734-406f-86c7-1b40c3e58ab0", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "799820e7-d614-43bb-9096-e5e722b037ff", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "41c0d307-9629-427f-9079-fb44bf8d51c8", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "1c45759b-0a19-4001-b21f-fc915a03dab1", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "b45ffa3c-5005-4dfb-81d0-c76d5952771f", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "9b42aad9-63b2-47d2-b047-27a13969db00", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "8fbcbc91-8d8a-41b3-9806-578d57b9c882", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "3738ccf5-cf02-46fe-a9d0-5a9f2256990b", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "ed326f10-3dd1-49f0-a766-533047b39c99", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "6ac48cfe-2659-4fc1-aba3-13f5fe53f9f9", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "8f9422bc-b07c-43de-8c9a-a15494013043", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "560b6bbc-bca9-42ab-8916-7525dc5f45e0", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "6a0c6d53-c4d5-44cd-8b74-8e9a975623c8", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["query-realms", "view-events", "manage-identity-providers", "manage-users", "view-clients", "view-authorization", "manage-authorization", "query-clients", "manage-clients", "query-users", "view-users", "impersonation", "view-realm", "view-identity-providers", "manage-realm", "create-client", "manage-events", "query-groups"]}}, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "25ad11fe-a373-40ff-bf32-67de9dfb5fb4", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "0ca5b980-158f-4a7d-a49c-0c2d5d298f4b", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "f1811edd-7efc-4f29-a357-24765d8132b3", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}, {"id": "0a0091aa-3eee-4609-bba8-333cc61c1e25", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "1d18009b-7d4b-4259-a2a0-8013c183e566", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "fathom-client": [{"id": "e1d8b4f2-2a7d-4101-acca-c079a8052bf4", "name": "user", "composite": false, "clientRole": true, "containerId": "4c01cd81-1870-4293-9164-43b7bca0f66a", "attributes": {}}], "account-console": [], "broker": [{"id": "bbf1cac8-8be0-43f2-a635-b74223c01fe9", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "ee47a05d-845b-47d5-a68b-81485a7d1e80", "attributes": {}}], "account": [{"id": "5eca941f-d19c-4867-9a6a-858060bc2cae", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "5c61d647-8944-42ce-8790-dbf010570713", "attributes": {}}, {"id": "5796c98e-d84f-4381-92c8-c634af91b597", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "5c61d647-8944-42ce-8790-dbf010570713", "attributes": {}}, {"id": "0af1dd07-a0ec-47d1-9231-bde4f095bcb8", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "5c61d647-8944-42ce-8790-dbf010570713", "attributes": {}}, {"id": "c17040a5-7fa3-41d3-9989-5086b1d5d1e1", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "5c61d647-8944-42ce-8790-dbf010570713", "attributes": {}}, {"id": "2997e953-b0a3-434a-8087-85d654606e9f", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "5c61d647-8944-42ce-8790-dbf010570713", "attributes": {}}, {"id": "e19f3788-c966-4a52-a7b8-656664c249c6", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "5c61d647-8944-42ce-8790-dbf010570713", "attributes": {}}, {"id": "055276b1-d2aa-419f-aeb4-5810118565b6", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "5c61d647-8944-42ce-8790-dbf010570713", "attributes": {}}, {"id": "b6d5d495-937c-40f6-b691-55b381735593", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "5c61d647-8944-42ce-8790-dbf010570713", "attributes": {}}]}}, "groups": [], "defaultRole": {"id": "a45304bc-55a1-4390-b2a5-f135de8cdec1", "name": "default-roles-fathomrealm", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "FathomRealm"}, "requiredCredentials": ["password"], "passwordPolicy": "length(8) and upperCase(1) and lowerCase(1) and digits(1) and specialChars(1)", "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "users": [{"id": "e044c90c-645f-4ef0-a71f-ba42bb5a3de9", "username": "service-account-admin-cli", "emailVerified": false, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "admin-cli", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-fathomrealm"], "clientRoles": {"realm-management": ["view-users", "view-identity-providers", "manage-users"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"client": "admin-cli", "roles": ["user"]}, {"clientScope": "profile", "roles": ["uma_authorization", "user"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "5c61d647-8944-42ce-8790-dbf010570713", "clientId": "account", "name": "${client_account}", "description": "", "rootUrl": "${authBaseUrl}", "adminUrl": "", "baseUrl": "/realms/FathomDevRealm/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/realms/FathomDevRealm/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "login_theme": "platform", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "saml.server.signature": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "oidc.ciba.grant.enabled": "false", "backchannel.logout.session.required": "true", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["basic"], "optionalClientScopes": []}, {"id": "1df44558-f0b2-4ef7-8561-45015563cdd5", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/FathomRealm/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["/realms/FathomRealm/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "01c34cf2-ef78-4a8d-b559-79ba4faf3cdf", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["basic"], "optionalClientScopes": []}, {"id": "2793cd73-6ee0-40a3-a5d1-533d0b3cb179", "clientId": "admin-cli", "name": "admin", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "/realms/FathomDevRealm/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "supersecret", "redirectUris": ["https://localhost:4200/*"], "webOrigins": ["https://platform.dev.fthm.io", "http://localhost:4200"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "client.secret.creation.time": "**********", "saml.multivalued.roles": "false", "saml.force.post.binding": "false", "saml.encrypt": "false", "login_theme": "platform", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "true", "saml.server.signature": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature.keyinfo.ext": "false", "use.refresh.tokens": "true", "exclude.session.state.from.auth.response": "false", "oidc.ciba.grant.enabled": "false", "saml.artifact.binding": "false", "backchannel.logout.session.required": "false", "client_credentials.use_refresh_token": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "5be3bd19-6880-40a8-9c6b-3da7048d4632", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "9ba663d2-1f62-4bb5-8971-28afaa972245", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "99d269f7-dbea-46d5-82d9-89a8ecbc084c", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "6ef950f2-7ab4-4c19-92c0-f2a4e51879f5", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "d02b6df8-2a80-4145-8cc2-a15249b17060", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "CreatedAtAndUserIdScope", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "ee47a05d-845b-47d5-a68b-81485a7d1e80", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "4c01cd81-1870-4293-9164-43b7bca0f66a", "clientId": "fathom-client", "name": "admin", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "/realms/FathomDevRealm/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["https://digitaltwin.dev.fthm.io/*", "https://theme.dev.fthm.io/*", "https://applicationbuilder.dev.fthm.io/*", "https://intelligence.dev.fthm.io/*", "https://api.dev.fthm.io/*", "https://platform.dev.fthm.io/*", "http://localhost:4200/*", "https://e05b-2a02-ce0-3802-56e3-bc83-c206-b694-e46c.ngrok-free.app/*", "https://analyticsops.dev.fthm.io/*", "https://auth.fthm.io/*", "https://dataops.dev.fthm.io/*"], "webOrigins": ["https://intelligence.dev.fthm.io", "https://digitaltwin.dev.fthm.io", "https://applicationbuilder.dev.fthm.io", "https://dataops.dev.fthm.io", "https://platform.dev.fthm.io", "https://api.dev.fthm.io", "http://localhost:4200", "https://e05b-2a02-ce0-3802-56e3-bc83-c206-b694-e46c.ngrok-free.app"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": true, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "client.secret.creation.time": "**********", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "login_theme": "platform", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "saml.server.signature": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature.keyinfo.ext": "false", "use.refresh.tokens": "true", "exclude.session.state.from.auth.response": "false", "oidc.ciba.grant.enabled": "false", "saml.artifact.binding": "false", "backchannel.logout.session.required": "false", "client_credentials.use_refresh_token": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "492826bb-0d10-41bb-ae15-ccbff7d33373", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String", "access.tokenResponse.claim": "false"}}, {"id": "cbb3c3a8-6099-4227-a122-51ea29b8c951", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String", "access.tokenResponse.claim": "false"}}, {"id": "3bc8884d-8d79-4499-8ac5-313e5b88f292", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String", "access.tokenResponse.claim": "false"}}], "defaultClientScopes": ["web-origins", "address", "CreatedAtAndUserIdScope", "phone", "profile", "roles", "microprofile-jwt", "basic", "email"], "optionalClientScopes": []}, {"id": "1d18009b-7d4b-4259-a2a0-8013c183e566", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "fc9d322d-41c0-4849-abe3-2e8c55c8ef15", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/FathomRealm/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/FathomRealm/console/*"], "webOrigins": ["*", "+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "saml.server.signature": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature.keyinfo.ext": "false", "use.refresh.tokens": "true", "exclude.session.state.from.auth.response": "false", "oidc.ciba.grant.enabled": "false", "saml.artifact.binding": "false", "backchannel.logout.session.required": "false", "client_credentials.use_refresh_token": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "pkce.code.challenge.method": "S256", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "ed224c30-d356-4ccb-8e2a-141f2c84fbce", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String", "userinfo.token.claim": "true"}}], "defaultClientScopes": ["web-origins", "CreatedAtAndUserIdScope", "profile", "roles", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "b74e9fd3-6390-410e-923c-5609e0c642ea", "name": "basic", "description": "OpenID Connect scope for add all basic claims to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "b31371ff-4a76-4684-8842-f2557e51e212", "name": "auth_time", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "AUTH_TIME", "id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "auth_time", "jsonType.label": "long"}}, {"id": "500d2dce-ebc8-49a5-ac3a-5a0b7f90c671", "name": "sub", "protocol": "openid-connect", "protocolMapper": "oidc-sub-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "1ad15a31-0de3-4537-b239-81e37949cdfd", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "22d338e8-b877-45f9-ab8e-6c60b706cec5", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "7cb88969-409d-494d-9eac-07c1665dab9a", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "aab04a2f-7429-4c5d-89e4-c040095b8719", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "false"}}, {"id": "245c949f-b77f-415f-a222-332ad268ed8d", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${phoneScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "7dc18053-31cb-47a6-b0ad-52f464f18283", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean", "userinfo.token.claim": "true"}}, {"id": "568568aa-4ced-41a8-aac5-7e1474506b3a", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String", "userinfo.token.claim": "true"}}]}, {"id": "fe47a50e-0fd2-4b30-98e1-6debe10f321b", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false", "gui.order": "", "consent.screen.text": ""}, "protocolMappers": [{"id": "4e8aeb41-7a55-4128-8255-90cf47ea2211", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "locale", "id.token.claim": "false", "access.token.claim": "false", "claim.name": "locale", "jsonType.label": "String", "userinfo.token.claim": "false"}}, {"id": "d830f25a-bb67-4004-986e-8d99a6e89680", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "birthdate", "id.token.claim": "false", "access.token.claim": "false", "claim.name": "birthdate", "jsonType.label": "String", "userinfo.token.claim": "false"}}, {"id": "a1a8eb03-c3f2-446f-bd92-2f1e43420520", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "first_name", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "41449659-5cc0-41a1-a704-128e0582569e", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"user.attribute": "username", "id.token.claim": "false", "access.token.claim": "false", "claim.name": "preferred_username", "jsonType.label": "String", "userinfo.token.claim": "false"}}, {"id": "1f9deb9d-22ac-4b32-97ca-1f7fcd6d34be", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "picture", "id.token.claim": "false", "access.token.claim": "false", "claim.name": "picture", "jsonType.label": "String", "userinfo.token.claim": "false"}}, {"id": "5fcf7c2e-5e8f-4194-9566-f290d2da0d88", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "63deff96-17e9-4194-a407-c1cb09b7bc70", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "gender", "id.token.claim": "false", "access.token.claim": "false", "claim.name": "gender", "jsonType.label": "String", "userinfo.token.claim": "false"}}, {"id": "668189a8-1b46-4f42-9a12-159f98ff3d6f", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "77ebcb2f-f5c1-4e77-8ea7-45ab3a65d456", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "844c4e27-bd16-49b7-b4b1-c145b8947467", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "last_name", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "e9afd0dc-1716-4e2c-b80c-8b421db875ed", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "zoneinfo", "id.token.claim": "false", "access.token.claim": "false", "claim.name": "zoneinfo", "jsonType.label": "String", "userinfo.token.claim": "false"}}, {"id": "9baf41bf-4a5f-4fc7-876f-f6094ee3a366", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "false", "access.token.claim": "false", "userinfo.token.claim": "false"}}, {"id": "19978841-1526-46b3-a3db-ff8f65019eae", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "nickname", "id.token.claim": "false", "access.token.claim": "false", "claim.name": "nickname", "jsonType.label": "String", "userinfo.token.claim": "false"}}, {"id": "a7bde5f4-db79-4ef0-b07f-3bee9c64bd7b", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "profile", "id.token.claim": "false", "access.token.claim": "false", "claim.name": "profile", "jsonType.label": "String", "userinfo.token.claim": "false"}}]}, {"id": "185ebb56-b2dc-4886-b671-3749c0f8af00", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "0effddcd-616c-4c9c-8974-eb8d7f468906", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "c1f1198d-d4f8-4b03-a186-0a9a46d81cb1", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "91cb32a0-d625-457b-9dab-f5be2ea5be36", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}, {"id": "9dc787a6-6027-4779-ad71-a52eac80044e", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "${addressScopeConsentText}", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "fbe9cc11-9dec-4b41-80e2-4ebf08ef22c2", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "e0729c75-8cb6-45a5-a363-b61b3dfd2aee", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${rolesScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "025b1e42-0ff2-46f7-b026-8e9c180c7fb5", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String"}}, {"id": "94a1b836-60c1-4ad6-8aa5-2c54b5b0dabd", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "28a9cd73-18ba-4b40-8439-9e0df7a4c5f9", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "29554d39-e538-45cc-952a-211229ff5ded", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${emailScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "f06cab45-d888-4dbd-87e8-41dbe8679f66", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean", "userinfo.token.claim": "true"}}, {"id": "9ec61212-509c-479a-8a78-7e83f6e95cad", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String", "userinfo.token.claim": "true"}}]}, {"id": "610a8bb4-87c3-43a1-8144-87f3a4f20263", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "69398cbc-ce17-44b9-a7da-622a3b57ce63", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}]}, {"id": "167e3d13-7bef-4478-92ca-6738277264be", "name": "CreatedAtAndUserIdScope", "description": "Adds default mappers to the client", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "12c4ac30-16ae-4824-9694-cf32bc779c3c", "name": "user_id", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"user.attribute": "id", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "user_id", "jsonType.label": "String", "userinfo.token.claim": "true"}}, {"id": "c9a42759-dab7-4b06-ab53-8f61861f0d90", "name": "createdAt", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"user.attribute": "createdTimestamp", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "createdAt", "jsonType.label": "long", "userinfo.token.claim": "true"}}]}], "defaultDefaultClientScopes": ["profile", "email", "roles", "CreatedAtAndUserIdScope", "acr", "basic"], "defaultOptionalClientScopes": [], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "referrerPolicy": "no-referrer", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=********; includeSubDomains"}, "smtpServer": {"password": "**********", "starttls": "true", "auth": "true", "port": "465", "host": "smtp.gmail.com", "replyTo": "<EMAIL>", "from": "<EMAIL>", "fromDisplayName": "Fathom support team", "ssl": "true", "user": "<EMAIL>"}, "loginTheme": "platform", "accountTheme": "keycloak.v3", "adminTheme": "keycloak.v2", "emailTheme": "keycloak", "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": ["SEND_RESET_PASSWORD", "UPDATE_CONSENT_ERROR", "GRANT_CONSENT", "REMOVE_TOTP", "REVOKE_GRANT", "UPDATE_TOTP", "LOGIN_ERROR", "CLIENT_LOGIN", "RESET_PASSWORD_ERROR", "IMPERSONATE_ERROR", "CODE_TO_TOKEN_ERROR", "CUSTOM_REQUIRED_ACTION", "RESTART_AUTHENTICATION", "IMPERSONATE", "UPDATE_PROFILE_ERROR", "LOGIN", "UPDATE_PASSWORD_ERROR", "CLIENT_INITIATED_ACCOUNT_LINKING", "TOKEN_EXCHANGE", "LOGOUT", "REGISTER", "CLIENT_REGISTER", "IDENTITY_PROVIDER_LINK_ACCOUNT", "UPDATE_PASSWORD", "CLIENT_DELETE", "FEDERATED_IDENTITY_LINK_ERROR", "IDENTITY_PROVIDER_FIRST_LOGIN", "CLIENT_DELETE_ERROR", "VERIFY_EMAIL", "CLIENT_LOGIN_ERROR", "RESTART_AUTHENTICATION_ERROR", "EXECUTE_ACTIONS", "REMOVE_FEDERATED_IDENTITY_ERROR", "TOKEN_EXCHANGE_ERROR", "PERMISSION_TOKEN", "SEND_IDENTITY_PROVIDER_LINK_ERROR", "EXECUTE_ACTION_TOKEN_ERROR", "SEND_VERIFY_EMAIL", "EXECUTE_ACTIONS_ERROR", "REMOVE_FEDERATED_IDENTITY", "IDENTITY_PROVIDER_POST_LOGIN", "IDENTITY_PROVIDER_LINK_ACCOUNT_ERROR", "UPDATE_EMAIL", "REGISTER_ERROR", "REVOKE_GRANT_ERROR", "EXECUTE_ACTION_TOKEN", "LOGOUT_ERROR", "UPDATE_EMAIL_ERROR", "CLIENT_UPDATE_ERROR", "UPDATE_PROFILE", "CLIENT_REGISTER_ERROR", "FEDERATED_IDENTITY_LINK", "SEND_IDENTITY_PROVIDER_LINK", "SEND_VERIFY_EMAIL_ERROR", "RESET_PASSWORD", "CLIENT_INITIATED_ACCOUNT_LINKING_ERROR", "UPDATE_CONSENT", "REMOVE_TOTP_ERROR", "VERIFY_EMAIL_ERROR", "SEND_RESET_PASSWORD_ERROR", "CLIENT_UPDATE", "CUSTOM_REQUIRED_ACTION_ERROR", "IDENTITY_PROVIDER_POST_LOGIN_ERROR", "UPDATE_TOTP_ERROR", "CODE_TO_TOKEN", "GRANT_CONSENT_ERROR", "IDENTITY_PROVIDER_FIRST_LOGIN_ERROR"], "adminEventsEnabled": false, "adminEventsDetailsEnabled": true, "identityProviders": [{"alias": "google", "internalId": "b3a796aa-e3e8-4da4-a328-2c996c0b4b0a", "providerId": "google", "enabled": true, "updateProfileFirstLoginMode": "on", "trustEmail": true, "storeToken": true, "addReadTokenRoleOnCreate": false, "authenticateByDefault": false, "linkOnly": false, "firstBrokerLoginFlowAlias": "first broker login", "config": {"offlineAccess": "true", "hideOnLoginPage": "false", "clientId": "***********-n9ulpkravcp0248btdmh8jb704kaer8p.apps.googleusercontent.com", "acceptsPromptNoneForwardFromClient": "false", "disableUserInfo": "false", "filteredByClaim": "false", "hostedDomain": "*", "syncMode": "IMPORT", "clientSecret": "**********", "guiOrder": "1", "useJwksUrl": "true"}}], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "2dc1a223-d328-4dc7-bc35-c564507a273c", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "0285032c-fed8-485d-ac74-737abd2ac21a", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "oidc-full-name-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "oidc-usermodel-property-mapper", "saml-user-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper"]}}, {"id": "5f50f2ed-0229-4b70-b05f-bf6b3474a61a", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "oidc-full-name-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-property-mapper", "oidc-usermodel-attribute-mapper"]}}, {"id": "70bb04d2-fd3d-4f78-bc7b-bedabd7cf33b", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "916c77af-2b29-46c0-8d28-50db48bb28a7", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "19818a7a-3ae5-4762-bf90-63caddca4ca4", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "ef8ada49-c4d0-4fc3-861e-796617a4c88e", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "e0e0f315-fdb9-4696-bcbb-35517effac21", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}], "org.keycloak.userprofile.UserProfileProvider": [{"id": "048d232a-aef5-4a43-a9dc-33f0369235aa", "providerId": "declarative-user-profile", "subComponents": {}, "config": {"kc.user.profile.config": ["{\"attributes\":[{\"name\":\"username\",\"displayName\":\"${username}\",\"validations\":{\"length\":{\"min\":3,\"max\":255},\"username-prohibited-characters\":{},\"up-username-not-idn-homograph\":{}},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"email\",\"displayName\":\"${email}\",\"validations\":{\"email\":{},\"length\":{\"max\":255}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"firstName\",\"displayName\":\"${firstName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"lastName\",\"displayName\":\"${lastName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false}],\"groups\":[{\"name\":\"user-metadata\",\"displayHeader\":\"User metadata\",\"displayDescription\":\"Attributes, which refer to user metadata\"}],\"unmanagedAttributePolicy\":\"ENABLED\"}"]}}], "org.keycloak.keys.KeyProvider": [{"id": "27c1ea12-64a2-416f-8cb3-50b6d5406195", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"keySize": ["2048"], "active": ["true"], "priority": ["110"], "enabled": ["true"], "algorithm": ["RS256"]}}, {"id": "6851f864-7794-4a47-9a47-c02f6598d79f", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "a0c6d951-c952-4e4f-b64b-a36ff49e1969", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS512"]}}, {"id": "28a6185d-f857-486b-a6b8-d80380b56701", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS256"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "defaultLocale": "", "authenticationFlows": [{"id": "43c607ce-7b63-4b36-984f-f49a8bdaa9e9", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "d9436e0e-65c4-49fd-adb0-3de8716400d3", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "a614fe5d-8023-4c2d-a308-2d0f5f3d706d", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "3531d2ff-c954-4c64-85f9-2bb52fe86f99", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "2ad36a46-ae5f-40c0-93bf-a3b5c19cc827", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "1d8ca767-4f4b-484b-aff2-132ef82ec93e", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "7963fbf4-ff67-4bea-9c86-52fc655ba073", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "040f22ef-430a-4420-855f-5f3c1e7a3fed", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "86a6638a-99b3-4d33-b9dc-d178037f6567", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "047e9afa-e514-4ec6-a3cd-a1ddb349d997", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "a4434097-c82e-4601-b380-9e45e643b106", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "f1f037c4-5716-4fda-84b0-c9146f8f9879", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "094ccc7e-895e-40d6-bafe-905191c11ef6", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "03b5fe03-5b64-46d6-8b2a-5d1cd76b3691", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "c8ce13e8-fb07-4457-ba8b-2ea0c3e8c6b6", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "140558d8-7423-47fd-a448-92a5bae591cb", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "84c30624-c1a1-4170-93ce-4c4ff518233b", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "fd9b1091-fbd0-47b6-a638-fd7555d5ffbd", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "8078d8eb-dea5-46e9-bddf-bcf9eebdb624", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "b2fe92f6-9411-4247-8e25-ed0a9727ddc5", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": false, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "delete_credential", "name": "Delete Credential", "providerId": "delete_credential", "enabled": true, "defaultAction": false, "priority": 100, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "firstBrokerLoginFlow": "first broker login", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "oauth2DevicePollingInterval": "5", "clientSessionIdleTimeout": "7200", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "86400", "cibaInterval": "5", "realmReusableOtpCode": "false"}, "keycloakVersion": "25.0.6", "userManagedAccessAllowed": false, "organizationsEnabled": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}