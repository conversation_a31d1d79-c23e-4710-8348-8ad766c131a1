package com.fathom.services.usermanagement.services.impl;

import com.fathom.services.usermanagement.model.dto.AuthorizeDto;
import com.fathom.services.usermanagement.services.AuthorizeService;
import com.fathom.services.usermanagement.services.GroupService;
import com.fathom.services.usermanagement.services.UserManagerService;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class AuthorizeServiceImpl implements AuthorizeService {
  private final UserManagerService userManagerService;
  private final GroupService groupService;

  @Override
  public boolean authorize(
      UUID organizationId, String projectId, String email, AuthorizeDto authorizeDto) {

    if (userManagerService.isAdminWithinOrganization(organizationId, email)) {
      return true;
    }

    return groupService.isHasTheRightAccessPolicy(organizationId, projectId, email, authorizeDto);
  }
}
