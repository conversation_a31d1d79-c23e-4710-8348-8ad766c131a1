package com.fathom.services.usermanagement.services.impl;

import static com.fathom.services.usermanagement.util.ExMessages.GROUP_WITH_ID_DOES_NOT_EXIST;
import static com.fathom.services.usermanagement.util.ExMessages.POLICY_WITH_ID_DOES_NOT_EXIST;

import com.fathom.lib.common.exception.RestException;
import com.fathom.services.usermanagement.feign.*;
import com.fathom.services.usermanagement.mapper.GroupMapper;
import com.fathom.services.usermanagement.mapper.PolicyMapper;
import com.fathom.services.usermanagement.model.Group;
import com.fathom.services.usermanagement.model.Policy;
import com.fathom.services.usermanagement.model.User;
import com.fathom.services.usermanagement.model.dto.*;
import com.fathom.services.usermanagement.model.dto.feign.Endpoint;
import com.fathom.services.usermanagement.model.dto.feign.ServiceInformationDto;
import com.fathom.services.usermanagement.model.enums.AccessTypeEnum;
import com.fathom.services.usermanagement.repositories.GroupRepository;
import com.fathom.services.usermanagement.repositories.PolicyRepository;
import com.fathom.services.usermanagement.services.GroupService;
import com.fathom.services.usermanagement.services.UserManagerService;
import jakarta.transaction.Transactional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class GroupServiceImpl implements GroupService {

  private final GroupRepository groupRepository;
  private final PolicyRepository policyRepository;
  private final GroupMapper groupMapper;
  private final PolicyMapper policyMapper;
  private final UserManagerService userManagerService;
  private final DirectorClient directorClient;

  @Override
  @Transactional
  public Page<GroupDto> getGroupsByOrganizationIdAndProjectId(
      UUID organizationId, int pageNumber, int pageSize, boolean pageable) {


    Page<Group> groupPage =
        groupRepository.findByOrganizationId(
            organizationId, PageRequest.of(pageNumber - 1, pageSize));

    List<GroupDto> groupDtoList =
        groupPage.stream().map(groupMapper::toDto).toList();

    return
        new PageImpl<>(
            groupDtoList, PageRequest.of(pageNumber , pageSize), groupPage.getTotalElements());

  }

  @Override
  @Transactional
  public List<GroupDto> createGroups(
      UUID organizationId, String email, List<GroupCreateUpdateDto> groupCreateUpdateDtoList) {

    userManagerService.checkIsUserExistAndAuthorized(organizationId, email);

    List<Group> groups =
        groupCreateUpdateDtoList.stream()
            .map(x -> groupMapper.toEntity(organizationId, email, x))
            .toList();
    groups = groupRepository.saveAll(groups);

    return groups.stream().map(groupMapper::toDto).toList();
  }

  @Override
  @Transactional
  public GroupDto overrideMembersInGroup(
      UUID organizationId, String email, UUID groupId, AddMembersToGroupDto addMembersToGroupDto) {

    userManagerService.checkIsUserExistAndAuthorized(organizationId, email);

    Group group = getGroup(groupId);
    Set<User> users = validateAndFetchUsers(organizationId, addMembersToGroupDto);
    group.setUsers(users);

    return groupMapper.toDto(groupRepository.save(group));
  }

  @Override
  @Transactional
  public GroupDto addMembersInGroup(
      UUID organizationId, String email, UUID groupId, AddMembersToGroupDto addMembersToGroupDto) {
    userManagerService.checkIsUserExistAndAuthorized(organizationId, email);

    Group group = getGroup(groupId);
    Set<User> users = validateAndFetchUsers(organizationId, addMembersToGroupDto);
    group.getUsers().addAll(users);

    return groupMapper.toDto(groupRepository.save(group));
  }

  @Override
  @Transactional
  public GroupDto deleteMembersInGroup(
      UUID organizationId, String email, UUID groupId, AddMembersToGroupDto addMembersToGroupDto) {
    userManagerService.checkIsUserExistAndAuthorized(organizationId, email);

    Group group = getGroup(groupId);
    Set<User> users = validateAndFetchUsers(organizationId, addMembersToGroupDto);
    group.setUsers(users);
    group.getUsers().removeAll(users);

    return groupMapper.toDto(groupRepository.save(group));
  }

  @Override
  @Transactional
  public GroupDto addPoliciesToGroup(
      UUID organizationId,
      String email,
      UUID groupId,
      AddPoliciesToGroupDto addPoliciesToGroupDto) {

    userManagerService.checkIsUserExistAndAuthorized(organizationId, email);
    Group group = getGroup(groupId);

    Set<Policy> policies =
        policyRepository.findByOrganizationIdAndNameIn(
            organizationId, addPoliciesToGroupDto.getPolicyNames());
    group.setPolicies(policies);

    return groupMapper.toDto(groupRepository.save(group));
  }

  @Override
  @Transactional
  public GroupDto updateGroup(
      UUID organizationId, String email, UUID groupId, GroupCreateUpdateDto groupCreateUpdateDto) {

    userManagerService.checkIsUserExistAndAuthorized(organizationId, email);
    Group group = groupMapper.updateDtoToEntity(getGroup(groupId), groupCreateUpdateDto);
    return groupMapper.toDto(groupRepository.save(group));
  }

  @Override
  @Transactional
  public void deleteGroup(UUID organizationId, String email, UUID groupId) {
    userManagerService.checkIsUserExistAndAuthorized(organizationId, email);
    Group group = getGroup(groupId);
    groupRepository.delete(group);
  }

  @Override
  @Transactional
  public void deleteGroupsByIds(UUID organizationId, String email, Set<UUID> groupIds) {
    userManagerService.checkIsUserExistAndAuthorized(organizationId, email);
    groupRepository.deleteByOrganizationIdAndIdIn(organizationId, groupIds);
  }

  @Override
  public List<GroupDto> addManyUsersToManyGroups(
      UUID organizationId, String email, UserEmailsGroupsDto userEmailsGroupsDto) {

    List<Group> groups =
        groupRepository.findByOrganizationIdAndIdInOrNameIn(
            organizationId, userEmailsGroupsDto.getGroupIds(), userEmailsGroupsDto.getGroupNames());

    Set<User> users = userManagerService.findByEmailIn(userEmailsGroupsDto.getMemberEmails());
    groups.forEach(x -> x.getUsers().addAll(users));

    return groupRepository.saveAll(groups).stream()
        .map(groupMapper::toDto)
        .toList();
  }

  @Override
  @Transactional
  public List<GroupDto> deleteManyUsersFromGroups(
      UUID organizationId, String email, UserEmailsGroupsDto userEmailsGroupsDto) {

    List<Group> groups =
        groupRepository.findByOrganizationIdAndIdInOrNameIn(
            organizationId, userEmailsGroupsDto.getGroupIds(), userEmailsGroupsDto.getGroupNames());

    groups.forEach(
        x ->
            x.getUsers()
                .removeIf(user -> userEmailsGroupsDto.getMemberEmails().contains(user.getEmail())));

    return groupRepository.saveAll(groups).stream()
        .map(groupMapper::toDto)
        .toList();
  }

  @Override
  public List<GroupDto> updateManyUsersInManyGroups(
      UUID organizationId, String email, UserEmailsGroupsDto userEmailsGroupsDto) {

    List<Group> groups =
        groupRepository.findByOrganizationIdAndIdInOrNameIn(
            organizationId, userEmailsGroupsDto.getGroupIds(), userEmailsGroupsDto.getGroupNames());

    Set<User> users = userManagerService.findByEmailIn(userEmailsGroupsDto.getMemberEmails());
    groups.forEach(x -> x.setUsers(users));

    return groupRepository.saveAll(groups).stream()
        .map(groupMapper::toDto)
        .toList();
  }

  @Override
  @Transactional
  public List<PolicyDto> createPolicies(
      UUID organizationId, String email, List<PolicyCreateUpdateDto> policyCreateUpdateDtoList) {

    userManagerService.checkIsUserExistAndAuthorized(organizationId, email);

    LocalDate now = LocalDate.now(ZoneId.of("UTC"));
    List<String> invalidPoliciesNames = new ArrayList<>();

    for (PolicyCreateUpdateDto policyCreateUpdateDto : policyCreateUpdateDtoList) {
      if (policyCreateUpdateDto.getActivationDate().isBefore(now)
          || policyCreateUpdateDto.getDeactivationDate().isBefore(now)
              || policyCreateUpdateDto.getActivationDate().isAfter(policyCreateUpdateDto.getDeactivationDate())) {
        invalidPoliciesNames.add(policyCreateUpdateDto.getName());
      }
    }

    if (!invalidPoliciesNames.isEmpty()) {
      throw new IllegalArgumentException(
          "Policies with names: %s contains invalid activationDate and/or deactivationDate dates. Dates cannot be less than: %s and activationDate cannot be greater that deactivationDate"
              .formatted(
                  invalidPoliciesNames.stream().collect(Collectors.joining(", ", "[", "]")), now));
    }

    Set<String> existingPoliciesNames =
        policyRepository.findPolicyNamesByOrganizationIdAndNameIn(
            organizationId,
            policyCreateUpdateDtoList.stream()
                .map(PolicyCreateUpdateDto::getName)
                .collect(Collectors.toSet()));

    if (!existingPoliciesNames.isEmpty()) {
      throw new IllegalArgumentException(
          "Policies with names: %s already exist in organization %s. Policy name should be unique within an organization"
              .formatted(
                  existingPoliciesNames.stream().collect(Collectors.joining(", ", "[", "]")),
                  organizationId));
    }

    List<Policy> policies =
        policyCreateUpdateDtoList.stream()
            .map(x -> policyMapper.toEntity(organizationId, email, x))
            .toList();
    policies = policyRepository.saveAll(policies);
    return policies.stream().map(policyMapper::toDto).toList();
  }

  @Override
  public Page<PolicyDto> getPoliciesByOrganizationId(
      UUID organizationId, int pageNumber, int pageSize, boolean pageable) {

    Page<Policy> policyPage =
        policyRepository.findByOrganizationId(
            organizationId, PageRequest.of(pageNumber - 1, pageSize));

    List<PolicyDto> policyDtosList =
        policyPage.stream().map(policyMapper::toDto).toList();

    return new PageImpl<>(
        policyDtosList, PageRequest.of(pageNumber - 1, pageSize), policyPage.getTotalElements());
  }

  @Override
  @Transactional
  public PolicyDto updatePolicy(
      UUID organizationId,
      String email,
      UUID policyId,
      PolicyCreateUpdateDto policyCreateUpdateDto) {

    userManagerService.checkIsUserExistAndAuthorized(organizationId, email);

    Policy policy = policyMapper.updateDtoToEntity(getPolicy(policyId), policyCreateUpdateDto);
    return policyMapper.toDto(policyRepository.save(policy));
  }

  @Override
  @Transactional
  public void deletePolicy(UUID organizationId, String email, UUID policyId) {
    userManagerService.checkIsUserExistAndAuthorized(organizationId, email);
    policyRepository.deleteById(policyId);
  }

  @Override
  @Transactional
  public void deletePolicies(UUID organizationId, String email, Set<UUID> policyIds) {
    userManagerService.checkIsUserExistAndAuthorized(organizationId, email);
    policyRepository.deleteByOrganizationIdAndIdIn(organizationId, policyIds);
  }

  @Override
  @Transactional
  public boolean isHasTheRightAccessPolicy(
      UUID organizationId, String projectId, String email, AuthorizeDto authorizeDto) {

    ServiceInformationDto servicesInformation =
        directorClient.getServicesInformation(authorizeDto.getServiceName());

    log.info(
        "Received information about {} service from the director service",
        servicesInformation.getServiceName());

    if (servicesInformation.getScope().equals("organization")) {
      return isAuthorizedWithinOrganizationScoop(authorizeDto, servicesInformation);
    }

    return isAuthorizedWithinProjectScoop(
        organizationId, projectId, email, authorizeDto, servicesInformation);
  }

  @Override
  public GroupDto getGroupByOrganizationIdAndName(UUID organizationId, String name) {

    return groupMapper.toDto(groupRepository.findByOrganizationIdAndName(organizationId, name));
  }

  @Override
  public List<GroupDto> getGroupsByOrganizationIdAndNames(UUID organizationId, Set<String> name) {
    return groupRepository.findByOrganizationIdAndNameIn(organizationId, name).stream().map(groupMapper::toDto).toList();
  }

  private boolean isAuthorizedWithinOrganizationScoop(
      AuthorizeDto authorizeDto, ServiceInformationDto servicesInformation) {

    if (servicesInformation.getServiceName().equals("notifications")
        || servicesInformation.getServiceName().equals("userprofile")) {
      return true;
    }

    Endpoint endpoint = getMatchedEndpoint(servicesInformation, authorizeDto);

    if (Objects.isNull(endpoint)) {
      log.warn(
          "Could not find a endpoint definition in directory service {}, endpoint {}, method {} => unauthorized request",
          authorizeDto.getServiceName(),
          authorizeDto.getEndPoint(),
          authorizeDto.getMethod());
      return false;
    }

    return endpoint.getFunction().equals("READ");
  }

  private boolean isAuthorizedWithinProjectScoop(
      UUID organizationId,
      String projectId,
      String email,
      AuthorizeDto authorizeDto,
      ServiceInformationDto servicesInformation) {

    log.info(
        "Starting to check if user: {} of role member in organizationId: {} and projectId {} has the valid access policy to access endpoint for project scoop {}",
        email,
        organizationId,
        projectId,
        authorizeDto.getEndPoint());

    LocalDateTime now = LocalDateTime.now(ZoneId.of("UTC"));

    List<Group> groups =
        groupRepository
            .findByOrganizationIdAndUsers_EmailAndPolicies_ActivationDateLessThanEqualAndPolicies_DeactivationDateGreaterThan(
                organizationId, email, now, now);

    Set<Policy> policies =
        groups.stream().map(Group::getPolicies).flatMap(Set::stream).collect(Collectors.toSet());

    policies =
        policies.stream()
            .filter(x -> x.getWorkspaces().contains(projectId))
            .collect(Collectors.toSet());

    if (policies.isEmpty()) {
      log.info(
          "User with email: {} and role [member] for organization id {} and project id {} has no policies within the parameter of active and deactivate date",
          email,
          organizationId,
          projectId);
      return false;
    }

    Set<String> accessTypeEnums =
        policies.stream()
            .map(Policy::getAccessTypes)
            .flatMap(Set::stream)
            .map(AccessTypeEnum::getValue)
            .collect(Collectors.toSet());

    Endpoint endpoint = getMatchedEndpoint(servicesInformation, authorizeDto);

    if (Objects.isNull(endpoint)) {
      log.warn(
          "Could not find a endpoint definition in directory service {}, endpoint {}, method {} => unauthorized request",
          authorizeDto.getServiceName(),
          authorizeDto.getEndPoint(),
          authorizeDto.getMethod());
      return false;
    }

    Set<String> scoops =
        policies.stream().map(Policy::getFeatures).flatMap(Set::stream).collect(Collectors.toSet());

    if (!scoops.contains(servicesInformation.getScope())) {

      log.info(
          "[Unauthorized request]: Feature types for the user with email: {} for the given parameters are: {} while request feature type is: {}",
          email,
          String.join(", ", scoops),
          servicesInformation.getScope());

      return false;
    }

    if (!accessTypeEnums.contains(getAccessTypeFromFunction(endpoint.getFunction()))) {
      log.info(
          "[Unauthorized request]: Access types for the user with email: {} are: {} while required request access type is: {}",
          email,
          String.join(", ", accessTypeEnums),
          getAccessTypeFromFunction(endpoint.getFunction()));
      return false;
    }

    return true;
  }

  private Endpoint getMatchedEndpoint(
      ServiceInformationDto serviceInformationDto, AuthorizeDto authorizeDto) {

    for (Endpoint endpoint : serviceInformationDto.getEndpoints()) {
      if (matchUrl(endpoint.getUrl(), authorizeDto.getEndPoint())
          && endpoint.getMethod().equals(authorizeDto.getMethod())) {
        log.info(
            "Match found from directory url: {} and http method: {} ",
            endpoint.getUrl(),
            endpoint.getMethod());
        return endpoint;
      }
    }

    return null;
  }

  private boolean matchUrl(String template, String actualUrl) {
    String regex = "\\{([^}]+)\\}";
    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(template);

    String regexTemplate = matcher.replaceAll("([^/]+)");

    Pattern actualPattern = Pattern.compile(regexTemplate);
    Matcher actualMatcher = actualPattern.matcher(actualUrl);

    if (actualMatcher.matches()) {
      matcher.reset();
      actualMatcher.reset();
      while (matcher.find() && actualMatcher.find()) {
        String paramName = matcher.group(1);
        String paramValue = actualMatcher.group(1);
        log.info(paramName + ": " + paramValue);
      }
      return true;
    } else {
      return false;
    }
  }

  private String getAccessTypeFromFunction(String method) {
      return switch (method) {
          case "READ" -> "view";
          case "WRITE" -> "edit";
          case "DELETE" -> "delete";
          default -> "unknown";
      };
  }

  private Group getGroup(UUID groupId) {
    return groupRepository
        .findById(groupId)
        .orElseThrow(
            () -> new NoSuchElementException(GROUP_WITH_ID_DOES_NOT_EXIST.formatted(groupId)));
  }

  private Policy getPolicy(UUID policyId) {
    return policyRepository
        .findById(policyId)
        .orElseThrow(
            () -> new NoSuchElementException(POLICY_WITH_ID_DOES_NOT_EXIST.formatted(policyId)));
  }

  public Set<User> validateAndFetchUsers(UUID organizationId, AddMembersToGroupDto addMembersToGroupDto) {

    Set<User> users = userManagerService.findByEmailIn(addMembersToGroupDto.getMemberEmails());

    if (addMembersToGroupDto.getMemberEmails().size() > users.size()) {
      addMembersToGroupDto
          .getMemberEmails()
          .removeAll(users.stream().map(User::getEmail).collect(Collectors.toSet()));

      throw new RestException(
          HttpStatus.NOT_FOUND,
          "Users with emails: %s does not have assign roles in organization with id: %s",
          addMembersToGroupDto.getMemberEmails().stream()
              .collect(Collectors.joining(", ", "[", "]")),
          organizationId);
    }

    return users;
  }
}
