package com.fathom.services.usermanagement.model.dto;

import com.fathom.services.usermanagement.model.enums.AccessTypeEnum;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Set;
import java.util.UUID;
import lombok.Data;

@Data
public class PolicyDto {
  UUID id;
  String name;
  String description;
  Set<String> workspaces;
  Set<AccessTypeEnum> accessTypes;
  Set<String> features;
  String createdBy;
  String updatedBy;
  LocalDateTime createdDate;
  LocalDateTime updatedDate;
  LocalDate activationDate;
  LocalDate deactivationDate;
}
