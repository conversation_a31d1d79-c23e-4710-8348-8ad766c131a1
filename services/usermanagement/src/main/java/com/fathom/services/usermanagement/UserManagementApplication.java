package com.fathom.services.usermanagement;

import com.fathom.diagnostics.configuration.DiagnosticProperties;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Contact;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.info.License;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@OpenAPIDefinition(
    info =
        @Info(
            title = "User management service",
            description = "This application used to manger users, policies, groups",
            license = @License(name = "Apache 2.0", url = "https://fathoms.io"),
            contact =
                @Contact(
                    name = "Fathom Solutions",
                    email = "<EMAIL>",
                    url = "https://fathom.io")))
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@EnableConfigurationProperties(DiagnosticProperties.class)
@ComponentScan(basePackages = {
        "com.fathom.services.usermanagement",
        "com.fathom.diagnostics"
})
public class UserManagementApplication {

  public static void main(String[] args) {
    SpringApplication.run(UserManagementApplication.class, args);
  }
}
