package com.fathom.services.usermanagement.feign;

import static com.fathom.services.usermanagement.util.StaticProperties.ORGANIZATION_HEADER;

import com.fathom.services.usermanagement.model.dto.feign.InvitationDto;
import java.util.List;
import java.util.UUID;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${services.invitation.name}")
public interface InvitationFeignClient {

  @PostMapping("/user/invitation/latest-invitations")
  List<InvitationDto> getLatestInvitations(@RequestHeader(ORGANIZATION_HEADER) UUID organizationId);
}
