package com.fathom.services.usermanagement.services;

import com.fathom.lib.advancedfilters.model.*;
import com.fathom.services.usermanagement.model.User;
import com.fathom.services.usermanagement.model.dto.OrganizationUserEmailDto;
import com.fathom.services.usermanagement.model.dto.UserCreateDto;
import com.fathom.services.usermanagement.model.dto.UserDto;
import com.fathom.services.usermanagement.model.enums.RoleType;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

public interface UserManagerService {
  User createUserInNotExist(UUID organizationId, String email);

  List<UserDto> createUsers(UUID organizationId, List<UserCreateDto> userCreateDto);

  void createOwnerRole(UUID organizationId, RoleType roleType, String adminEmail);

  UserDto createRole(UUID organizationId, RoleType roleType, String adminEmail, String memberEmail);

  void deleteRole(UUID organizationId, String adminEmail, String email);

  void checkIsUserExistAndAuthorized(UUID organizationId, String email);

  boolean isAdminWithinOrganization(UUID organizationId, String email);

  Set<User> findByEmailIn(Set<String> emails);

  void uploadProfileImage(UUID organizationId, String adminEmail, MultipartFile file);

  void deleteCurrentUser(String email);

  List<OrganizationUserEmailDto> getAllUsersRoles(String email);

  OrganizationUserEmailDto getUserRole(UUID organizationId, String email);

  Page<UserDto> getUsersFiltered(
      UUID organizationId, CustomFilter filterCriteria, int pageNumber, int pageSize);
}
