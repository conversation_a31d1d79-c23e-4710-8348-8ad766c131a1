package com.fathom.services.usermanagement.services;

import com.fathom.services.usermanagement.model.dto.*;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;

public interface GroupService {
  Page<GroupDto> getGroupsByOrganizationIdAndProjectId(
      UUID organizationId, int pageNumber, int pageSize, boolean pageable);

  List<GroupDto> createGroups(
      UUID organizationId, String email, List<GroupCreateUpdateDto> groupCreateUpdateDtoList);

  GroupDto overrideMembersInGroup(
      UUID organizationId, String email, UUID groupId, AddMembersToGroupDto addMembersToGroupDto);

  GroupDto addMembersInGroup(
      UUID organizationId, String email, UUID groupId, AddMembersToGroupDto addMembersToGroupDto);

  GroupDto deleteMembersInGroup(
      UUID organizationId, String email, UUID groupId, AddMembersToGroupDto addMembersToGroupDto);

  GroupDto addPoliciesToGroup(
      UUID organizationId, String email, UUID groupId, AddPoliciesToGroupDto addPoliciesToGroupDto);

  GroupDto updateGroup(
      UUID organizationId, String email, UUID groupId, GroupCreateUpdateDto groupCreateUpdateDto);

  void deleteGroup(UUID organizationId, String email, UUID groupId);

  void deleteGroupsByIds(UUID organizationId, String email, Set<UUID> groupIds);

  List<GroupDto> addManyUsersToManyGroups(
      UUID organizationId, String email, UserEmailsGroupsDto userEmailsGroupsDto);

  List<GroupDto> deleteManyUsersFromGroups(
      UUID organizationId, String email, UserEmailsGroupsDto userEmailsGroupsDto);

  List<GroupDto> updateManyUsersInManyGroups(
      UUID organizationId, String email, UserEmailsGroupsDto userEmailsGroupsDto);

  List<PolicyDto> createPolicies(
      UUID organizationId, String email, List<PolicyCreateUpdateDto> policyCreateUpdateDtoList);

  Page<PolicyDto> getPoliciesByOrganizationId(
      UUID organizationId, int pageNumber, int pageSize, boolean pageable);

  PolicyDto updatePolicy(
      UUID organizationId,
      String email,
      UUID policyId,
      PolicyCreateUpdateDto policyCreateUpdateDto);

  void deletePolicy(UUID organizationId, String email, UUID policyId);

  void deletePolicies(UUID organizationId, String email, Set<UUID> policyIds);

  boolean isHasTheRightAccessPolicy(
      UUID organizationId, String projectId, String email, AuthorizeDto authorizeDto);

  GroupDto getGroupByOrganizationIdAndName(UUID organizationId, String name);

  List<GroupDto> getGroupsByOrganizationIdAndNames(UUID organizationId, Set<String> name);
}
