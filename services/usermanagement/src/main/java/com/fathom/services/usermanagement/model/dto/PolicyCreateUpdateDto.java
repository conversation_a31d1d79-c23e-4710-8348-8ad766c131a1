package com.fathom.services.usermanagement.model.dto;

import com.fathom.services.usermanagement.model.enums.AccessTypeEnum;
import java.time.LocalDate;
import java.util.Set;
import lombok.Data;

@Data
public class PolicyCreateUpdateDto {
  String name;
  String description;
  Set<String> workspaces;
  Set<AccessTypeEnum> accessTypes;
  Set<String> features;
  LocalDate activationDate;
  LocalDate deactivationDate;
}
