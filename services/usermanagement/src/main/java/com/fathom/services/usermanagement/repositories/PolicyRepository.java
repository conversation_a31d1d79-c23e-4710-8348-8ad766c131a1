package com.fathom.services.usermanagement.repositories;

import com.fathom.services.usermanagement.model.Policy;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface PolicyRepository extends JpaRepository<Policy, UUID> {
  Set<Policy> findByOrganizationIdAndIdIn(UUID organizationId, List<UUID> privilegeIds);

  Page<Policy> findByOrganizationId(UUID organizationId, Pageable pageable);

  void deleteByOrganizationIdAndIdIn(UUID organizationId, Set<UUID> policyIds);

  Set<Policy> findByOrganizationIdAndNameIn(UUID organizationId, Set<String> policyNames);

  @Query(
      "SELECT p.name FROM Policy p WHERE p.organizationId = :organizationId AND p.name IN :policyNames")
  Set<String> findPolicyNamesByOrganizationIdAndNameIn(
      UUID organizationId, Set<String> policyNames);
}
