package com.fathom.services.usermanagement.util;

public class ExMessages {
  private ExMessages() {}

  public static final String USER_DOES_NOT_EXIST = "User with email %s doesn't exist";
  public static final String GROUP_WITH_ID_DOES_NOT_EXIST = "Group with id %s doesn't exist";
  public static final String POLICY_WITH_ID_DOES_NOT_EXIST = "Policy with id %s doesn't exist";
  public static final String ROLE_NOT_FOUND = "Not role found under organization with id: %s";
  public static final String USER_UNAUTHORIZED =
      "User with email %s doesn't have the correct role to preform this action";
  public static final String KEYCLOAK_USER_NOT_CREATED =
      "User with email: %s not created in Keycloak";
  public static final String UNAUTHORIZED_REQUEST_TO_KEYCLOAK = "Unauthorized request to Keycloak";
  public static final String USER_DELETION_FAILED = "Failed to delete user account: %s";
  public static final String USER_NOT_FOUND_FOR_DELETION = "User account not found for deletion: %s";
}
