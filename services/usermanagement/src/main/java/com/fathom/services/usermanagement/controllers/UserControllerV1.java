package com.fathom.services.usermanagement.controllers;

import com.fathom.lib.advancedfilters.model.CustomFilter;
import com.fathom.services.usermanagement.model.dto.AddUserDTO;
import com.fathom.services.usermanagement.model.dto.OrganizationUserEmailDto;
import com.fathom.services.usermanagement.model.dto.SetupPasswordDTO;
import com.fathom.services.usermanagement.model.dto.UserDto;
import com.fathom.services.usermanagement.model.enums.RoleType;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;
import java.util.UUID;

import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

public interface UserControllerV1 {

    @Operation(
            summary = "Endpoint for getting users by advanced filtering options.",
            description = """
                    This endpoint retrieves a list of users based on advanced filtering options specified in the `Filter` object. The filters can include conditions like equality, range (greater than, less than), and logical combinations (AND, OR).
                    
                    You can specify the following filtering criteria:
                       - **@and**: Combines multiple filters that all must be satisfied.
                       - **@or**: Combines multiple filters where at least one must be satisfied.
                       - **@gt**: Checks if a value is greater than a specified value.
                       - **@lt**: Checks if a value is less than a specified value.
                       - **@eq**: Checks if a value is equal to a specified value.
                       - **@notEq**: Checks if a value is not equal to a specified value.
                       - **@gte**: Checks if a value is greater than or equal to a specified value.
                       - **@lte**: Checks if a value is less than or equal to a specified value.
                       - **@in**: Checks if a value is present in a list of specified values.
                       - **@not**: Checks if a value is not present in a specified list.
                       - **@contains**: Checks if a string contains a specified substring.
                       - **@between**: Checks if a value lies within a specified range.
                       - **@regex**: Checks if a string matches a specified regular expression.
                    
                    ### Example Request:
                    To filter users with a price greater than 10 and less than 99, you can use the following JSON structure:
                    ```json
                    {
                      "filter": {
                        "@and": [
                          {
                            "@gt": {
                              "path": "price",
                              "value": 10
                            }
                          },
                          {
                            "@lt": {
                              "path": "price",
                              "value": 99
                            }
                          }
                        ]
                      },
                      "sort": {
                        "by": "columnName",
                        "ord": "ASC|DESC"
                       }
                    }
                    ```
                    
                    ### Pagination:
                    You can also specify pagination by using `Pageable` parameters. Make sure to include `pageNumber` and `pageSize` in your request for efficient data retrieval.
                    
                    ### Response:
                    The response will contain a list of `UserDto` objects that match the filtering criteria. If no users are found, an empty list will be returned.
                    
                    ### Usage:
                    This endpoint is useful for retrieving specific user information based on various attributes, which can be beneficial for building user management features in your application.
                    """
    )
    ResponseEntity<Page<UserDto>> getUsersFiltered(UUID organizationId, CustomFilter customFilter, int pageNumber, int pageSize);

    @Operation(
            summary = "Endpoint for all user's roles in every organization they are added in. ",
            description = "Endpoint for all user's roles in every organization they are added in. ")
    ResponseEntity<List<OrganizationUserEmailDto>> getAllUsersRoles(String email);

    @Operation(
            summary = "Endpoint for getting user role in an organization. ",
            description = "Endpoint for getting user role in an organization. ")
    ResponseEntity<OrganizationUserEmailDto> getUserRole(UUID organizationId, String email);

    @Operation(
            summary = "Endpoint for creating a user role or update it if it is existing. ",
            description = "Endpoint for creating a user role or update it if it is existing. ")
    ResponseEntity<UserDto> createRole(
            UUID organizationId, String adminEmail, RoleType roleType, String email);

    @Operation(
            summary = "Endpoint for deleting a user role. ",
            description = "Endpoint for deleting a user role. ")
    ResponseEntity<UserDto> deleteRole(UUID organizationId, String adminEmail, String email);

    @Operation(
            summary = "Endpoint for check if the user is an admin with the origination. ",
            description = "Endpoint for check if the user is an admin with the origination. ")
    ResponseEntity<Boolean> isAdminWithinOrganization(UUID organizationId, String email);

    @Operation(
            summary = "Endpoint for create a uploading a profile image. ",
            description = "Endpoint for create a uploading a profile image. ")
    ResponseEntity<UserDto> uploadProfileImage(UUID organizationId, String email, MultipartFile file);

    @Operation(
            summary = "Endpoint for creating a user. ",
            description = "Endpoint for creating a user as a facade to keycloak. User ends up without a password, and so " +
                    "the frontend will send another request to reset password.")
    ResponseEntity<UserDto> addUser(@Valid AddUserDTO adduser);

    @Operation(
            summary = "Endpoint for resetting users' password",
            description = "This endpoint will interact with keycloak to facilitate password setting to the " +
                    "one provided in parameters"
    )
    ResponseEntity<Void> setupPassword(String email, @Valid SetupPasswordDTO password);

    @Operation(
            summary = "Temp Endpoint for creating a user role. ",
            description = "Temp Endpoint for creating a user role. ")
    ResponseEntity<UserDto> createOwnerRole(UUID organizationId, String email);

    @Operation(
            summary = "Endpoint for deleting current user account.",
            description = "This endpoint allows the authenticated user to delete their own account. " +
                    "The user is identified by the email in the request header. " +
                    "This action will remove the user from both Keycloak and the local database. " +
                    "After deletion, the user will no longer be able to login to the platform.")
    ResponseEntity<Void> deleteCurrentUser(String email);
}
