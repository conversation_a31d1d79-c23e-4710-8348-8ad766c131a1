package com.fathom.services.usermanagement.repositories;

import com.fathom.services.usermanagement.model.User;
import com.fathom.services.usermanagement.model.enums.RoleType;
import java.util.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface UserRepository extends JpaRepository<User, UUID>, JpaSpecificationExecutor<User> {
  Optional<User> findByEmail(String email);

  Page<User> findByRoles_OrganizationIdAndRoles_RoleTypeNot(
      UUID organizationId, RoleType notRole, Pageable pageable);

  default Page<User> findNonSuperAdminsInOrganization(UUID organizationId, Pageable pageable) {
    return findByRoles_OrganizationIdAndRoles_RoleTypeNot(
        organizationId, RoleType.SUPER_ADMIN, pageable);
  }

  Set<User> findByEmailIn(Set<String> memberEmails);

  @Query(
      "select u from User u inner join u.roles roles where u.email = ?1 and roles.organizationId = ?2")
  Optional<User> findByEmailAndRoles_OrganizationId(String email, UUID organizationId);

  void deleteByEmail(String email);
}
