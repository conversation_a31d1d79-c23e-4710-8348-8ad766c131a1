package com.fathom.services.usermanagement.model;

import jakarta.persistence.*;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;

@Entity
@Getter
@Setter
@Table(
    name = "password_reset_timestamps",
    uniqueConstraints = @UniqueConstraint(columnNames = "userEmail"))
public class PasswordResetTimestamp {
  @Id
  @GeneratedValue(generator = "UUID")
  @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
  @Column(name = "id", updatable = false, nullable = false)
  UUID id;

  long timestamp;
  String userEmail;

  public PasswordResetTimestamp(String userEmail, long timestamp) {
    this.userEmail = userEmail;
    this.timestamp = timestamp;
  }

  public PasswordResetTimestamp() {}
}
