package com.fathom.services.usermanagement.services.impl;

import static com.fathom.services.usermanagement.util.ExMessages.*;

import com.fathom.lib.advancedfilters.filters.FilterAndSortInMemoryUtil;
import com.fathom.lib.advancedfilters.model.CustomFilter;
import com.fathom.lib.advancedfilters.model.SortOrder;
import com.fathom.lib.common.exception.RestException;
import com.fathom.services.usermanagement.feign.FileClient;
import com.fathom.services.usermanagement.mapper.UserMapper;
import com.fathom.services.usermanagement.model.Role;
import com.fathom.services.usermanagement.model.User;
import com.fathom.services.usermanagement.model.dto.OrganizationUserEmailDto;
import com.fathom.services.usermanagement.model.dto.UserCreateDto;
import com.fathom.services.usermanagement.model.dto.UserDto;
import com.fathom.services.usermanagement.model.dto.sorting.UserDtoComparatorFactory;
import com.fathom.services.usermanagement.model.enums.RoleType;
import com.fathom.services.usermanagement.repositories.PasswordResetTimestampRepository;
import com.fathom.services.usermanagement.repositories.RoleRepository;
import com.fathom.services.usermanagement.repositories.UserRepository;
import com.fathom.services.usermanagement.services.UserManagerService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserManagerServiceImpl implements UserManagerService {
  private final UserRepository userRepository;
  private final RoleRepository roleRepository;
  private final UserMapper userMapper;
  private final PasswordResetTimestampRepository passwordResetTimestampRepository;
  private final UserExtraInformationService userExtraInformationService;
  private final FileClient fileClient;
  private final KeycloakService keycloakService;
  private static final List<String> ALLOWED_IMAGE_EXTENSIONS =
      Arrays.asList("jpg", "jpeg", "png", "gif");

  @PersistenceContext private EntityManager entityManager;

  @Override
  public User createUserInNotExist(UUID organizationId, String email) {
    return userRepository
        .findByEmail(email)
        .orElseGet(() -> userRepository.save(userMapper.toEntityUsingEmail(organizationId, email)));
  }

  @Override
  public List<UserDto> createUsers(UUID organizationId, List<UserCreateDto> userCreateDto) {
    List<User> users =
        userCreateDto.stream().map(x -> userMapper.toEntity(organizationId, x)).toList();
    users = userRepository.saveAll(users);
    userExtraInformationService.loadExtraUserInformation(organizationId, users);
    return users.stream().map(source -> userMapper.toDto(organizationId, source)).toList();
  }

  @Override
  @Transactional
  public void createOwnerRole(UUID organizationId, RoleType roleType, String adminEmail) {
    var user = createUserInNotExist(organizationId, adminEmail);
    Set<Role> roleSet = new HashSet<>(Objects.isNull(user.getRoles()) ? Set.of() : user.getRoles());
    roleSet.add(getRole(organizationId, roleType, user));
    user.setRoles(roleSet);
    userRepository.save(user);
  }

  @Override
  @Transactional
  public UserDto createRole(
      UUID organizationId, RoleType roleType, String adminEmail, String memberEmail) {

    if (roleType.equals(RoleType.SUPER_ADMIN)) {
      throw new RestException(
          HttpStatus.BAD_REQUEST,
          "super_admin role only reserved for the organization owner",
          memberEmail);
    }

    checkIsUserExistAndAuthorized(organizationId, adminEmail);

    Optional<User> user =
        userRepository.findByEmailAndRoles_OrganizationId(memberEmail, organizationId);

    if (user.isPresent()) {

      RoleType existingRoleType =
          user.get().getRoles().stream()
              .filter(x -> x.getOrganizationId().equals(organizationId))
              .findFirst()
              .orElseThrow(() -> new RuntimeException(ROLE_NOT_FOUND.formatted(organizationId)))
              .getRoleType();

      if (existingRoleType.equals(RoleType.SUPER_ADMIN)) {
        throw new RestException(
            HttpStatus.BAD_REQUEST,
            "User with email: %s has the role of super-admin (i.e. creator) which cannot be changed",
            memberEmail);
      }

      user.get().getRoles().stream()
          .filter(x -> x.getOrganizationId().equals(organizationId))
          .findFirst()
          .orElseThrow(() -> new RuntimeException(ROLE_NOT_FOUND.formatted(organizationId)))
          .setRoleType(roleType);

      userExtraInformationService.loadExtraUserInformation(organizationId, List.of(user.get()));
      return userMapper.toDto(organizationId, userRepository.save(user.get()));
    }

    return createNewRole(organizationId, roleType, memberEmail);
  }

  @Override
  @Transactional
  public void deleteRole(UUID organizationId, String adminEmail, String email) {
    checkIsUserExistAndAuthorized(organizationId, adminEmail);

    Optional<User> user = userRepository.findByEmail(email);

    if (user.isPresent()) {

      RoleType existingRoleType =
          user.get().getRoles().stream()
              .filter(x -> x.getOrganizationId().equals(organizationId))
              .findFirst()
              .orElseThrow(() -> new RuntimeException(ROLE_NOT_FOUND.formatted(organizationId)))
              .getRoleType();

      if (existingRoleType.equals(RoleType.SUPER_ADMIN)) {
        throw new RestException(
            HttpStatus.BAD_REQUEST,
            "User with email: %s has the role of super-admin (i.e. creator) which cannot be changed",
            adminEmail);
      }
    }

    user.ifPresent(u -> roleRepository.deleteByOrganizationIdAndUsers(organizationId, u));
  }

  @Transactional
  private UserDto createNewRole(UUID organizationId, RoleType roleType, String adminEmail) {
    User user = createUserInNotExist(organizationId, adminEmail);
    Set<Role> roleSet = new HashSet<>(Objects.isNull(user.getRoles()) ? Set.of() : user.getRoles());
    roleSet.add(getRole(organizationId, roleType, user));
    user.setRoles(roleSet);
    userExtraInformationService.loadExtraUserInformation(organizationId, List.of(user));
    return userMapper.toDto(organizationId, userRepository.save(user));
  }


  private Role getRole(UUID organizationId, RoleType roleType, User user) {
    Role role = new Role();
    role.setOrganizationId(organizationId);
    role.setRoleType(roleType);
    role.setUsers(user);
    return role;
  }

  @Override
  public void checkIsUserExistAndAuthorized(UUID organizationId, String email) {
    Optional<User> user = userRepository.findByEmail(email);

    if (user.isEmpty()) {
      throw new RestException(HttpStatus.NOT_FOUND, USER_DOES_NOT_EXIST.formatted(email));
    }

    Set<Role> roles = user.get().getRoles();

    if (Objects.isNull(roles)) {
      throw new RestException(HttpStatus.UNAUTHORIZED, USER_UNAUTHORIZED.formatted(email));
    }

    Role role =
        roles.stream()
            .filter(x -> x.getOrganizationId().equals(organizationId))
            .findAny()
            .orElseThrow(
                () ->
                    new RestException(HttpStatus.UNAUTHORIZED, USER_UNAUTHORIZED.formatted(email)));

    if (role.getRoleType().equals(RoleType.MEMBER)) {
      throw new RestException(HttpStatus.UNAUTHORIZED, USER_UNAUTHORIZED.formatted(email));
    }
  }

  @Override
  @Transactional
  public boolean isAdminWithinOrganization(UUID organizationId, String email) {
    Optional<User> user = userRepository.findByEmail(email);
    if (user.isEmpty()) {
      return false;
    }

    Set<Role> roles = user.get().getRoles();
    if (Objects.isNull(roles)) {
      return false;
    }

    Role role =
        roles.stream()
            .filter(x -> x.getOrganizationId().equals(organizationId))
            .findAny()
            .orElse(null);

    if (Objects.isNull(role)) {
      return false;
    }

    RoleType roleType = role.getRoleType();

    if (Objects.isNull(roleType)) {
      return false;
    }

    return !role.getRoleType().equals(RoleType.MEMBER);
  }

  @Override
  public Set<User> findByEmailIn(Set<String> emails) {
    return userRepository.findByEmailIn(emails);
  }

  @Override
  public void uploadProfileImage(UUID organizationId, String adminEmail, MultipartFile file) {

    String fileName = file.getOriginalFilename();

    if (fileName != null) {
      String fileExtension = getFileExtension(fileName);
      if (!ALLOWED_IMAGE_EXTENSIONS.contains(fileExtension.toLowerCase())) {
        throw new RestException(
            HttpStatus.BAD_REQUEST,
            "File type is not of image extension of the following %s",
            ALLOWED_IMAGE_EXTENSIONS.stream().collect(Collectors.joining(", ", "[", "]")));
      }
    }

    User user =
        userRepository
            .findByEmailAndRoles_OrganizationId(adminEmail, organizationId)
            .orElseThrow(
                () ->
                    new RestException(
                        HttpStatus.NOT_FOUND,
                        "User with email: %s is not found in organization with id: %s",
                        adminEmail,
                        organizationId));

    fileClient.deleteByUuid(user.getId());

    fileClient.addFile(
        user.getId(),
        file,
        String.format("%s profile picture", user.getEmail()),
        "profile-picture",
        String.format("%s profile picture", user.getEmail()));
  }

  @Override
  public List<OrganizationUserEmailDto> getAllUsersRoles(String email) {

    User user = userRepository.findByEmail(email).orElse(null);

    return Objects.isNull(user)
        ? List.of()
        : user.getRoles().stream()
            .map(x -> new OrganizationUserEmailDto(x.getOrganizationId(), x.getRoleType()))
            .toList();
  }

  @Override
  public OrganizationUserEmailDto getUserRole(UUID organizationId, String email) {

    Role role = roleRepository.findByOrganizationIdAndUsers_Email(organizationId, email);

    if (role == null) {
      throw new RestException(
          HttpStatus.BAD_REQUEST,
          "User with email " + email + " not found in the organization with id" + organizationId);
    }

    return new OrganizationUserEmailDto(role.getOrganizationId(), role.getRoleType());
  }

  @Override
  public Page<UserDto> getUsersFiltered(
      UUID organizationId, CustomFilter filterCriteria, int pageNumber, int pageSize) {

    log.info("Fetching users with filtering criteria for organizationId: {}", organizationId);

    // Apply default sorting option if they are not provided
    if (Objects.isNull(filterCriteria)) {
      filterCriteria = new CustomFilter();
    }

    Comparator<UserDto> comparator = null;
    if (Objects.isNull(filterCriteria.getSort())) {
      filterCriteria.setSort(new CustomFilter.CustomSort());
      filterCriteria.getSort().setBy("invitationUpdateDate");
      filterCriteria.getSort().setOrd(SortOrder.DESCENDING);
      comparator = UserDtoComparatorFactory.getComparator("invitationUpdateDate");
    }
    comparator = UserDtoComparatorFactory.getComparator(filterCriteria.getSort().getBy());

    Page<User> usersPage =
        userRepository.findNonSuperAdminsInOrganization(
            organizationId, PageRequest.of(pageNumber, pageSize));

    // Load Additional User Information (Enrich Data)
    userExtraInformationService.loadExtraUserInformation(organizationId, usersPage.getContent());

    List<UserDto> userDto =
        new ArrayList<>(
            usersPage.stream().map(user -> userMapper.toDto(organizationId, user)).toList());
    userDto.addAll(userExtraInformationService.getUserNotActivated());

    Page<UserDto> userDtoPage =
        new PageImpl<>(userDto, PageRequest.of(pageNumber, pageSize), usersPage.getTotalElements());

    // Apply In-Memory Filtering & Sorting
    FilterAndSortInMemoryUtil<UserDto> filterSortUtil = new FilterAndSortInMemoryUtil<>();

    return filterSortUtil.applyFiltersAndSort(
        userDtoPage.getContent(), filterCriteria, comparator, pageNumber, userDtoPage.getSize());
  }

  @Override
  @Transactional
  public void deleteCurrentUser(String email) {
    try {
      keycloakService.deleteUser(email);

      userRepository.deleteByEmail(email);

      log.info("Successfully deleted user account: {}", email);
    } catch (Exception e) {
      log.error("Failed to delete user account: {}", email, e);
      throw new RestException(HttpStatus.BAD_REQUEST, USER_DELETION_FAILED, email);
    }
  }

  private String getFileExtension(String fileName) {
    int lastDotIndex = fileName.lastIndexOf(".");
    if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
      return fileName.substring(lastDotIndex + 1);
    }
    return "";
  }
}
