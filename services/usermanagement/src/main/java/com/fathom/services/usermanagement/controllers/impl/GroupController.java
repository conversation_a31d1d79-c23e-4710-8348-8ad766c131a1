package com.fathom.services.usermanagement.controllers.impl;

import static com.fathom.services.usermanagement.util.StaticProperties.*;

import com.fathom.services.usermanagement.controllers.GroupControllerV1;
import com.fathom.services.usermanagement.model.dto.*;
import com.fathom.services.usermanagement.services.GroupService;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Validated
@RestController
@RequiredArgsConstructor
public class GroupController implements GroupControllerV1 {

  private final GroupService groupService;

  @Override
  @GetMapping("groups")
  public ResponseEntity<Page<GroupDto>> getGroupsByOrganizationId(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestParam(defaultValue = "1", required = false) int pageNumber,
      @RequestParam(defaultValue = "10", required = false) int pageSize,
      @RequestParam(defaultValue = "false", required = false) boolean pageable) {

    return ResponseEntity.ok(
        groupService.getGroupsByOrganizationIdAndProjectId(
            organizationId, pageNumber, pageSize, pageable));
  }

  @Override
  @PostMapping("groups/byNames")
  public ResponseEntity<List<GroupDto>> getGroupsByOrganizationIdAndNames(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId, @RequestBody Set<String> name) {

    return ResponseEntity.ok(groupService.getGroupsByOrganizationIdAndNames(organizationId, name));
  }

  @Override
  @PostMapping("groups")
  public ResponseEntity<List<GroupDto>> createGroups(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @RequestBody @Valid List<@Valid GroupCreateUpdateDto> groupCreateUpdateDto) {

    return ResponseEntity.ok(
        groupService.createGroups(organizationId, email, groupCreateUpdateDto));
  }

  @Override
  @PostMapping("groups/members/{groupId}")
  public ResponseEntity<GroupDto> overrideMembersInGroup(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @PathVariable UUID groupId,
      @RequestBody AddMembersToGroupDto addMembersToGroupDto) {

    return ResponseEntity.ok(
        groupService.overrideMembersInGroup(organizationId, email, groupId, addMembersToGroupDto));
  }

  @Override
  @PutMapping("groups/members/{groupId}")
  public ResponseEntity<GroupDto> addMembersInGroup(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @PathVariable UUID groupId,
      @RequestBody AddMembersToGroupDto addMembersToGroupDto) {

    return ResponseEntity.ok(
        groupService.addMembersInGroup(organizationId, email, groupId, addMembersToGroupDto));
  }

  @Override
  @DeleteMapping("groups/members/{groupId}")
  public ResponseEntity<GroupDto> deleteMembersInGroup(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @PathVariable UUID groupId,
      @RequestBody AddMembersToGroupDto addMembersToGroupDto) {
    return ResponseEntity.ok(
        groupService.deleteMembersInGroup(organizationId, email, groupId, addMembersToGroupDto));
  }

  @Override
  @PostMapping("groups/members/many-to-groups")
  public ResponseEntity<List<GroupDto>> addManyUsersToManyGroups(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @RequestBody UserEmailsGroupsDto userEmailsGroupsDto) {

    return ResponseEntity.ok(
        groupService.addManyUsersToManyGroups(organizationId, email, userEmailsGroupsDto));
  }

  @Override
  @PutMapping("groups/members/many-in-groups")
  public ResponseEntity<List<GroupDto>> updateManyUsersInManyGroups(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @RequestBody UserEmailsGroupsDto userEmailsGroupsDto) {
    return ResponseEntity.ok(
        groupService.updateManyUsersInManyGroups(organizationId, email, userEmailsGroupsDto));
  }

  @Override
  @DeleteMapping("groups/members/many-from-groups")
  public ResponseEntity<List<GroupDto>> deleteManyUsersFromGroups(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @RequestBody UserEmailsGroupsDto userEmailsGroupsDto) {

    return ResponseEntity.ok(
        groupService.deleteManyUsersFromGroups(organizationId, email, userEmailsGroupsDto));
  }

  @Override
  @PostMapping("groups/policies/{groupId}")
  public ResponseEntity<GroupDto> addPoliciesToGroup(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @PathVariable UUID groupId,
      @RequestBody AddPoliciesToGroupDto addPoliciesToGroupDto) {

    return ResponseEntity.ok(
        groupService.addPoliciesToGroup(organizationId, email, groupId, addPoliciesToGroupDto));
  }

  @Override
  @PutMapping("groups/{groupId}")
  public ResponseEntity<GroupDto> updateGroup(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @PathVariable UUID groupId,
      @RequestBody GroupCreateUpdateDto groupCreateUpdateDto) {

    return ResponseEntity.ok(
        groupService.updateGroup(organizationId, email, groupId, groupCreateUpdateDto));
  }

  @Override
  @DeleteMapping("groups/{groupId}")
  public ResponseEntity<GroupDto> deleteGroup(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @PathVariable UUID groupId) {

    groupService.deleteGroup(organizationId, email, groupId);
    return ResponseEntity.ok().build();
  }

  @Override
  @DeleteMapping("groups")
  public ResponseEntity<GroupDto> deleteGroupsByIds(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @RequestBody Set<UUID> groupIds) {

    groupService.deleteGroupsByIds(organizationId, email, groupIds);
    return ResponseEntity.ok().build();
  }

  @Override
  @GetMapping("groups/policies")
  public ResponseEntity<Page<PolicyDto>> getPoliciesByOrganizationId(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestParam(defaultValue = "1", required = false) int pageNumber,
      @RequestParam(defaultValue = "10", required = false) int pageSize,
      @RequestParam(defaultValue = "false", required = false) boolean pageable) {

    return ResponseEntity.ok(
        groupService.getPoliciesByOrganizationId(organizationId, pageNumber, pageSize, pageable));
  }

  @Override
  @PostMapping("groups/policies")
  public ResponseEntity<List<PolicyDto>> createPolicies(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @RequestBody List<PolicyCreateUpdateDto> policyCreateUpdateDtoList) {
    return ResponseEntity.ok(
        groupService.createPolicies(organizationId, email, policyCreateUpdateDtoList));
  }

  @Override
  @PutMapping("groups/policies/{policyId}")
  public ResponseEntity<PolicyDto> updatePolicy(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @PathVariable UUID policyId,
      @RequestBody PolicyCreateUpdateDto policyCreateUpdateDto) {

    return ResponseEntity.ok(
        groupService.updatePolicy(organizationId, email, policyId, policyCreateUpdateDto));
  }

  @Override
  @DeleteMapping("groups/policies/{policyId}")
  public ResponseEntity<PolicyDto> deletePolicy(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @PathVariable UUID policyId) {

    groupService.deletePolicy(organizationId, email, policyId);
    return ResponseEntity.ok().build();
  }

  @Override
  @PostMapping("groups/policies/delete")
  public ResponseEntity<PolicyDto> deletePolicies(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email,
      @RequestBody Set<UUID> policyIds) {
    groupService.deletePolicies(organizationId, email, policyIds);
    return ResponseEntity.ok().build();
  }
}
