package com.fathom.services.usermanagement.feign;

import com.fathom.services.usermanagement.model.dto.feign.UserProfileDto;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "${services.userprofile.name}")
public interface UserProfileClient {
  String EMAIL_HEADER_NAME = "x-email";

  @PostMapping(path = "/profiles/retrieveManyByEmails")
  List<UserProfileDto> getManyUserProfileByNames(@RequestBody Set<String> stringSet);

  @GetMapping(path = "/profiles")
  UserProfileDto getUserProfile(@RequestHeader(EMAIL_HEADER_NAME) String email);

  @PutMapping(path = "/profiles/{profile_id}")
  void updateUserProfile(
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @PathVariable("profile_id") UUID profileId,
      @RequestBody UserProfileDto userProfileDto);
}
