package com.fathom.services.usermanagement.feign;

import com.fathom.services.usermanagement.model.dto.feign.ServiceInformationDto;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(name = "${services.director.name}")
public interface DirectorClient {
  @GetMapping(path = "serviceInformation")
  List<ServiceInformationDto> getServicesInformation();

  @GetMapping(path = "serviceInformation/{serviceName}")
  ServiceInformationDto getServicesInformation(@PathVariable String serviceName);
}
