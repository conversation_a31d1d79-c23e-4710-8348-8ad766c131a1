package com.fathom.services.usermanagement.configuration;

import com.fathom.services.usermanagement.model.enums.AccessTypeEnum;
import com.fathom.services.usermanagement.model.enums.RoleType;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
  @Override
  public void addFormatters(FormatterRegistry registry) {
    registry.addConverter(new StringToEnumConverter());

    registry.addConverter(new StringToEnumConverter2());
  }

  public static class StringToEnumConverter implements Converter<String, RoleType> {
    @Override
    public RoleType convert(String source) {
      return RoleType.valueOf(source.toUpperCase());
    }
  }

  public static class StringToEnumConverter2 implements Converter<String, AccessTypeEnum> {
    @Override
    public AccessTypeEnum convert(String source) {
      return AccessTypeEnum.valueOf(source.toUpperCase());
    }
  }
}
