package com.fathom.services.usermanagement.services.impl;

import com.fathom.lib.common.exception.RestException;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.keycloak.representations.idm.CredentialRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class KeycloakService {
  private final Keycloak kc;
  private final String realm;

  public KeycloakService(
      @Value("${keycloak.auth-server-url}") String authServerUrl,
      @Value("${keycloak.realm}") String realm,
      @Value("${keycloak.client-id}") String clientId,
      @Value("${keycloak.client-secret}") String clientSecret) {
    this.realm = realm;
    this.kc =
        KeycloakBuilder.builder()
            .realm(realm)
            .clientId(clientId)
            .clientSecret(clientSecret)
            .serverUrl(authServerUrl)
            .grantType("client_credentials")
            .build();
  }

  public UserRepresentation createUser(String firstName, String lastName, String email) {
    var kcuser = new UserRepresentation();
    kcuser.setFirstName(firstName);
    kcuser.setLastName(lastName);
    kcuser.setEmail(email);
    kcuser.setEnabled(true);
    kcuser.setUsername(email);

    try (var response = kc.realms().realm(realm).users().create(kcuser)) {
      if (response.getStatus() != 201) {
        throw new RestException(HttpStatus.BAD_REQUEST, "Failed to create user: %s", response.getStatusInfo());
      }
      return kc.realms()
          .realm(realm)
          .users()
          .get(extractUserId(response.getLocation().getPath()))
          .toRepresentation();
    } catch (Exception e) {
      throw new RestException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to create user: %s", e.getMessage());
    }
  }

  private String extractUserId(String location) {
    var parts = location.split("/");
    return parts[parts.length - 1];
  }

  public void addPasswordCredential(String email, String newPassword) {
    var users = kc.realms().realm(realm).users().searchByEmail(email, true);
    if (users.isEmpty()) {
      throw new RestException(HttpStatus.NOT_FOUND, "User not found: %s", email);
    }
    if (users.size() > 1) {
      throw new RestException(HttpStatus.BAD_REQUEST, "Multiple users found with email: %s", email);
    }
    var user = users.get(0);
    var credential = new CredentialRepresentation();
    credential.setType(CredentialRepresentation.PASSWORD);
    credential.setValue(newPassword);
    credential.setTemporary(false);
    try {
      kc.realms().realm(realm).users().get(user.getId()).resetPassword(credential);
    } catch (Exception e) {
      throw new RestException(
          HttpStatus.BAD_REQUEST, "Failed to set password for user, most likely not fitting password policy: %s", email);
    }
  }

  public void deleteUser(String email) {
    var users = kc.realms().realm(realm).users().searchByEmail(email, true);
    if (users.isEmpty()) {
      throw new RestException(HttpStatus.NOT_FOUND, "User not found in Keycloak: %s", email);
    }
    if (users.size() > 1) {
      throw new RestException(HttpStatus.BAD_REQUEST, "Multiple users found with email: %s", email);
    }
    var user = users.get(0);
    try {
      var response = kc.realms().realm(realm).users().delete(user.getId());
      if (response.getStatus() != 204) {
        throw new RestException(HttpStatus.BAD_REQUEST, "Failed to delete user from Keycloak: %s", response.getStatusInfo());
      }
    } catch (Exception e) {
      throw new RestException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to delete user from Keycloak: %s", email);
    }
  }
}
