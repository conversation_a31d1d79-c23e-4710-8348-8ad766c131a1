package com.fathom.services.usermanagement.mapper;

import com.fathom.services.usermanagement.model.Group;
import com.fathom.services.usermanagement.model.User;
import com.fathom.services.usermanagement.model.dto.GroupCreateUpdateDto;
import com.fathom.services.usermanagement.model.dto.GroupDto;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import org.mapstruct.*;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class GroupMapper {

  @Mapping(target = "organizationId", source = "organizationId")
  @Mapping(target = "creator", source = "email")
  public abstract Group toEntity(UUID organizationId, String email, GroupCreateUpdateDto source);

  public abstract GroupDto toDto(Group source);

  public abstract Group updateDtoToEntity(
      @MappingTarget Group group, GroupCreateUpdateDto groupCreateUpdateDto);

  @AfterMapping
  void dtoAfterMapping(@MappingTarget GroupDto source, Group group) {

    if (Objects.nonNull(group.getUsers())) {
      source.setMemberEmails(
          group.getUsers().stream().map(User::getEmail).collect(Collectors.toSet()));
    }
  }
}
