package com.fathom.services.usermanagement.mapper;

import com.fathom.services.usermanagement.model.Group;
import com.fathom.services.usermanagement.model.Role;
import com.fathom.services.usermanagement.model.User;
import com.fathom.services.usermanagement.model.dto.UserCreateDto;
import com.fathom.services.usermanagement.model.dto.UserDto;
import com.fathom.services.usermanagement.services.impl.UserExtraInformationService;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(
    componentModel = MappingConstants.ComponentModel.SPRING,
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
@RequiredArgsConstructor
public abstract class UserMapper {
  @Autowired private UserExtraInformationService userExtraInformationService;

  public abstract User toEntity(UUID organizationId, UserCreateDto source);

  public abstract User toEntityUsingEmail(UUID organizationId, String email);

  @Mapping(target = "role", ignore = true)
  @Mapping(target = "groups", ignore = true)
  public abstract UserDto toDto(UUID organizationId, User source);

  @AfterMapping
  public void dtoAfterMapping(@MappingTarget UserDto userDto, UUID organizationId, User user) {

    Role role =
        user.getRoles().stream()
            .filter(x -> x.getOrganizationId().equals(organizationId))
            .findFirst()
            .orElse(null);

    if (Objects.nonNull(role)) {
      userDto.setRole(role.getRoleType().getValue());
    }

    Map<UUID, UserDto> userDtoMap = userExtraInformationService.getResult();

    UserDto userDtoWithExtraInformation = userDtoMap.get(user.getId());

    if (Objects.nonNull(userDtoWithExtraInformation)) {
      userDto.setFirstName(userDtoWithExtraInformation.getFirstName());
      userDto.setLastName(userDtoWithExtraInformation.getLastName());
      userDto.setFullName(userDtoWithExtraInformation.getFullName());
      userDto.setInvitationStatus(userDtoWithExtraInformation.getInvitationStatus());
      userDto.setProfilePictureDownloadLink(
          userDtoWithExtraInformation.getProfilePictureDownloadLink());
      userDto.setInvitationId(userDtoWithExtraInformation.getInvitationId());
      userDto.setInvitationUpdateDate(userDtoWithExtraInformation.getInvitationUpdateDate());
      userDto.setInvitationCreatedDate(userDtoWithExtraInformation.getInvitationCreatedDate());
    }

    if (user.getGroups() != null && !user.getGroups().isEmpty()) {
      userDto.setGroups(
          user.getGroups().stream()
              .filter(
                  group ->
                      group != null
                          && group.getName() != null
                          && group.getOrganizationId().equals(organizationId))
              .map(Group::getName)
              .collect(Collectors.toSet()));
    }
  }
}
