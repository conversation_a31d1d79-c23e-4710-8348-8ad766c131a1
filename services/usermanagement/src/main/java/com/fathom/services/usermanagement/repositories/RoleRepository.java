package com.fathom.services.usermanagement.repositories;

import com.fathom.services.usermanagement.model.Role;
import com.fathom.services.usermanagement.model.User;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

public interface RoleRepository extends JpaRepository<Role, UUID> {
  Page<Role> findByOrganizationId(UUID organizationId, Pageable pageRequest);

  @Transactional
  @Modifying
  @Query("delete from Role r where r.organizationId = ?1 and r.users = ?2")
  void deleteByOrganizationIdAndUsers(UUID organizationId, User users);

  @Query("select r from Role r where r.organizationId = ?1 and r.users.email = ?2")
  Role findByOrganizationIdAndUsers_Email(UUID organizationId, String email);
}
