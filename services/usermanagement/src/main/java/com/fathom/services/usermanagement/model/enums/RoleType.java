package com.fathom.services.usermanagement.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;

public enum RoleType {
  SUPER_ADMIN("super_admin"),
  ADMIN("admin"),
  MEMBER("member");
  private final String value;

  RoleType(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return this.value;
  }

  public static RoleType fromValue(String value) {
    for (RoleType type : RoleType.values()) {
      if (type.value.equals(value)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid RoleType value: " + value);
  }
}
