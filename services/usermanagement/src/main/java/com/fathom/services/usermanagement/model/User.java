package com.fathom.services.usermanagement.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import java.util.Set;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "users")
public class User {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  @Column(name = "id", updatable = false, nullable = false)
  private UUID id;

  @Column(unique = true)
  String email;

  @ManyToMany(mappedBy = "users", fetch = FetchType.EAGER)
  @JsonIgnore
  Set<Group> groups;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
  @JoinColumn(name = "role_id", referencedColumnName = "id")
  @JsonIgnore
  Set<Role> roles;
}
