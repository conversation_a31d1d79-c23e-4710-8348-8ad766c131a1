package com.fathom.services.usermanagement.controllers.impl;

import static com.fathom.services.usermanagement.util.StaticProperties.*;

import com.fathom.services.usermanagement.controllers.AuthorizeControllerV1;
import com.fathom.services.usermanagement.model.dto.AuthorizeDto;
import com.fathom.services.usermanagement.services.AuthorizeService;
import jakarta.annotation.Nullable;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
public class AuthorizeController implements AuthorizeControllerV1 {
  private final AuthorizeService authorizeService;

  @Override
  @PostMapping("authorize")
  public ResponseEntity<Boolean> authorize(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(PROJECT_HEADER) @Nullable String projectId,
      @RequestHeader(EMAIL_HEADER) String email,
      @RequestBody AuthorizeDto authorizeDto) {

    return ResponseEntity.ok(
        authorizeService.authorize(organizationId, projectId, email, authorizeDto));
  }
}
