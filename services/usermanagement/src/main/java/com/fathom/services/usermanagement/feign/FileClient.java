package com.fathom.services.usermanagement.feign;

import com.fathom.services.usermanagement.model.dto.feign.FileInfoDto;
import com.fathom.services.usermanagement.model.dto.feign.TokenFileIdDto;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(name = "${services.fileservice.name}")
public interface FileClient {
  @PostMapping(path = "file/generate-download-token")
  List<TokenFileIdDto> getProfilePictureDownloadTokens(@RequestBody Set<String> stringSet);

  @PostMapping(path = "files", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  void addFile(
      @RequestParam UUID uuid,
      @RequestPart MultipartFile file,
      @RequestParam(required = false) String description,
      @RequestParam(required = false) String classification,
      @RequestParam(required = false) String displayName);

  @DeleteMapping("files/{uuid}")
  void deleteByUuid(@PathVariable UUID uuid);

  @PostMapping("files/byUUID")
  List<FileInfoDto> findFileInformationByUUIDs(@RequestBody Set<UUID> uuidList);
}
