package com.fathom.services.usermanagement.controllers.impl;

import static com.fathom.services.usermanagement.util.StaticProperties.*;

import com.fathom.lib.advancedfilters.model.CustomFilter;
import com.fathom.services.usermanagement.controllers.UserControllerV1;
import com.fathom.services.usermanagement.feign.UserProfileClient;
import com.fathom.services.usermanagement.model.dto.AddUserDTO;
import com.fathom.services.usermanagement.model.dto.OrganizationUserEmailDto;
import com.fathom.services.usermanagement.model.dto.SetupPasswordDTO;
import com.fathom.services.usermanagement.model.dto.UserDto;
import com.fathom.services.usermanagement.model.dto.feign.UserProfileDto;
import com.fathom.services.usermanagement.model.enums.RoleType;
import com.fathom.services.usermanagement.services.UserManagerService;
import com.fathom.services.usermanagement.services.impl.KeycloakService;
import io.swagger.v3.oas.annotations.Hidden;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Validated
@RestController
@RequiredArgsConstructor
public class UserController implements UserControllerV1 {

  private final UserProfileClient userProfileClient;
  private final UserManagerService userManagerService;
  private final KeycloakService kc;

  @Override
  @PostMapping("users/filtered")
  public ResponseEntity<Page<UserDto>> getUsersFiltered(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestBody CustomFilter customFilter,
      @RequestParam int pageNumber,
      @RequestParam int pageSize) {

    return ResponseEntity.ok(
        userManagerService.getUsersFiltered(organizationId, customFilter, pageNumber, pageSize));
  }

  @Override
  @GetMapping("users/roles")
  public ResponseEntity<List<OrganizationUserEmailDto>> getAllUsersRoles(
      @RequestHeader(EMAIL_HEADER) String email) {

    return ResponseEntity.ok(userManagerService.getAllUsersRoles(email));
  }

  @Override
  @GetMapping("users/roles/organization")
  public ResponseEntity<OrganizationUserEmailDto> getUserRole(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email) {

    return ResponseEntity.ok(userManagerService.getUserRole(organizationId, email));
  }

  @Override
  @PostMapping("users/roles/{assigneeEmail}")
  public ResponseEntity<UserDto> createRole(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String adminEmail,
      @RequestParam("assigneeRole") RoleType roleType,
      @PathVariable("assigneeEmail") String email) {

    return ResponseEntity.ok(
        userManagerService.createRole(organizationId, roleType, adminEmail, email));
  }

  @Override
  @DeleteMapping("users/roles/{assigneeEmail}")
  public ResponseEntity<UserDto> deleteRole(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String adminEmail,
      @PathVariable("assigneeEmail") String email) {

    userManagerService.deleteRole(organizationId, adminEmail, email);
    return ResponseEntity.ok().build();
  }

  @Override
  @GetMapping("users/roles/checkIfAdmin")
  public ResponseEntity<Boolean> isAdminWithinOrganization(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String email) {
    return ResponseEntity.ok(userManagerService.isAdminWithinOrganization(organizationId, email));
  }

  @Override
  @PostMapping(path = "users/profile-image", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  public ResponseEntity<UserDto> uploadProfileImage(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String adminEmail,
      @RequestPart MultipartFile file) {

    userManagerService.uploadProfileImage(organizationId, adminEmail, file);

    return null;
  }

  @Override
  @PostMapping(
      path = "users",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<UserDto> addUser(@RequestBody AddUserDTO user) {
    var kcuser = kc.createUser(user.firstName(), user.lastName(), user.email());
    var userprofile = userProfileClient.getUserProfile(user.email());

    if (userprofile != null) {
      userProfileClient.updateUserProfile(
          user.email(),
          userprofile.getProfileId(),
          UserProfileDto.builder()
              .profileId(userprofile.getProfileId())
              .firstName(user.firstName())
              .lastName(user.lastName())
              .email(user.email())
              .build());
    }

    var userDTO =
        UserDto.builder()
            .id(UUID.fromString(kcuser.getId()))
            .firstName(kcuser.getFirstName())
            .lastName(kcuser.getLastName())
            .fullName(kcuser.getFirstName() + " " + kcuser.getLastName())
            .email(kcuser.getEmail())
            .build();

    return ResponseEntity.ok(userDTO);
  }

  @Override
  @PostMapping(
      path = "users/{email}/setup-password",
      consumes = MediaType.APPLICATION_JSON_VALUE,
      produces = MediaType.APPLICATION_JSON_VALUE)
  public ResponseEntity<Void> setupPassword(
      @PathVariable("email") String email, @RequestBody SetupPasswordDTO passwordRequest) {
    kc.addPasswordCredential(email, passwordRequest.password());
    return ResponseEntity.noContent().build();
  }

  // This endpoint is used by organization service here:
  // https://github.com/fathom-io/platform-backend-java/blob/00d1e5c26aed282803286635cf14198dbe3951dc/services/organization/src/main/java/com/fathom/services/organization/service/impl/OrganizationServiceImpl.java#L93
  // This needs to be rethought as this endpoint is vulnerable however it needs to stay because it
  // breaks organization creation
  @Override
  @Hidden
  @PostMapping("users/roles/admin")
  public ResponseEntity<UserDto> createOwnerRole(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(EMAIL_HEADER) String adminEmail) {
    userManagerService.createOwnerRole(organizationId, RoleType.SUPER_ADMIN, adminEmail);

    return ResponseEntity.ok().build();
  }

  @Override
  @DeleteMapping(path = "users/account")
  public ResponseEntity<Void> deleteCurrentUser(@RequestHeader(EMAIL_HEADER) String email) {
    userManagerService.deleteCurrentUser(email);
    return ResponseEntity.noContent().build();
  }
}
