package com.fathom.services.usermanagement.repositories;

import com.fathom.services.usermanagement.model.Group;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface GroupRepository extends JpaRepository<Group, UUID> {
  @Query("select g from Group g where g.organizationId = ?1 and g.name = ?2")
  Group findByOrganizationIdAndName(UUID organizationId, String name);

  @Query("select g from Group g where g.organizationId = ?1 and g.name in ?2")
  List<Group> findByOrganizationIdAndNameIn(UUID organizationId, Collection<String> names);

  Page<Group> findByOrganizationId(UUID organizationId, Pageable pageable);

  void deleteByOrganizationIdAndIdIn(UUID organizationId, Set<UUID> ids);

  @Query(
      "select g from Group g inner join g.users users inner join g.policies policies "
          + "where g.organizationId = ?1 and users.email = ?2 and policies.activationDate <= ?3 and policies.deactivationDate > ?4")
  List<Group>
      findByOrganizationIdAndUsers_EmailAndPolicies_ActivationDateLessThanEqualAndPolicies_DeactivationDateGreaterThan(
          UUID organizationId,
          String email,
          LocalDateTime activationDate,
          LocalDateTime deactivationDate);

  @Query(
      "SELECT g FROM Group g WHERE g.organizationId = :organizationId AND (g.id IN :ids OR g.name IN :names)")
  List<Group> findByOrganizationIdAndIdInOrNameIn(
      @Param("organizationId") UUID organizationId,
      @Param("ids") Collection<UUID> ids,
      @Param("names") Collection<String> names);
}
