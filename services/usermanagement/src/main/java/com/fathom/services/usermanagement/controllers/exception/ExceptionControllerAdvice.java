package com.fathom.services.usermanagement.controllers.exception;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.NoSuchElementException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.naming.AuthenticationException;
import com.fathom.lib.common.exception.RestException;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
@RestControllerAdvice
@Slf4j
public class ExceptionControllerAdvice {

  private static ErrorResponse of(HttpStatus status, String message) {
    return ErrorResponse.builder()
        .timestamp(LocalDateTime.now(ZoneId.of("UTC")))
        .status(status.getReasonPhrase())
        .message(message)
        .build();
  }

  @ExceptionHandler(ServletRequestBindingException.class)
  public ResponseEntity<ErrorResponse> handleServletRequestBindingException(
      ServletRequestBindingException ex) {
    ErrorResponse res = of(HttpStatus.BAD_REQUEST, ex.getMessage());
    return ResponseEntity.badRequest().body(res);
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<ErrorResponse> handleMethodArgumentNotValidException(
      MethodArgumentNotValidException ex) {
    ErrorResponse res = of(HttpStatus.BAD_REQUEST, ex.getMessage());
    return ResponseEntity.badRequest().body(res);
  }

  @ExceptionHandler(RestException.class)
  public ResponseEntity<ErrorResponse> handleRestException(RestException ex) {
    log.error("RestException occurred: {}", ex.getMessage(), ex);
    ErrorResponse res = of(ex.getHttpStatusCode(), ex.getMessage());
    return ResponseEntity.status(ex.getHttpStatusCode()).body(res);
  }

  @ExceptionHandler(NoSuchElementException.class)
  public ResponseEntity<ErrorResponse> handleNoSuchElementException(NoSuchElementException ex) {
    log.error("NoSuchElementException occurred: {}", ex.getMessage(), ex);
    ErrorResponse res = of(HttpStatus.NOT_FOUND, ex.getMessage());
    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(res);
  }

  @ExceptionHandler(AuthenticationException.class)
  public ResponseEntity<ErrorResponse> handleAuthenticationException(AuthenticationException ex) {
    log.error("AuthenticationException occurred: {}", ex.getMessage(), ex);
    ErrorResponse res = of(HttpStatus.UNAUTHORIZED, ex.getMessage());
    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(res);
  }

  @Order(Ordered.HIGHEST_PRECEDENCE)
  @ExceptionHandler(value = {DataIntegrityViolationException.class})
  public ResponseEntity<ErrorResponse> handleConstraintViolation(ConstraintViolationException ex) {
    // Handle the exception and return a custom error message

    String regex = "Key \\(organization_id, name\\)=\\(([^,]+), ([^\\)]+)\\)";
    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(ex.getMessage());

    String organizationId = "";
    String name = "";

    if (matcher.find()) {
      organizationId = matcher.group(1);
      name = matcher.group(2);
    }

    ErrorResponse res =
        of(
            HttpStatus.BAD_REQUEST,
            "Entity with name %s already exist for organization %s. Please, use another name".formatted(
                name, organizationId));
    return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(res);
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<ErrorResponse> handleGenericException(Exception ex) {
    log.error("Unexpected exception occurred: {}", ex.getMessage(), ex);
    // For security reasons, don't expose internal error details in generic exceptions
    ErrorResponse res = of(HttpStatus.INTERNAL_SERVER_ERROR, "An unexpected error occurred");
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(res);
  }

  @Data
  @Builder
  private static class ErrorResponse {

    private final LocalDateTime timestamp;
    private final String status;
    private final String message;
  }
}
