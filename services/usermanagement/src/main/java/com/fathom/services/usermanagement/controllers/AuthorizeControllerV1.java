package com.fathom.services.usermanagement.controllers;

import com.fathom.services.usermanagement.model.dto.AuthorizeDto;
import io.swagger.v3.oas.annotations.Operation;
import java.util.UUID;
import org.springframework.http.ResponseEntity;

public interface AuthorizeControllerV1 {

  @Operation(
      summary = "Endpoint for checking if a user has the right access. ",
      description = "Endpoint for checking a user has the right access. ")
  ResponseEntity<Boolean> authorize(
      UUID organizationId, String projectId, String email, AuthorizeDto authorizeDto);
}
