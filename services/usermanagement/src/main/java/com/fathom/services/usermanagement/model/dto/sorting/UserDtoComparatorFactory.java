package com.fathom.services.usermanagement.model.dto.sorting;

import com.fathom.services.usermanagement.model.dto.UserDto;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.Set;

public class UserDtoComparatorFactory {
    public static Comparator<UserDto> getComparator(String orderBy) {
        return switch (orderBy) {
            case "id" -> Comparator.comparing(UserDto::getId, Comparator.nullsLast(Comparator.naturalOrder()));
            case "email" -> Comparator.comparing(UserDto::getEmail, Comparator.nullsLast(String::compareToIgnoreCase));
            case "firstName" ->
                    Comparator.comparing(UserDto::getFirstName, Comparator.nullsLast(String::compareToIgnoreCase));
            case "lastName" ->
                    Comparator.comparing(UserDto::getLastName, Comparator.nullsLast(String::compareToIgnoreCase));
            case "fullName" ->
                    Comparator.comparing(UserDto::getFullName, Comparator.nullsLast(String::compareToIgnoreCase))
                            .thenComparing(UserDto::getEmail, Comparator.nullsLast(String::compareToIgnoreCase));
            case "role" -> Comparator.comparing(UserDto::getRole, Comparator.nullsLast(String::compareToIgnoreCase));
            case "groups" ->
                    Comparator.comparing(UserDto::getGroups, Comparator.nullsFirst(Comparator.comparingInt(Set::size)));
            case "invitationStatus" ->
                    Comparator.comparing(UserDto::getInvitationStatus, Comparator.nullsLast(String::compareToIgnoreCase));
            case "invitationCreatedDate" ->
                    Comparator.comparing(UserDto::getInvitationCreatedDate, Comparator.nullsLast(LocalDateTime::compareTo));
            case "invitationUpdateDate" ->
                    Comparator.comparing(UserDto::getInvitationUpdateDate, Comparator.nullsLast(LocalDateTime::compareTo));
            case "active" -> Comparator.comparing(UserDto::isActive, Comparator.nullsLast(Boolean::compare));
            default -> throw new IllegalArgumentException("Invalid order by field: " + orderBy);
        };
    }
}
