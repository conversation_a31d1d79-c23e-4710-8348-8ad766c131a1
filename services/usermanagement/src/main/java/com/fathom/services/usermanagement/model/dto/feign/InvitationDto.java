package com.fathom.services.usermanagement.model.dto.feign;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import java.util.Set;
import lombok.Data;

@Data
public class InvitationDto {

  String id;
  String email;

  @JsonProperty("groups")
  Set<String> invitationGroups;

  @JsonProperty("status")
  String invitationStatus;

  @JsonProperty("role")
  String invitationRole;

  LocalDateTime createdDate;
  LocalDateTime updateDate;
}
