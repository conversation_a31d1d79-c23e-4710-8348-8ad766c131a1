package com.fathom.services.usermanagement.repositories;

import com.fathom.services.usermanagement.model.PasswordResetTimestamp;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;

public interface PasswordResetTimestampRepository
    extends JpaRepository<PasswordResetTimestamp, UUID> {
  Optional<PasswordResetTimestamp> findByUserEmail(String userEmail);
}
