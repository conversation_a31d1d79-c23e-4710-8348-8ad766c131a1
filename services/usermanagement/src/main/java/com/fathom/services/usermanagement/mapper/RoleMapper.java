package com.fathom.services.usermanagement.mapper;

import com.fathom.services.usermanagement.model.Role;
import com.fathom.services.usermanagement.model.User;
import com.fathom.services.usermanagement.model.dto.RoleDto;
import com.fathom.services.usermanagement.model.enums.RoleType;
import java.util.UUID;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class RoleMapper {

  @Mapping(target = "organizationId", source = "organizationId")
  @Mapping(target = "roleType", source = "roleType")
  @Mapping(target = "users", source = "users")
  public abstract Role toEntity(UUID organizationId, RoleType roleType, User users);

  @Mapping(target = "email", source = "users.email")
  public abstract RoleDto toDto(Role source);
}
