package com.fathom.services.usermanagement.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;

public enum AccessTypeEnum {
  EDIT("edit"),
  VIEW("view"),
  DELETE("delete");
  private final String value;

  AccessTypeEnum(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return this.value;
  }

  public static AccessTypeEnum fromValue(String value) {
    for (AccessTypeEnum type : AccessTypeEnum.values()) {
      if (type.value.equals(value)) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid AccessType value: " + value);
  }
}
