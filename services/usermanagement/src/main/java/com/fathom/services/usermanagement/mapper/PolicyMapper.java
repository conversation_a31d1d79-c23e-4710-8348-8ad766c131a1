package com.fathom.services.usermanagement.mapper;

import com.fathom.services.usermanagement.model.Policy;
import com.fathom.services.usermanagement.model.dto.PolicyCreateUpdateDto;
import com.fathom.services.usermanagement.model.dto.PolicyDto;
import java.util.UUID;
import org.mapstruct.*;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class PolicyMapper {
  @Mapping(target = "organizationId", source = "organizationId")
  @Mapping(target = "createdBy", source = "email")
  @Mapping(target = "updatedBy", source = "email")
  public abstract Policy toEntity(UUID organizationId, String email, PolicyCreateUpdateDto source);

  public abstract PolicyDto toDto(Policy source);

  public abstract Policy updateDtoToEntity(
      @MappingTarget Policy group, PolicyCreateUpdateDto source);
}
