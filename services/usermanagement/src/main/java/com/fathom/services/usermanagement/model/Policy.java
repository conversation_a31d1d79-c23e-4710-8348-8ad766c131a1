package com.fathom.services.usermanagement.model;

import com.fathom.services.usermanagement.model.enums.AccessTypeEnum;
import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Set;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Cascade;

@Entity
@Getter
@Setter
@Table(
    name = "policies",
    uniqueConstraints = @UniqueConstraint(columnNames = {"organizationId", "name"}))
public class Policy {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  @Column(name = "id", updatable = false, nullable = false)
  UUID id;

  UUID organizationId;
  String name;
  String description;

  @ElementCollection(targetClass = String.class, fetch = FetchType.EAGER)
  @CollectionTable(
      name = "workspaces",
      joinColumns = @JoinColumn(name = "policy_id", referencedColumnName = "id"))
  @Column(name = "workspaces", nullable = false)
  @Cascade({
    org.hibernate.annotations.CascadeType.ALL,
    org.hibernate.annotations.CascadeType.REMOVE
  })
  Set<String> workspaces;

  @ElementCollection(targetClass = AccessTypeEnum.class, fetch = FetchType.EAGER)
  @CollectionTable(
      name = "access_types",
      joinColumns = @JoinColumn(name = "policy_id", referencedColumnName = "id"))
  @Column(name = "access_types", nullable = false)
  @Enumerated(EnumType.STRING)
  @Cascade({
    org.hibernate.annotations.CascadeType.ALL,
    org.hibernate.annotations.CascadeType.REMOVE
  })
  Set<AccessTypeEnum> accessTypes;

  @ElementCollection(targetClass = String.class, fetch = FetchType.EAGER)
  @CollectionTable(
      name = "features",
      joinColumns = @JoinColumn(name = "policy_id", referencedColumnName = "id"))
  @Column(name = "features", nullable = false)
  @Cascade({
    org.hibernate.annotations.CascadeType.ALL,
    org.hibernate.annotations.CascadeType.REMOVE
  })
  Set<String> features;

  String createdBy;
  String updatedBy;

  @ManyToMany(mappedBy = "policies")
  Set<Group> groups;

  LocalDateTime createdDate;
  LocalDateTime updatedDate;
  LocalDate activationDate;
  LocalDate deactivationDate;

  @PrePersist
  private void prePersist() {
    LocalDateTime now = LocalDateTime.now(ZoneId.of("UTC"));
    this.createdDate = now;
    this.updatedDate = now;
  }

  @PreUpdate
  private void preUpdate() {
    LocalDateTime now = LocalDateTime.now(ZoneId.of("UTC"));
    if (this.createdDate == null) {
      this.createdDate = now;
    }

    this.updatedDate = now;
  }
}
