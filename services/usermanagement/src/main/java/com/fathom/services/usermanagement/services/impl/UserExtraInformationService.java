package com.fathom.services.usermanagement.services.impl;

import com.fathom.services.usermanagement.feign.FileClient;
import com.fathom.services.usermanagement.feign.InvitationFeignClient;
import com.fathom.services.usermanagement.feign.UserProfileClient;
import com.fathom.services.usermanagement.model.User;
import com.fathom.services.usermanagement.model.dto.UserDto;
import com.fathom.services.usermanagement.model.dto.feign.FileInfoDto;
import com.fathom.services.usermanagement.model.dto.feign.InvitationDto;
import com.fathom.services.usermanagement.model.dto.feign.TokenFileIdDto;
import com.fathom.services.usermanagement.model.dto.feign.UserProfileDto;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserExtraInformationService {

  private final UserProfileClient userProfileClient;
  private final FileClient fileClient;
  private final InvitationFeignClient invitationFeignClient;

  @Getter private Map<UUID, UserDto> result;
  @Getter private List<UserDto> userNotActivated;

  public UserExtraInformationService(
      UserProfileClient userProfileClient,
      FileClient fileClient,
      InvitationFeignClient invitationFeignClient,
      Map<UUID, UserDto> result,
      List<UserDto> userNotActivated) {
    this.userProfileClient = userProfileClient;
    this.fileClient = fileClient;
    this.invitationFeignClient = invitationFeignClient;
  }

  public void loadExtraUserInformation(UUID organizationId, List<User> userList) {
    result = new ConcurrentHashMap<>();
    userNotActivated = new ArrayList<>();

    // Fetch file information for users
    Set<UUID> userIds = userList.stream().map(User::getId).collect(Collectors.toSet());
    List<FileInfoDto> fileInfoList = fileClient.findFileInformationByUUIDs(userIds);

    // Extract file IDs for token retrieval
    Set<String> fileIds = fileInfoList.stream().map(FileInfoDto::getId).collect(Collectors.toSet());

    log.info("Fetching download tokens for files: {}", String.join(", ", fileIds));

    // Get profile picture download tokens
    List<TokenFileIdDto> tokenFileIdList = fileClient.getProfilePictureDownloadTokens(fileIds);

    // Fetch the latest invitation data for users
    Set<String> userEmails = userList.stream().map(User::getEmail).collect(Collectors.toSet());

    List<InvitationDto> latestInvitations =
        invitationFeignClient.getLatestInvitations(organizationId);
    if (latestInvitations == null) {
      latestInvitations = List.of();
    }

    List<InvitationDto> emailsNotInUserEmailsMap =
        latestInvitations.stream()
            .filter(
                invitation ->
                    invitation != null
                        && invitation.getEmail() != null
                        && !userEmails.contains(invitation.getEmail()))
            .toList();

    Map<String, InvitationDto> invitationMap =
        latestInvitations.stream()
            .filter(invitation -> invitation != null && invitation.getEmail() != null)
            .collect(Collectors.toMap(InvitationDto::getEmail, Function.identity()));

    // Map file UUIDs to download tokens
    Map<UUID, String> profilePictureMap =
        tokenFileIdList.stream()
            .map(
                tokenFile ->
                    fileInfoList.stream()
                        .filter(fileInfo -> fileInfo.getId().equals(tokenFile.getId()))
                        .findFirst()
                        .map(fileInfo -> Pair.of(fileInfo.getObjectUUID(), tokenFile.getToken()))
                        .orElse(null))
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(Pair::getLeft, Pair::getRight));

    // Fetch user profile data
    Map<String, UserProfileDto> userProfileMap =
        Optional.ofNullable(
                userProfileClient.getManyUserProfileByNames(
                    Stream.concat(
                            userEmails.stream(),
                            emailsNotInUserEmailsMap.stream()
                                .map(InvitationDto::getEmail)
                                .toList()
                                .stream())
                        .collect(Collectors.toSet())))
            .orElse(Collections.emptyList()).stream()
            .filter(userProfile -> userProfile != null && userProfile.getEmail() != null)
            .collect(Collectors.toMap(UserProfileDto::getEmail, Function.identity()));

    // Add user that are not activated i.e. with PENDING status in the invitation service
    userNotActivated.addAll(
        emailsNotInUserEmailsMap.stream()
            .map(
                invitationDto -> {
                  UserDto userDto =
                      createUserDto(invitationDto.getEmail(), userProfileMap, invitationMap);
                  userDto.setActive(false);
                  userDto.setGroups(invitationDto.getInvitationGroups());
                  userDto.setRole(invitationDto.getInvitationRole());
                  return userDto;
                })
            .toList());

    // Populate user details into the result map
    result.putAll(
        userList.stream()
            .collect(
                Collectors.toMap(
                    User::getId,
                    user -> {
                      UserDto userDto =
                          createUserDto(user.getEmail(), userProfileMap, invitationMap);
                      userDto.setProfilePictureDownloadLink(profilePictureMap.get(user.getId()));
                      return userDto;
                    })));
  }

  private UserDto createUserDto(
      String email,
      Map<String, UserProfileDto> userProfileMap,
      Map<String, InvitationDto> invitationMap) {

    UserDto userDto = UserDto.builder().build();

    // Set user profile details (first and last name)
    UserProfileDto userProfileDto = userProfileMap.get(email);
    if (Objects.nonNull(userProfileDto)) {
      userDto.setFirstName(userProfileDto.getFirstName());
      userDto.setLastName(userProfileDto.getLastName());
      userDto.setFullName(userProfileDto.getFirstName() + " " + userProfileDto.getLastName());
    }

    // Set invitation details
    InvitationDto invitationDto = invitationMap.get(email);
    if (Objects.nonNull(invitationDto)) {
      userDto.setInvitationStatus(invitationDto.getInvitationStatus());
      userDto.setInvitationId(invitationDto.getId());
      userDto.setInvitationCreatedDate(invitationDto.getCreatedDate());
      userDto.setInvitationUpdateDate(invitationDto.getUpdateDate());
    }

    userDto.setEmail(email);
    return userDto;
  }
}
