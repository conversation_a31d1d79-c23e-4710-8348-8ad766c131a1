package com.fathom.services.usermanagement.controllers;

import com.fathom.services.usermanagement.model.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;

public interface GroupControllerV1 {

  @Operation(
      summary = "Endpoint for getting all groups and users by organization id. ",
      description = "Endpoint for getting all groups and users by organization id. ")
  ResponseEntity<Page<GroupDto>> getGroupsByOrganizationId(
      UUID organizationId, int pageNumber, int pageSize, boolean pageable);

  @Operation(
      summary = "Endpoint for getting a groups by organization id and names. ",
      description = "Endpoint for getting a groups by organization id and names. ")
  ResponseEntity<List<GroupDto>> getGroupsByOrganizationIdAndNames(
      UUID organizationId, Set<String> name);

  @Operation(
      summary = "Endpoint for creating many groups. ",
      description = "Endpoint for creating many groups. ")
  ResponseEntity<List<GroupDto>> createGroups(
      UUID organizationId,
      String email,
      @Valid List<@Valid GroupCreateUpdateDto> groupCreateUpdateDto);

  @Operation(
      summary =
          "Endpoint for overriding members to a group. This change will affect all other existing member by this request body",
      description =
          "Endpoint for overriding members to a group. This change will affect all other existing member by this request body")
  ResponseEntity<GroupDto> overrideMembersInGroup(
      UUID organizationId,
      String email,
      UUID groupId,
      @Valid AddMembersToGroupDto addMembersToGroupDto);

  @Operation(
      summary = "Endpoint for adding new members to a group.",
      description = "Endpoint for adding members to a group.")
  ResponseEntity<GroupDto> addMembersInGroup(
      UUID organizationId,
      String email,
      UUID groupId,
      @Valid AddMembersToGroupDto addMembersToGroupDto);

  @Operation(
      summary = "Endpoint for delete members from a group.",
      description = "Endpoint for delete members from a group.")
  ResponseEntity<GroupDto> deleteMembersInGroup(
      UUID organizationId,
      String email,
      UUID groupId,
      @Valid AddMembersToGroupDto addMembersToGroupDto);

  @Operation(
      summary = "Endpoint for adding many users from to many groups. ",
      description = "Endpoint for adding many users to many groups. ")
  ResponseEntity<List<GroupDto>> addManyUsersToManyGroups(
      UUID organizationId, String email, UserEmailsGroupsDto userEmailsGroupsDto);

  @Operation(
      summary = "Endpoint for updating many users from to many groups. ",
      description = "Endpoint for updating many users to many groups. ")
  ResponseEntity<List<GroupDto>> updateManyUsersInManyGroups(
      UUID organizationId, String email, UserEmailsGroupsDto userEmailsGroupsDto);

  @Operation(
      summary = "Endpoint for deleting many users from many groups. ",
      description = "Endpoint for deleting many users from many groups. ")
  ResponseEntity<List<GroupDto>> deleteManyUsersFromGroups(
      UUID organizationId, String email, UserEmailsGroupsDto userEmailsGroupsDto);

  @Operation(
      summary = "Endpoint for adding policies to a group by policy name within the organization. ",
      description =
          "Endpoint for adding policies to a group by policy name within the organization. ")
  ResponseEntity<GroupDto> addPoliciesToGroup(
      UUID organizationId,
      String email,
      UUID groupId,
      @Valid AddPoliciesToGroupDto addPoliciesToGroupDto);

  @Operation(
      summary = "Endpoint for updating a group. ",
      description = "Endpoint for updating a group. ")
  ResponseEntity<GroupDto> updateGroup(
      UUID organizationId, String email, UUID groupId, GroupCreateUpdateDto groupCreateUpdateDto);

  @Operation(
      summary = "Endpoint for deleting a group. ",
      description = "Endpoint for deleting a group. ")
  ResponseEntity<GroupDto> deleteGroup(UUID organizationId, String email, UUID groupId);

  @Operation(
      summary = "Endpoint for deleting many group. ",
      description = "Endpoint for deleting many group. ")
  ResponseEntity<GroupDto> deleteGroupsByIds(UUID organizationId, String email, Set<UUID> groupIds);

  @Operation(
      summary = "Endpoint for getting policies by organizationId. ",
      description = "Endpoint for getting policies by organizationId. ")
  ResponseEntity<Page<PolicyDto>> getPoliciesByOrganizationId(
      UUID organizationId, int pageNumber, int pageSize, boolean pageable);

  @Operation(
      summary = "Endpoint for creating many policies. ",
      description = "Endpoint for creating many policies. ")
  ResponseEntity<List<PolicyDto>> createPolicies(
      UUID organizationId, String email, List<PolicyCreateUpdateDto> policyCreateUpdateDtoList);

  @Operation(
      summary = "Endpoint for updating a policy. ",
      description = "Endpoint for updating a policy. ")
  ResponseEntity<PolicyDto> updatePolicy(
      UUID organizationId,
      String email,
      UUID policyId,
      PolicyCreateUpdateDto policyCreateUpdateDto);

  @Operation(
      summary = "Endpoint for deleting a policy. ",
      description = "Endpoint for deleting a policy. ")
  ResponseEntity<PolicyDto> deletePolicy(UUID organizationId, String email, UUID policyId);

  @Operation(
      summary = "Endpoint for deleting many policies. ",
      description = "Endpoint for deleting many policies. ")
  ResponseEntity<PolicyDto> deletePolicies(UUID organizationId, String email, Set<UUID> policyIds);
}
