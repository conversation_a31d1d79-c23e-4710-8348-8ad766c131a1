package com.fathom.services.usermanagement.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Set;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(
    name = "groups",
    uniqueConstraints = @UniqueConstraint(columnNames = {"organizationId", "name"}))
public class Group {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  @Column(name = "id", updatable = false, nullable = false)
  private UUID id;

  UUID organizationId;
  String name;
  String description;
  LocalDateTime createdDate;
  String creator;

  @ManyToMany(fetch = FetchType.EAGER)
  @JsonIgnore
  @JoinTable(
      name = "users_groups",
      joinColumns = @JoinColumn(name = "user_id", referencedColumnName = "id"),
      inverseJoinColumns = @JoinColumn(name = "group_id", referencedColumnName = "id"))
  Set<User> users;

  @ManyToMany(fetch = FetchType.EAGER)
  @JsonIgnore
  @JoinTable(
      name = "roles_privileges",
      joinColumns = @JoinColumn(name = "role_id", referencedColumnName = "id"),
      inverseJoinColumns = @JoinColumn(name = "privilege_id", referencedColumnName = "id"))
  Set<Policy> policies;

  @PrePersist
  private void prePersist() {
    this.createdDate = LocalDateTime.now(ZoneId.of("UTC"));
  }
}
