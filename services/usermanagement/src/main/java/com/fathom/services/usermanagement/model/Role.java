package com.fathom.services.usermanagement.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fathom.services.usermanagement.model.enums.RoleType;
import jakarta.persistence.*;
import java.util.UUID;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(
    name = "roles",
    uniqueConstraints = @UniqueConstraint(columnNames = {"organizationId", "user_id"}))
public class Role {

  @Id
  @GeneratedValue(strategy = GenerationType.UUID)
  @Column(name = "id", updatable = false, nullable = false)
  private UUID id;

  UUID organizationId;
  RoleType roleType;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "user_id", referencedColumnName = "id")
  @JsonIgnore
  User users;
}
