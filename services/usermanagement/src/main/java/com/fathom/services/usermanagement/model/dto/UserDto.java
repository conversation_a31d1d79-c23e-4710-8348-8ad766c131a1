package com.fathom.services.usermanagement.model.dto;

import java.time.LocalDateTime;
import java.util.*;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class UserDto {

  UUID id;
  String email;
  String firstName;
  String lastName;
  String fullName;
  String profilePictureDownloadLink;
  String role;
  boolean active = true;

  Set<String> groups;

  String invitationId;
  String invitationStatus;
  LocalDateTime invitationCreatedDate;
  LocalDateTime invitationUpdateDate;
}
