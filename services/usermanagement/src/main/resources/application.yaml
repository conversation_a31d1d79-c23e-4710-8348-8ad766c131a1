server:
  port: ${USER_MANAGEMENT_SERVICE_PORT:8059}

logging:
  level:
    root: INFO
    org:
      springframework:
        boot:
          autoconfigure:
            web:
              reactive: WARN
        cloud:
          gateway: WARN
spring:
  application:
    name: ${USER_MANAGEMENT_SERVICE_NAME:usermanagement}
  flyway:
    enabled: ${FLYWAY_MIGRATION_ENABLED:false}
    locations: classpath:db/migration
    url: ${spring.datasource.url}
    user: ${spring.datasource.username}
    password: ${spring.datasource.password}
  datasource:
    url: jdbc:postgresql://${POSTGRES_HOST:localhost}:${POSTGRES_PORT:5432}/${POSTGRES_DATABASE:fathom}?currentSchema=${AM_SCHEMA:usermanagement}
    username: ${POSTGRES_USER:postgres}
    password: ${POSTGRES_PASSWORD:postgres}
  jpa:
    open-in-view: false
    show-sql: false
    database: postgresql
    hibernate:
      ddl-auto: update
  cloud:
    loadbalancer:
      ribbon:
        enabled: false
    kubernetes:
      loadbalancer:
        mode: service
      discovery:
        all-namespaces: false
  sql:
    init:
      mode: always
      schema-locations: classpath:schema-postgres.sql
  mvc:
    static-path-pattern: /static/**

services:
  director:
    name: ${DIRECTOR_SERVICE_NAME:director:8060}
  userprofile:
    name: ${USER_PROFILE_SERVICE_NAME:userprofile:8089}
  fileservice:
    name: ${FILE_SERVICE_NAME:fileservice:8015}
  invitation:
    name: ${INVITATION_SERVICE_NAME:invitation:8101}

keycloak:
  auth-server-url: ${KEYCLOAK_AUTH_SERVER_URL:http://keycloak.keycloak.svc.cluster.local}
  realm: ${KEYCLOAK_REALM:FathomDevRealm}
  client-id: ${KEYCLOAK_CLIENT_ID:admin-cli}
  client-secret: ${KEYCLOAK_CLIENT_SECRET:supersecret}

hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 10000
      circuitBreaker:
        requestVolumeThreshold: 5
        errorThresholdPercentage: 50
        sleepWindowInMilliseconds: 10000
      metrics:
        rollingStats:
          timeInMilliseconds: 10000

springdoc:
  api-docs:
    version: "openapi_3_0"

diagnostics:
  service-name: "usermanagement"
  version: ${VERSION:0.0.1-dev}
  build-timestamp: ${BUILD_TIMESTAMP:1970-01-01T00:00:00Z}
  git-hash: ${GIT_HASH:unknown}
  git-branch: ${GIT_BRANCH:main}
  git-commit-timestamp: ${GIT_COMMIT_TIMESTAMP:1970-01-01T00:00:00Z}