spring:
  cloud:
    discovery:
      client:
        simple:
          instances:
            director:
              - uri: ${DIRECTOR_SERVICE_NAME:http://localhost:8060/}
            userprofile:
              - uri: ${USER_PROFILE_SERVICE_NAME:http://localhost:8089/}
            fileservice:
              - uri: ${FILE_SERVICE_NAME:http://localhost:8015/}
            keycloak:
              - uri: ${KEYCLOAK_SERVICE_NAME:http://localhost:3300/}
            invitation:
              - uri: ${INVITATION_SERVICE_NAME:http://localhost:8101/}
    loadbalancer:
      ribbon:
        enabled: false
    kubernetes:
      enabled: false
      loadbalancer:
        enabled: false
      discovery:
        enabled: false
feign:
  hystrix:
    enabled: false