<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fathom</groupId>
    <artifactId>services</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <groupId>com.fathom.services</groupId>
  <artifactId>usermanagement</artifactId>
  <version>0.0.1</version>
  <name>usermanagement</name>
  <description>Fathom user management service</description>

  <properties>
    <java.version>17</java.version>
    <lombok.version>1.18.32</lombok.version>
    <org-mapstruct.version>1.6.3</org-mapstruct.version>
    <flyway-maven-plugin.version>10.18.0</flyway-maven-plugin.version>
  </properties>

  <dependencies>
    <!-- Spring-->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
      <version>2.7.0</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <!-- Spring Data Jpa -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
    </dependency>
    <dependency>
      <groupId>org.flywaydb</groupId>
      <artifactId>flyway-maven-plugin</artifactId>
      <version>${flyway-maven-plugin.version}</version>
    </dependency>
    <!-- Fathom -->
    <dependency>
      <groupId>com.fathom.lib</groupId>
      <artifactId>common</artifactId>
      <version>5.2.4</version>
    </dependency>
    <!-- Cloud -->
    <dependency>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
      <groupId>org.springframework.cloud</groupId>
      <exclusions>
        <exclusion>
          <groupId>commons-fileupload</groupId>
          <artifactId>commons-fileupload</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-kubernetes-fabric8-all</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>${org-mapstruct.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-loadbalancer</artifactId>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>
    <dependency>
      <groupId>jakarta.validation</groupId>
      <artifactId>jakarta.validation-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.keycloak</groupId>
      <artifactId>keycloak-admin-client</artifactId>
      <version>26.0.5</version>
    </dependency>

    <!--Test-->
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.10.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>postgresql</artifactId>
      <version>1.20.6</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>mongodb</artifactId>
      <version>1.20.6</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>minio</artifactId>
      <version>1.20.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.github.dasniko</groupId>
      <artifactId>testcontainers-keycloak</artifactId>
      <version>3.6.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>1.20.5</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.26.3</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.wiremock</groupId>
      <artifactId>wiremock-standalone</artifactId>
      <version>3.6.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.wiremock.integrations.testcontainers</groupId>
      <artifactId>wiremock-testcontainers-module</artifactId>
      <version>1.0-alpha-13</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.rest-assured</groupId>
      <artifactId>rest-assured</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.fathom.lib</groupId>
      <artifactId>advancedfilters</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fathom.lib</groupId>
      <artifactId>common</artifactId>
      <version>5.2.4</version>
    </dependency>
    <dependency>
      <groupId>com.fathom</groupId>
      <artifactId>diagnostics</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.openrewrite.maven</groupId>
        <artifactId>rewrite-maven-plugin</artifactId>
        <version>5.45.1</version>
        <configuration>
          <activeRecipes>
            <recipe>org.openrewrite.java.spring.boot3.UpgradeSpringBoot_3_3</recipe>
          </activeRecipes>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.openrewrite.recipe</groupId>
            <artifactId>rewrite-spring</artifactId>
            <version>5.23.2</version>
          </dependency>
        </dependencies>
      </plugin>
      <plugin>
        <groupId>com.coveo</groupId>
        <artifactId>fmt-maven-plugin</artifactId>
        <version>2.9</version>
        <configuration>
          <sourceDirectory>src/main/java</sourceDirectory>
          <testSourceDirectory>src/test/java</testSourceDirectory>
          <verbose>false</verbose>
          <filesNamePattern>.*\.java</filesNamePattern>
          <skip>false</skip>
          <skipSortingImports>false</skipSortingImports>
          <style>google</style>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <groupId>org.springframework.boot</groupId>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <annotationProcessorPaths>
            <path>
              <artifactId>lombok</artifactId>
              <groupId>org.projectlombok</groupId>
              <version>${lombok.version}</version>
            </path>
            <path>
              <artifactId>mapstruct-processor</artifactId>
              <groupId>org.mapstruct</groupId>
              <version>${org-mapstruct.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
        <groupId>org.apache.maven.plugins</groupId>
      </plugin>
      <plugin>
        <groupId>com.spotify</groupId>
        <artifactId>dockerfile-maven-plugin</artifactId>
        <version>1.4.13</version>
        <configuration>
          <repository>${project.artifactId}</repository>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <id>nexus</id>
      <name>Nexus repository</name>
      <url>https://nexus.fathom-solutions.com/repository/maven-public/</url>
    </repository>
  </repositories>
</project>