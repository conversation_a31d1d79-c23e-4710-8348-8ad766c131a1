<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <!-- Basic Metadata -->
  <groupId>com.fathom.services</groupId>
  <artifactId>projects</artifactId>
  <version>0.0.3-SNAPSHOT</version>
  <name>projects</name>
  <description>Fathom project service</description>

  <parent>
    <groupId>com.fathom</groupId>
    <artifactId>services</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>

  <!-- Properties -->
  <properties>
    <java.version>17</java.version>
    <lombok.version>1.18.20</lombok.version>
    <org-mapstruct.version>1.4.1.Final</org-mapstruct.version>
  </properties>

  <!-- Repositories -->
  <repositories>
    <repository>
      <id>nexus</id>
      <name>Nexus repository</name>
      <url>https://nexus.fathom-solutions.com/repository/maven-public/</url>
    </repository>
  </repositories>

  <!-- Dependencies -->
  <dependencies>
    <!-- Spring Boot -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-mongodb</artifactId>
    </dependency>

    <!-- Documentation -->
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
      <version>2.7.0</version>
    </dependency>

    <!-- Cloud -->
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
      <exclusions>
        <exclusion>
          <groupId>commons-fileupload</groupId>
          <artifactId>commons-fileupload</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-kubernetes-fabric8-all</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-loadbalancer</artifactId>
    </dependency>

    <!-- Fathom -->
    <dependency>
      <groupId>com.fathom.lib</groupId>
      <artifactId>filters</artifactId>
      <version>2.1.0</version>
    </dependency>

    <!-- Annotations -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>

    <!-- Tests -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- Mapping -->
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <version>${org-mapstruct.version}</version>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>

    <!-- Validation -->
    <dependency>
      <groupId>jakarta.validation</groupId>
      <artifactId>jakarta.validation-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fathom</groupId>
      <artifactId>diagnostics</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
  </dependencies>

  <!-- Build -->
  <build>
    <plugins>
      <!-- Rewrite Plugin -->
      <plugin>
        <groupId>org.openrewrite.maven</groupId>
        <artifactId>rewrite-maven-plugin</artifactId>
        <version>5.45.1</version>
        <configuration>
          <activeRecipes>
            <recipe>org.openrewrite.java.spring.boot3.UpgradeSpringBoot_3_3</recipe>
          </activeRecipes>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>org.openrewrite.recipe</groupId>
            <artifactId>rewrite-spring</artifactId>
            <version>5.23.2</version>
          </dependency>
        </dependencies>
      </plugin>

      <!-- Format Plugin -->
      <plugin>
        <groupId>com.spotify.fmt</groupId>
        <artifactId>fmt-maven-plugin</artifactId>
        <version>2.25</version>
      </plugin>

      <!-- Spring Boot Maven Plugin -->
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>

      <!-- Maven Compiler Plugin -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <annotationProcessorPaths>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${org-mapstruct.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>