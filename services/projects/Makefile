# -*- MakeFile -*-
export VERSION := 0.1
export ARTIFACT_ID := projects
export MY_SERVICE := $(shell .\mvnw -q -Dexec.executable="echo" -Dexec.args='$${project.version}' --non-recursive exec:exec)
export JAR_NAME := projects-*.jar

.PHONY: info
info:
	wget https://fathomsjenkinsdeployment.s3.eu-central-1.amazonaws.com/scripts/Java/buildBanner.sh
	wget https://fathomsjenkinsdeployment.s3.eu-central-1.amazonaws.com/scripts/Java/getAppProp.sh
	wget https://fathomsjenkinsdeployment.s3.eu-central-1.amazonaws.com/scripts/Java/getLastCommit.sh
	wget https://fathomsjenkinsdeployment.s3.eu-central-1.amazonaws.com/scripts/Java/getVersion.sh
	chmod +x buildBanner.sh getAppProp.sh getLastCommit.sh getVersion.sh
	./buildBanner.sh

.PHONY: clean
clean: mvnw mvnw.cmd info
	.\mvnw clean

.PHONY: compile
compile: mvnw mvnw.cmd
	.\mvnw compile

.PHONY: package
package: compile
	.\mvnw package -Dmaven.test.skip=true

.PHONY: repackage
repackage: clean package

.PHONY: build-artifact
build-artifact:
	echo "Build myservice service jar"
	.\mvnw clean install

.PHONY: build-image
build-image:
	echo "Build myservice image"
	skaffold build -f ./ops/skaffold.yaml --file-output=./target/images.json

.PHONY: deploy-service
deploy-service:
	echo "Deploy myservice service"
	skaffold deploy -f ./ops/skaffold.yaml --build-artifacts=./target/images.json

.PHONY: run-service
run-service:
	echo "Run myservice service"
	skaffold run -f ./ops/skaffold.yaml

.PHONY: debug-service
debug-service:
	echo "Debug myservice service"
	skaffold debug -f ./ops/skaffold.yaml

.PHONY: delete-service
delete-service:
	echo "Delete myservice service"
	skaffold delete -f ./ops/skaffold.yaml

.PHONY: deploy-dependencies
deploy-dependencies:
	echo "Deploy myservice dependencies"
	skaffold deploy -f ./ops/skaffold-dependencies.yaml

.PHONY: deploy-service-with-dependencies
deploy-service-with-dependencies: build-artifact build-image deploy-dependencies deploy-service

.PHONY: run-dependencies
run-dependencies:
	echo "Run myservice dependencies"
	skaffold run -f ./ops/skaffold-dependencies.yaml

.PHONY: run-service-with-dependencies
run-service-with-dependencies: run-dependencies run-service

.PHONY: debug-service-with-dependencies
debug-service-with-dependencies: deploy-dependencies debug-service

.PHONY: delete-dependencies
delete-dependencies:
	echo "Delete myservice dependencies"
	skaffold delete -f ./ops/skaffold-dependencies.yaml

.PHONY: delete-service-with-dependencies
delete-service-with-dependencies: delete-service delete-dependencies

.PHONY: docker
docker:
	docker build -t b4ft1bt3.gra7.container-registry.ovh.net/library/$(ARTIFACT_ID):$(VERSION) --build-arg JAR_PATH="target/$(JAR_NAME)" .

.PHONY: docker-latest
docker-latest:
	docker tag b4ft1bt3.gra7.container-registry.ovh.net/library/$(ARTIFACT_ID):$(VERSION) b4ft1bt3.gra7.container-registry.ovh.net/library/$(ARTIFACT_ID):latest

.PHONY: docker-nexus
docker-nexus:
	docker push b4ft1bt3.gra7.container-registry.ovh.net/library/$(ARTIFACT_ID):latest