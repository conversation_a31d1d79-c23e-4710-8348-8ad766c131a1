server:
  port: ${PROJECT_SERVICE_PORT:8055}

logging:
  level:
    root: INFO
    org:
      springframework:
        boot:
          autoconfigure:
            web:
              reactive: WARN
        cloud:
          gateway: WARN
  pattern:
    # Logging pattern containing traceId and spanId; no longer provided through Sleuth by default
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"

spring:
  application:
    name: ${PROJECT_SERVICE_NAME:projects}
  kafka:
    bootstrap-servers: ${KAFKA_BROKER_LIST:localhost:29092}
  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017}
      database: ${MONGODB_DATABASE:dev}
      auto-index-creation: true
  cloud:
    loadbalancer:
      ribbon:
        enabled: false
    kubernetes:
      loadbalancer:
        mode: service
      discovery:
        all-namespaces: false

kubeflowworkspacemanager:
  service:
    name: ${KUBEFLOWWORKSPACEMANAGER_SERVICE_NAME:kubeflow-workspace-manager-api.kwm:8080}

airbyte:
  service:
    name: ${AIRBYTE_SERVICE_NAME:airbyte}
  config:
    destination:
      minio:
        destinationDefinitionKey: S3
        host:
        bucket:
          name: ${AIRBYTE_BUCKET_NAME:airbyte-data}
          path: ${AIRBYTE_BUCKET_PATH:projects}
        user:
        password:
      delta:
        destinationDefinitionKey: ${DELTA_DESTINATION_DEFINITION_KEY:delta-lake-harbor}
        url: ${DELTA_HOST}
        user: ${DELTA_USER}
        password: ${DELTA_PASSWORD}
        bucket-url: ${DELTA_BUCKET:s3a://rawdata/}
        table-format: ${DELTA_TABLE_FORMAT:non-partitioned}
        compression-mode: ${DELTA_COMPRESSION_MODE:gzip}
        rawServiceUrl: ${RAW_SERVICE_URL:http://dev-k8s.fathom-solutions.com:30004/}
fathom:
  persistent:
    events:
      organization:
        kafka-topic-name: ${PERSISTENT_EVENTS_ORGANIZATION_KAFKA_TOPIC_NAME:fathom.organization.persistent.events}

springdoc:
  api-docs:
    version: "openapi_3_0"

diagnostics:
  service-name: "projects"
  version: ${VERSION:0.0.1-dev}
  build-timestamp: ${BUILD_TIMESTAMP:1970-01-01T00:00:00Z}
  git-hash: ${GIT_HASH:unknown}
  git-branch: ${GIT_BRANCH:main}
  git-commit-timestamp: ${GIT_COMMIT_TIMESTAMP:1970-01-01T00:00:00Z}