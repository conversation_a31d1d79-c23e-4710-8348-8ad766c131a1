package com.fathom.services.projects.mapper;

import com.fathom.services.projects.models.Project;
import com.fathom.services.projects.models.dto.airbyte.workspace.AirbyteWorkspace;
import com.fathom.services.projects.models.dto.project.ProjectCreateDto;
import com.fathom.services.projects.models.dto.project.ProjectDto;
import com.fathom.services.projects.models.dto.project.ProjectUpdateDto;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.UUID;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class ProjectMapper {

  @Mapping(target = "organizationId", source = "organizationId")
  @Mapping(target = "name", source = "workspace.name")
  @Mapping(target = "airbyteWorkspaceId", source = "workspace.workspaceId")
  @Mapping(target = "ownerEmail", source = "email")
  public abstract Project toEntityForDataType(
      UUID organizationId, String email, ProjectCreateDto source, AirbyteWorkspace workspace);

  @Mapping(target = "organizationId", source = "organizationId")
  @Mapping(target = "ownerEmail", source = "email")
  public abstract Project toEntity(UUID organizationId, String email, ProjectCreateDto dto);

  @Mapping(target = "lastAccessed", ignore = true)
  public abstract ProjectDto toDto(Project source);

  public abstract Project updateDtoToEntity(
      @MappingTarget Project project, ProjectUpdateDto projectUpdateDto);

  @AfterMapping
  protected void updateUpdatedTime(@MappingTarget Project project) {
    project.setUpdatedTime(ZonedDateTime.now(ZoneId.of("UTC")).toLocalDateTime());
    project.setLastAccessed(ZonedDateTime.now(ZoneId.of("UTC")).toLocalDateTime());
  }
}
