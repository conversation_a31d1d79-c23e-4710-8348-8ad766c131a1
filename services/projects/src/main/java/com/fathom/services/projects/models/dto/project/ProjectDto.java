package com.fathom.services.projects.models.dto.project;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fathom.services.projects.models.enums.ProjectType;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.UUID;
import lombok.Data;

@Data
public class ProjectDto {

  String id;
  UUID organizationId;
  String clientId;
  String name;
  String displayName;
  ProjectType projectType;
  String description;
  HashMap<String, Object> metadata;
  HashMap<ProjectType, String> linkedProjects;
  Date createdTime;
  Date updatedTime;

  @JsonInclude(Include.NON_NULL)
  UUID airbyteWorkspaceId;

  @JsonInclude(Include.NON_NULL)
  Long kubeflowWorkspaceId;

  boolean isArchived;

  @JsonInclude(Include.NON_NULL)
  String ownerEmail;

  @JsonInclude(Include.NON_NULL)
  LocalDateTime lastAccessed;
}
