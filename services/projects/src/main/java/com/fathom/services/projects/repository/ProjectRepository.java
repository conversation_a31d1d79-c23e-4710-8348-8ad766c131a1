package com.fathom.services.projects.repository;

import com.fathom.services.projects.models.Project;
import com.fathom.services.projects.models.enums.ProjectType;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ProjectRepository extends MongoRepository<Project, String> {

  Optional<Page<Project>> findByOrganizationId(UUID organizationId, Pageable pageable);

  Optional<Page<Project>> findByOrganizationIdAndProjectType(
      UUID organizationId, ProjectType projectType, Pageable pageable);

  Optional<Project> findByIdOrClientId(String id, String clientId);

  Optional<Project> findByOrganizationIdAndNameAndProjectType(
      UUID organizationId, String name, ProjectType projectType);

  boolean existsByOrganizationIdAndNameAndProjectType(
      UUID organizationId, String name, ProjectType projectType);

  List<Project> findByOrganizationIdAndIdIn(UUID organizationId, Set<String> ids);
}
