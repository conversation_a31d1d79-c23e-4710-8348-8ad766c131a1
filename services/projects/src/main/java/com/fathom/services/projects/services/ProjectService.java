package com.fathom.services.projects.services;

import com.fathom.services.projects.models.dto.project.ProjectCreateDto;
import com.fathom.services.projects.models.dto.project.ProjectDto;
import com.fathom.services.projects.models.dto.project.ProjectUpdateDto;
import com.fathom.services.projects.models.enums.ProjectType;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ProjectService {

  ProjectDto createProject(UUID organizationId, String email, ProjectCreateDto project);

  Page<ProjectDto> getProjects(UUID organizationId, ProjectType projectType, Pageable pageable);

  ProjectDto getProjectByID(String id);

  ProjectDto updateProject(UUID organizationId, ProjectUpdateDto projectUpdateDto);

  ProjectDto getByProjectByIds(String id);

  ProjectDto getByProjectByNameAndProjectType(
      UUID organizationId, String name, ProjectType projectType);

  List<ProjectDto> findByOrganizationIdAndIdIn(UUID organizationId, Set<String> ids);

  void deleteProjectById(String id);
}
