package com.fathom.services.projects.models.dto.airbyte.workspace;

import java.util.List;
import lombok.Data;

@Data
public class AirbyteWorkspaceCreateDto {

  String email;
  boolean anonymousDataCollection;
  String name;
  boolean news;
  boolean securityUpdates;
  List<AirbyteNotification> notifications;
  boolean displaySetupWizard;

  public AirbyteWorkspaceCreateDto(String email, String name) {
    this.email = email;
    this.name = name;
    this.notifications = List.of();
    this.anonymousDataCollection = false;
    this.news = false;
    this.securityUpdates = false;
    this.displaySetupWizard = false;
  }
}
