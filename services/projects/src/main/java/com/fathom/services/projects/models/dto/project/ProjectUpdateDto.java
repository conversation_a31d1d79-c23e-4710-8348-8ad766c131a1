package com.fathom.services.projects.models.dto.project;

import com.fathom.services.projects.models.enums.ProjectType;
import java.util.HashMap;
import lombok.Data;

@Data
public class ProjectUpdateDto {

  String id;
  String clientId;
  String displayName;
  String description;
  HashMap<String, Object> metadata;
  HashMap<ProjectType, String> linkedProjects;
  boolean isArchived;
  String projectIcon;
}
