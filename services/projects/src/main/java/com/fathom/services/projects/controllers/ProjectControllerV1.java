package com.fathom.services.projects.controllers;

import com.fathom.services.projects.models.dto.project.ProjectCreateDto;
import com.fathom.services.projects.models.dto.project.ProjectDto;
import com.fathom.services.projects.models.dto.project.ProjectUpdateDto;
import com.fathom.services.projects.models.enums.ProjectType;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;

public interface ProjectControllerV1 {

  @Operation(
      summary = "Endpoint for creating a resource",
      description =
          "Endpoint for creating a resource. It create an airbyte workspace and a airbyte destination "
              + "along with the resource. Possible project types are: intelligence, data, application and digitaltwin")
  ResponseEntity<ProjectDto> createProject(
      UUID organizationId, String email, ProjectCreateDto project);

  @Operation(
      summary = "Endpoint for getting all resources by organization id",
      description = "Endpoint for getting all resources by organization id")
  ResponseEntity<Page<ProjectDto>> getProjects(
      UUID organizationId, ProjectType projectType, int pageNumber, int pageSize);

  @Operation(
      summary = "Endpoint for getting a resource by id",
      description = "Endpoint for getting a resource by id")
  ResponseEntity<ProjectDto> getProjectById(String id);

  @Operation(
      summary = "Endpoint for getting a resource by name and project type",
      description = "Endpoint for getting a resource by name and project type")
  ResponseEntity<ProjectDto> getByProjectByNameAndProjectType(
      UUID organizationId, String name, ProjectType projectType);

  @Operation(
      summary = "Endpoint for updating an existing resource",
      description = "Endpoint for updating an existing resource")
  ResponseEntity<ProjectDto> updateProject(UUID organizationId, ProjectUpdateDto projectUpdateDto);

  @Operation(
      summary = "Endpoint for get a resource by id or by clientId whichever passed",
      description = "Endpoint for get a resource by id or by clientId whichever passed")
  ResponseEntity<ProjectDto> getByProjectByIds(String id);

  @Operation(
      summary = "Fetch projects by a set of IDs",
      description =
          "This endpoint allows you to retrieve a list of projects by providing an organization id and a set of project IDs.")
  List<ProjectDto> findByOrganizationIdAndIdIn(UUID organizationId, Set<String> ids);

  @Operation(
      summary = "Endpoint for deleting a resource by id",
      description = "Endpoint for deleting a resource by id")
  ResponseEntity<Void> deleteProjectById(String id);
}
