package com.fathom.services.projects.models.dto.airbyte.destination;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.UUID;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class DeltaLakeDestination {

  @JsonInclude(Include.NON_NULL)
  UUID destinationId;

  @JsonInclude(Include.NON_NULL)
  UUID destinationDefinitionId;

  @JsonInclude(Include.NON_NULL)
  UUID workspaceId;

  DeltaConnectionConfiguration connectionConfiguration;
  String name;

  @Data
  @Builder
  public static class DeltaConnectionConfiguration {
    @JsonProperty("delta_lake_url")
    String deltaLakeUrl;

    @JsonProperty("raw_service_url")
    String rawServiceUrl;

    @JsonProperty("delta_lake_access_key")
    String deltaLakeAccessKey;

    @JsonProperty("delta_lake_bucket_url")
    String deltaLakeBucketUrl;

    @JsonProperty("delta_lake_project_id")
    String deltaLakeProjectId;

    @JsonProperty("delta_lake_secret_key")
    String deltaLakeSecretKey;

    @JsonProperty("delta_lake_table_format")
    String deltaLakeTableFormat;

    @JsonProperty("delta_lake_organization_id")
    UUID deltaLakeOrganizationId;

    @JsonProperty("delta_lake_compression_mode")
    String deltaLakeCompressionMode;
  }
}
