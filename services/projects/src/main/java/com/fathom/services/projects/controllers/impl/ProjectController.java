package com.fathom.services.projects.controllers.impl;

import com.fathom.services.projects.controllers.ProjectControllerV1;
import com.fathom.services.projects.models.dto.project.ProjectCreateDto;
import com.fathom.services.projects.models.dto.project.ProjectDto;
import com.fathom.services.projects.models.dto.project.ProjectUpdateDto;
import com.fathom.services.projects.models.enums.ProjectType;
import com.fathom.services.projects.services.ProjectService;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
public class ProjectController implements ProjectControllerV1 {

  private static final String ORGANIZATION_HEADER = "x-organizationId";
  public static final String EMAIL_HEADER = "x-email";

  private final ProjectService projectService;

  @Override
  @PostMapping("/projects")
  public ResponseEntity<ProjectDto> createProject(
      @RequestHeader(value = ORGANIZATION_HEADER) UUID organizationId,
      @RequestHeader(value = EMAIL_HEADER) String email,
      @RequestBody @Valid ProjectCreateDto project) {
    return ResponseEntity.ok(projectService.createProject(organizationId, email, project));
  }

  @Override
  @GetMapping("/projects")
  public ResponseEntity<Page<ProjectDto>> getProjects(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestParam(required = false) ProjectType projectType,
      @RequestParam int pageNumber,
      @RequestParam int pageSize) {
    return ResponseEntity.ok(
        projectService.getProjects(
            organizationId, projectType, PageRequest.of(pageNumber - 1, pageSize)));
  }

  @Override
  @GetMapping("/projects/{id}")
  public ResponseEntity<ProjectDto> getProjectById(@PathVariable String id) {
    return ResponseEntity.ok(projectService.getProjectByID(id));
  }

  @Override
  @GetMapping("/projects/byName/{name}")
  public ResponseEntity<ProjectDto> getByProjectByNameAndProjectType(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @PathVariable String name,
      @RequestParam ProjectType projectType) {
    return ResponseEntity.ok(
        projectService.getByProjectByNameAndProjectType(organizationId, name, projectType));
  }

  @Override
  @PutMapping("/projects")
  public ResponseEntity<ProjectDto> updateProject(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId,
      @RequestBody ProjectUpdateDto projectUpdateDto) {
    return ResponseEntity.ok(projectService.updateProject(organizationId, projectUpdateDto));
  }

  @Override
  @GetMapping("/projects/byIDs/{id}")
  public ResponseEntity<ProjectDto> getByProjectByIds(@PathVariable String id) {
    return ResponseEntity.ok(projectService.getByProjectByIds(id));
  }

  @Override
  @PostMapping("projects/byIDs")
  public List<ProjectDto> findByOrganizationIdAndIdIn(
      @RequestHeader(ORGANIZATION_HEADER) UUID organizationId, @RequestBody Set<String> ids) {
    return projectService.findByOrganizationIdAndIdIn(organizationId, ids);
  }

  @Override
  @DeleteMapping("/projects/{id}")
  public ResponseEntity<Void> deleteProjectById(@PathVariable String id) {
    projectService.deleteProjectById(id);
    return ResponseEntity.ok().build();
  }
}
