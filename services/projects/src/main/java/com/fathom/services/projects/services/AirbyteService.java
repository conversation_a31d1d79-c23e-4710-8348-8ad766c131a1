package com.fathom.services.projects.services;

import com.fathom.services.projects.models.Project;
import com.fathom.services.projects.models.dto.airbyte.destination.DeltaLakeDestination;
import com.fathom.services.projects.models.dto.airbyte.workspace.AirbyteWorkspace;
import java.util.UUID;
import org.apache.commons.lang3.tuple.Pair;

public interface AirbyteService {

  Pair<AirbyteWorkspace, DeltaLakeDestination> initializeWorkspace(
      UUID organisationId, String email, String name);

  void updateDestinationProjectId(Project project, DeltaLakeDestination deltaLakeDestination);
}
