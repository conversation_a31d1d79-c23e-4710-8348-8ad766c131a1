package com.fathom.services.projects.services.impl;

import com.fathom.services.projects.config.AirbyteProperties;
import com.fathom.services.projects.feign.AirbyteClient;
import com.fathom.services.projects.models.Project;
import com.fathom.services.projects.models.dto.airbyte.definition.DestinationDefinitionDto;
import com.fathom.services.projects.models.dto.airbyte.destination.DeltaLakeDestination;
import com.fathom.services.projects.models.dto.airbyte.destination.DeltaLakeDestination.DeltaConnectionConfiguration;
import com.fathom.services.projects.models.dto.airbyte.workspace.AirbyteWorkspace;
import com.fathom.services.projects.models.dto.airbyte.workspace.AirbyteWorkspaceCreateDto;
import com.fathom.services.projects.services.AirbyteService;
import com.fathom.services.projects.util.ExMessage;
import java.util.NoSuchElementException;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class AirbyteServiceImpl implements AirbyteService {

  private final AirbyteClient airbyteClient;
  private final AirbyteProperties airbyteProperties;

  @Override
  @Transactional
  public Pair<AirbyteWorkspace, DeltaLakeDestination> initializeWorkspace(
      UUID organisationId, String email, String name) {
    AirbyteWorkspace workspace =
        airbyteClient.createWorkspace(new AirbyteWorkspaceCreateDto(email, name));

    DeltaLakeDestination deltaLakeDestination = getDeltaLakeDestination(organisationId, workspace);
    deltaLakeDestination = airbyteClient.createDestination(deltaLakeDestination);

    return Pair.of(workspace, deltaLakeDestination);
  }

  @Override
  public void updateDestinationProjectId(
      Project project, DeltaLakeDestination deltaLakeDestination) {
    deltaLakeDestination.getConnectionConfiguration().setDeltaLakeProjectId(project.getId());
    deltaLakeDestination.setDestinationDefinitionId(null);
    deltaLakeDestination.setWorkspaceId(null);
    airbyteClient.updateDestination(deltaLakeDestination);
  }

  public DeltaLakeDestination getDeltaLakeDestination(
      UUID organisationId, AirbyteWorkspace workspace) {

    UUID destinationDefinitionId =
        getDestinationDefinitionIdFormat(
            airbyteProperties.getDelta().getDestinationDefinitionKey());

    return DeltaLakeDestination.builder()
        .destinationDefinitionId(destinationDefinitionId)
        .workspaceId(workspace.getWorkspaceId())
        .name(airbyteProperties.getDelta().getDestinationDefinitionKey())
        .connectionConfiguration(
            DeltaConnectionConfiguration.builder()
                .deltaLakeUrl(airbyteProperties.getDelta().getUrl())
                // TODO this is hardcoded value
                .deltaLakeProjectId("not-claimed")
                .rawServiceUrl(airbyteProperties.getDelta().getRawServiceUrl())
                .deltaLakeAccessKey(airbyteProperties.getDelta().getUser())
                .deltaLakeSecretKey(airbyteProperties.getDelta().getPassword())
                .deltaLakeBucketUrl(airbyteProperties.getDelta().getBucketUrl())
                .deltaLakeTableFormat(airbyteProperties.getDelta().getTableFormat())
                .deltaLakeOrganizationId(organisationId)
                .deltaLakeCompressionMode(airbyteProperties.getDelta().getCompressionMode())
                .build())
        .build();
  }

  private UUID getDestinationDefinitionIdFormat(String key) {
    DestinationDefinitionDto destinationDefinition =
        airbyteClient.listDestinationDefinition().getDestinationDefinitions().stream()
            .filter(x -> x.getName().equals(key))
            .findFirst()
            .orElseThrow(
                () -> new NoSuchElementException(String.format(ExMessage.UNSUPPORTED_FORMAT, key)));

    return destinationDefinition.getDestinationDefinitionId();
  }
}
