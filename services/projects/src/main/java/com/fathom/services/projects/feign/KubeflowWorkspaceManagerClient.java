package com.fathom.services.projects.feign;

import com.fathom.services.projects.models.dto.kubeflowworkspacemanager.WorkspaceDeleteResponse;
import com.fathom.services.projects.models.dto.kubeflowworkspacemanager.WorkspaceDto;
import java.util.UUID;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${kubeflowworkspacemanager.service.name}")
public interface KubeflowWorkspaceManagerClient {

  @PostMapping(path = "workspaces/")
  WorkspaceDto createWorkspace(
      @RequestHeader("x-organization-id") UUID organizationId,
      @RequestHeader("x-project-id") UUID projectId,
      @RequestBody WorkspaceDto workspaceDto);

  @DeleteMapping(path = "workspaces/{id}")
  WorkspaceDeleteResponse deleteWorkspace(
      @RequestHeader("x-organization-id") UUID organizationId,
      @RequestHeader("x-project-id") String projectId,
      @PathVariable("id") Integer workspaceId);
}
