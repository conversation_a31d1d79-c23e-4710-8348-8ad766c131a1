package com.fathom.services.projects.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "airbyte.config.destination")
public class AirbyteProperties {

  Minio minio;
  Delta delta;

  @Data
  public static class Delta {

    String url;
    String user;
    String password;
    String bucketUrl;
    String tableFormat;
    String compressionMode;
    String destinationDefinitionKey;
    String rawServiceUrl;
  }

  @Data
  public static class Minio {

    String host;
    Bucket bucket;
    String user;
    String password;
    String destinationDefinitionKey;

    @Data
    public static class Bucket {

      String name;
      String path;
    }
  }
}
