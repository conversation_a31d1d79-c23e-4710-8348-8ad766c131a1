package com.fathom.services.projects.config;

import com.fathom.services.projects.models.enums.ProjectType;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class ProjectTypeEnumConverter implements Converter<String, ProjectType> {

  @Override
  public ProjectType convert(String s) {
    return Enum.valueOf(ProjectType.class, s.toUpperCase());
  }
}
