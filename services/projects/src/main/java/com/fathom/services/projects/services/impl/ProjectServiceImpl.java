package com.fathom.services.projects.services.impl;

import com.fathom.lib.common.exception.RestException;
import com.fathom.services.projects.feign.KubeflowWorkspaceManagerClient;
import com.fathom.services.projects.mapper.ProjectMapper;
import com.fathom.services.projects.models.Project;
import com.fathom.services.projects.models.dto.airbyte.destination.DeltaLakeDestination;
import com.fathom.services.projects.models.dto.airbyte.workspace.AirbyteWorkspace;
import com.fathom.services.projects.models.dto.kubeflowworkspacemanager.WorkspaceDto;
import com.fathom.services.projects.models.dto.project.ProjectCreateDto;
import com.fathom.services.projects.models.dto.project.ProjectDto;
import com.fathom.services.projects.models.dto.project.ProjectUpdateDto;
import com.fathom.services.projects.models.enums.ProjectType;
import com.fathom.services.projects.repository.ProjectRepository;
import com.fathom.services.projects.services.AirbyteService;
import com.fathom.services.projects.services.ProjectService;
import com.fathom.services.projects.util.ExMessage;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectServiceImpl implements ProjectService {

  private final ProjectRepository projectRepository;
  private final AirbyteService airbyteService;

  private final KubeflowWorkspaceManagerClient kubeflowWorkspaceManagerClient;
  private final ProjectMapper mapper;

  @Override
  @Transactional
  public ProjectDto createProject(UUID organizationId, String email, ProjectCreateDto dto) {
    if (projectRepository.existsByOrganizationIdAndNameAndProjectType(
        organizationId, dto.getName(), dto.getProjectType())) {
      throw new RestException(
          HttpStatus.BAD_REQUEST,
          ExMessage.NAME_ALREADY_EXIST_WITHIN_ORG,
          dto.getName(),
          organizationId);
    }

    Project project;

    switch (dto.getProjectType()) {
      case DATA:
        Pair<AirbyteWorkspace, DeltaLakeDestination> result =
            airbyteService.initializeWorkspace(organizationId, email, dto.getName());
        project = mapper.toEntityForDataType(organizationId, email, dto, result.getLeft());
        project = projectRepository.save(project);
        airbyteService.updateDestinationProjectId(project, result.getRight());
        break;

      case INTELLIGENCE:
        WorkspaceDto workspace = getKubeflowWorkspace(dto);
        workspace =
            kubeflowWorkspaceManagerClient.createWorkspace(
                organizationId, organizationId, workspace);
        project = mapper.toEntity(organizationId, email, dto);
        project.setKubeflowWorkspaceId(workspace.getId());
        project = projectRepository.save(project);

        log.info(
            "Created a new kubeflow workspace for project in organization {} with name {}",
            organizationId,
            project.getName());
        break;

      default:
        project = mapper.toEntity(organizationId, email, dto);
        project = projectRepository.save(project);
        log.info(
            "Created a new project in organization {} with name {}",
            organizationId,
            project.getName());
        break;
    }

    return mapper.toDto(project);
  }

  @Override
  public Page<ProjectDto> getProjects(
      UUID organizationId, ProjectType projectType, Pageable pageable) {

    if (Objects.isNull(organizationId)) {
      return getPagedDto(projectRepository.findAll(pageable));
    }

    if (Objects.nonNull(projectType)) {
      Page<Project> entitiesPaged =
          projectRepository
              .findByOrganizationIdAndProjectType(organizationId, projectType, pageable)
              .orElseThrow(
                  () ->
                      new NoSuchElementException(
                          String.format(ExMessage.ORGANIZATION_EMPTY, organizationId)));
      return getPagedDto(entitiesPaged);
    }

    Page<Project> entitiesPaged =
        projectRepository
            .findByOrganizationId(organizationId, pageable)
            .orElseThrow(
                () ->
                    new NoSuchElementException(
                        String.format(ExMessage.ORGANIZATION_EMPTY, organizationId)));
    return getPagedDto(entitiesPaged);
  }

  @Override
  public ProjectDto getProjectByID(String id) {
    Project project =
        projectRepository
            .findById(id)
            .orElseThrow(
                () -> new NoSuchElementException(String.format(ExMessage.PROJECT_NOT_FOUND, id)));

    return getProjectDtoWithLastUpdatedTime(project, true);
  }

  @Override
  public ProjectDto updateProject(UUID organizationId, ProjectUpdateDto projectUpdateDto) {
    Project project =
        projectRepository
            .findById(projectUpdateDto.getId())
            .orElseThrow(
                () ->
                    new NoSuchElementException(
                        String.format(ExMessage.PROJECT_NOT_FOUND, projectUpdateDto.getId())));

    // Track the last update time before changes
    LocalDateTime lastUpdateTime = project.getLastAccessed();

    // Update the project entity and save
    mapper.updateDtoToEntity(project, projectUpdateDto);
    projectRepository.save(project);

    // Map to DTO and set the previous last accessed time
    ProjectDto projectDto = mapper.toDto(project);
    projectDto.setLastAccessed(lastUpdateTime);
    return projectDto;
  }

  @Override
  public ProjectDto getByProjectByIds(String id) {
    Project project =
        projectRepository
            .findByIdOrClientId(id, id)
            .orElseThrow(
                () ->
                    new NoSuchElementException(
                        String.format(ExMessage.PROJECT_OR_ASSET_ID_NOT_FOUND, id, id)));
    return getProjectDtoWithLastUpdatedTime(project, true);
  }

  @Override
  public ProjectDto getByProjectByNameAndProjectType(
      UUID organizationId, String name, ProjectType projectType) {
    Project project =
        projectRepository
            .findByOrganizationIdAndNameAndProjectType(organizationId, name, projectType)
            .orElseThrow(
                () ->
                    new NoSuchElementException(
                        String.format(ExMessage.PROJECT_NOT_FOUND_BY_NAME, name)));

    return getProjectDtoWithLastUpdatedTime(project, true);
  }

  @Override
  public List<ProjectDto> findByOrganizationIdAndIdIn(UUID organizationId, Set<String> ids) {
    return projectRepository.findByOrganizationIdAndIdIn(organizationId, ids).stream()
        .map(mapper::toDto)
        .toList();
  }

  @Override
  public void deleteProjectById(String id) {
    projectRepository
        .findById(id)
        .ifPresent(
            project -> {
              if (ProjectType.INTELLIGENCE.equals(project.getProjectType())) {
                kubeflowWorkspaceManagerClient.deleteWorkspace(
                    project.getOrganizationId(),
                    project.getId(),
                    project.getKubeflowWorkspaceId().intValue());
              }
              projectRepository.deleteById(id);
            });
  }

  // region helpers
  public ProjectDto getProjectDtoWithLastUpdatedTime(Project project, boolean update) {

    LocalDateTime lastUpdateTime = project.getLastAccessed();

    // Update last accessed time
    if (update) {
      project.setLastAccessed(ZonedDateTime.now(ZoneId.of("UTC")).toLocalDateTime());
      projectRepository.save(project);
    }

    // Map to DTO and set the previous last accessed time
    ProjectDto projectDto = mapper.toDto(project);
    projectDto.setLastAccessed(lastUpdateTime);
    return projectDto;
  }

  private PageImpl<ProjectDto> getPagedDto(Page<Project> entitiesPaged) {
    return new PageImpl<>(
        entitiesPaged.getContent().stream()
            .map(x -> getProjectDtoWithLastUpdatedTime(x, false))
            .toList(),
        entitiesPaged.getPageable(),
        entitiesPaged.getTotalElements());
  }

  private WorkspaceDto getKubeflowWorkspace(ProjectCreateDto projectCreateDto) {
    WorkspaceDto workspace = new WorkspaceDto();

    workspace.setName(projectCreateDto.getName().replace(" ", "-").toLowerCase());
    workspace.setActive(true);
    workspace.setCompanyId("1");

    return workspace;
  }
  // end region
}
