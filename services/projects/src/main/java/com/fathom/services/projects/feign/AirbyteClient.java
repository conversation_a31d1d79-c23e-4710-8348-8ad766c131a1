package com.fathom.services.projects.feign;

import com.fathom.services.projects.models.dto.airbyte.definition.DestinationDefinitionResponse;
import com.fathom.services.projects.models.dto.airbyte.destination.AirbyteConnectionRequest;
import com.fathom.services.projects.models.dto.airbyte.destination.AirbyteConnectionStatus;
import com.fathom.services.projects.models.dto.airbyte.destination.DeltaLakeDestination;
import com.fathom.services.projects.models.dto.airbyte.workspace.AirbyteWorkspace;
import com.fathom.services.projects.models.dto.airbyte.workspace.AirbyteWorkspaceCreateDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "${airbyte.service.name}")
public interface AirbyteClient {

  @PostMapping(path = "api/v1/workspaces/create")
  AirbyteWorkspace createWorkspace(@RequestBody AirbyteWorkspaceCreateDto workspace);

  @PostMapping(path = "api/v1/destination_definitions/list")
  DestinationDefinitionResponse listDestinationDefinition();

  @PostMapping(path = "api/v1/scheduler/destinations/check_connection")
  AirbyteConnectionStatus checkConnectionToTarget(@RequestBody AirbyteConnectionRequest body);

  @PostMapping(path = "api/v1/destinations/create")
  DeltaLakeDestination createDestination(@RequestBody DeltaLakeDestination destination);

  @PostMapping(path = "api/v1/destinations/update")
  DeltaLakeDestination updateDestination(@RequestBody DeltaLakeDestination destination);
}
