package com.fathom.services.projects.models.dto.project;

import static com.fathom.lib.common.model.Constants.REG_EXP_FOR_NAME_VALIDATION;

import com.fathom.services.projects.models.enums.ProjectType;
import jakarta.validation.constraints.Pattern;
import java.util.HashMap;
import lombok.Data;

@Data
public class ProjectCreateDto {

  @Pattern(regexp = REG_EXP_FOR_NAME_VALIDATION, message = "Only letters, dash & lower case")
  String name;

  String displayName;
  ProjectType projectType;
  String clientId;
  String description;
  HashMap<String, Object> metadata;
  HashMap<ProjectType, String> linkedProjects;
  boolean isArchived;
  String projectIcon;
}
