package com.fathom.services.projects.util;

public class ExMessage {

  public static final String ORGANIZATION_EMPTY = "project with id: %s does not exist";
  public static final String PROJECT_NOT_FOUND = "project with id: %s does not exist";
  public static final String PROJECT_NOT_FOUND_BY_NAME = "project with name: %s does not exist";
  public static final String PROJECT_OR_ASSET_ID_NOT_FOUND =
      "project with id: %s or asset id: %s does not exist";
  public static final String UNSUPPORTED_FORMAT = "%s unsupported format";
  public static final String AIRBYTE_CONNECTION_FAILED = "could not establish connection to %s";
  public static final String NAME_ALREADY_EXIST_WITHIN_ORG =
      "%s is already exist within organization with id %s";

  private ExMessage() {
    throw new IllegalStateException("Utility class");
  }
}
