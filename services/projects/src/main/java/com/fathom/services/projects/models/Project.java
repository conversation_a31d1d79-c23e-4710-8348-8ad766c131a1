package com.fathom.services.projects.models;

import com.fathom.services.projects.models.enums.ProjectType;
import jakarta.persistence.Id;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.UUID;
import lombok.Data;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@CompoundIndex(
    name = "unique_name_and_project_type_within_organization",
    def = "{ 'organizationId':1 , 'name':1, 'projectType':1 }",
    unique = true)
@Document
public class Project {

  @Id String id;
  UUID organizationId;
  String clientId;
  String name;
  String displayName;
  String description;
  ProjectType projectType;
  HashMap<String, Object> metadata;
  HashMap<ProjectType, String> linkedProjects;
  LocalDateTime createdTime = ZonedDateTime.now(ZoneId.of("UTC")).toLocalDateTime();
  LocalDateTime updatedTime = ZonedDateTime.now(ZoneId.of("UTC")).toLocalDateTime();
  UUID airbyteWorkspaceId;
  Long kubeflowWorkspaceId;
  boolean isArchived;
  String ownerEmail;
  String projectIcon;
  LocalDateTime lastAccessed = ZonedDateTime.now(ZoneId.of("UTC")).toLocalDateTime();
}
