package com.fathom.services.projects.models.dto.airbyte.destination;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.UUID;
import lombok.Data;

@Data
public class MinioIoDestinationDto {

  UUID destinationDefinitionId;
  UUID workspaceId;
  S3ConnectionConfiguration connectionConfiguration;
  String name;

  @Data
  public static class S3ConnectionConfiguration {

    Format format;

    @JsonProperty("s3_endpoint")
    String s3Endpoint;

    @JsonProperty("access_key_id")
    String accessKeyId;

    @JsonProperty("s3_bucket_name")
    String s3BucketName;

    @JsonProperty("s3_bucket_path")
    String s3BucketPath;

    @JsonProperty("s3_bucket_region")
    String s3BucketRegion;

    @JsonProperty("secret_access_key")
    String secretAccessKey;

    @Data
    public static class Format {

      @JsonProperty("format_type")
      String formatType;

      @JsonProperty("compression_codec")
      CompressionCodec compressionCodec = new CompressionCodec();

      @Data
      public static class CompressionCodec {

        String codec;
      }
    }
  }
}
