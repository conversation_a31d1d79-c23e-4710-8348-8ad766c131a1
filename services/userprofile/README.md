# Fathom user profile service #

 [1. Description.]()

 [2. Documentation]()

 [3. Dependencies]()

 [4. Service configuration]()

 [5. Build process]()

 [6. Deployment]()

 [6.1. By Docker compose example]()

 [6.1.1. Debug in docker]()

 [6.2. By Makefile example]()


### 1. Description
User profile service provides the ability to create and manage user's profile.

### 2. Documentation
To reach service documentation you should use this link template:http://{host}:{port}/swagger-ui.html E.g.` http://localhost:8089/swagger-ui.html`

### 3. Dependencies
To deploy this service properly you need to configure postgresql and mongo db instances on your server.

Preferred versions:
 
    - postgres: 12

    - mongo: 4.0
    

By default this service listens:

    POSTGRES_HOST: postgres
  
    POSTGRES_PORT: 5432
    
    POSTGRES_USER: postgres
    
    POSTGRES_PASSWORD: postgres
    
    POSTGRES_DB: fathom
    
    MONGO_HOST: mongo
    
    MONGO_PORT: 27017
    
    MONGO_NAME: springboot-mongo
    


### 4. Service configuration
Below is a list of environment variables which can be consumed by this service.

(ENV_VAR_NAME : DEFAULT_VALUE - DESCRIPTION)

    POSTGRES_HOST : postgres - Database host name.
    POSTGRES_PORT : 5432 - Database port number.
    POSTGRES_DB : fathom - Database name.
    POSTGRES_USER : postgres - Postgres username
    POSTGRES_PASSWORD : postgres - Postgres password.
    PROFILES_PORT: 8089 - Profile service port number.
    PROFILES_NAME: profiles - Profile service name.
    MONGO_HOST: mongo - Database host name.
    MONGO_PORT: 27017 - Database port number.
    MONGO_NAME: springboot-mongo - Database name.

### 5. Build process
To build code and generate artifact you need to go through next steps: 

 1. go to the root directory (directory which contains pom.xml) 
 
 2. run command: `mvn package`

After that artifact will be generated and stored by the way below.

`{root directory}/target/userprofile-{version}.jar`

### 6. Deployment
#### 6.1 By Docker compose example
You can use this docker compose file to run this service locally. It already contains profiles, mongo, postgresql and pgadmin (ui for postgres db).

To run this service with current docker-compose file configurations you should:

* create docker-compose.yml file in the root directory (directory which contains pom.xml file), with LF line endings and based on the example showed below.

* build code (follow chapter 'Build process')

* run command from the project root directory (requires docker and docker-compose tool installation): `docker-compose up --build` or `docker-compose up --build -d` (detached mode)
  
   
        
        version: '3.7'
           
        x-constants:
            postgres: &postgresEnv
                POSTGRES_HOST: postgres
                POSTGRES_PORT: 5432
                POSTGRES_USER: postgres
                POSTGRES_PASSWORD: postgres
                POSTGRES_DB: fathom
            profiles: &profilesEnv
                PROFILES_HOST: localhost
                PROFILES_PORT: 8089
                PROFILES_NAME: profiles
            mongo: &mongoEnv
                MONGO_HOST: mongo
                MONGO_PORT: 27017
                MONGO_NAME: springboot-mongo                           
                    
            services:
                profiles:
                    container_name: profiles
                build:
                    context: .
                dockerfile: Dockerfile
                image: profiles:latest
                ports:
                    - "8089:8089"
                    - "5089:5089"
                environment:
                    <<: *profilesEnv
                    <<: *postgresEnv
                    <<: *mongoEnv
                depends_on:
                    - postgres
                    - mongo
                networks:
                    - my_network1
    
            postgres:
                image: postgres:12
                command: ["postgres", "-c", "listen_addresses=*"]
                ports:
                    - "5432:5432"
                environment:
                    <<: *postgresEnv
                volumes:
                    - postgres-data:/var/lib/postgresql/data/
                networks:
                    - my_network1

            mongo:
                image: mongo:4.0
                command: ["mongod", "--smallfiles", "--logpath=/dev/null"]
                ports:
                    - "27017:27017"
                environment:
                    <<: *mongoEnv
                volumes:
                    - ./data/db:/data/db
                networks:
                    - my_network1

            pgadmin:
                image: dpage/pgadmin4
                depends_on:
                    - postgres
                ports:
                    - "5555:80"
                environment:
                    PGADMIN_DEFAULT_EMAIL: <EMAIL>
                    PGADMIN_DEFAULT_PASSWORD: admin
                networks:
                    - my_network1
    
            networks:
                my_network1:
                    name: my_network1
                    driver: bridge 
                    
            volumes:
                postgres-data:

        
### 6.1.1 DEBUG IN DOCKER

To be able to join the service in debug mode the string `-agentlib:jdwp=transport=dt_socket,address=5089,server=y,suspend=n' was added into dockerfile CMD command after $VM_OPTS parameter.`

It must looks like: `CMD ["sh", "-c", "java -Djava.security.egd=file:/dev/./urandom $VM_OPTS -agentlib:jdwp=transport=dt_socket,address=5089,server=y,suspend=n -jar $EXECUTION_PATH"]`

Also port 5089 must be opened in containers configuration.

### 6.2 By Makefile example

Local deployment could be done going through next steps:

 * make sure that mongo and postgres are already running;

 * change required environmet variables in <envfile> located in userprofile project root directory.

 * go into the userprofile project root directory and run` <make>` command from terminal. 

  E.g. `make docker`
  
 * run created docker image

#### ALL REQUIRED ENVIRONMET VARIABLES LOCATED IN ENVFILE
    POSTGRES_HOST : postgres - Database host name.
    POSTGRES_PORT : 5432 - Database port name.
    POSTGRES_DB : fathom - Database name.
    POSTGRES_USER : postgres - Postgres username.
    POSTGRES_PASSWORD : postgres - Postgres password.
    MONGO_HOST: mongo - Database host name.
    MONGO_PORT: 27017 - Database port number.
    MONGO_NAME: springboot-mongo - Database name.

