package com.fathom.services.user.profile.controller.impl;

import com.fathom.services.user.IntegrationTestBase;
import com.fathom.services.user.profile.domain.dto.ProfileInfoDTO;
import io.restassured.RestAssured;
import io.restassured.http.ContentType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpStatus;

import java.util.*;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class ProfileInfoControllerImplTest extends IntegrationTestBase {

    @LocalServerPort
    private int port;

    private final String TEST_EMAIL = "<EMAIL>";
    private final UUID TEST_ORG_ID = UUID.randomUUID();

    @BeforeEach
    void setUp() {
        RestAssured.port = port;
        RestAssured.basePath = "/profiles";
    }

    @Test
    void testCreateAndRetrieveUserProfile() {
        // Create a profile
        ProfileInfoDTO profileDTO = new ProfileInfoDTO();
        profileDTO.setFirstName("John");
        profileDTO.setLastName("Doe");
        profileDTO.setEmail(TEST_EMAIL);
        profileDTO.setDefaultOrganizationId(TEST_ORG_ID);

        given()
            .contentType(ContentType.JSON)
            .header("x-email", TEST_EMAIL)
            .body(profileDTO)
        .when()
            .post()
        .then()
            .statusCode(HttpStatus.CREATED.value())
            .body("firstName", equalTo("John"))
            .body("lastName", equalTo("Doe"));

        // Retrieve the profile
        given()
            .header("x-email", TEST_EMAIL)
        .when()
            .get()
        .then()
            .statusCode(HttpStatus.OK.value())
            .body("firstName", equalTo("John"))
            .body("lastName", equalTo("Doe"));
    }

    @Test
    void testUpdateUserProfile() {
        // Create profile first
        ProfileInfoDTO createDTO = new ProfileInfoDTO();
        createDTO.setFirstName("Jane");
        createDTO.setLastName("Smith");
        createDTO.setEmail(TEST_EMAIL);
        createDTO.setDefaultOrganizationId(TEST_ORG_ID);

        String profileId = given()
            .contentType(ContentType.JSON)
            .header("x-email", TEST_EMAIL)
            .body(createDTO)
        .when()
            .post()
        .then()
            .statusCode(HttpStatus.CREATED.value())
            .extract().path("profileId");

        // Update profile
        ProfileInfoDTO updateDTO = new ProfileInfoDTO();
        updateDTO.setFirstName("Updated");
        updateDTO.setLastName("User");

        given()
            .contentType(ContentType.JSON)
            .header("x-email", TEST_EMAIL)
            .body(updateDTO)
        .when()
            .put("/"+profileId)
        .then()
            .statusCode(HttpStatus.OK.value())
            .body("firstName", equalTo("Updated"))
            .body("lastName", equalTo("User"));
    }

    @Test
    void testRetrieveProfilesByEmails() {
        // Create profiles with specific emails
        List<String> emails = Arrays.asList(
            "<EMAIL>",
            "<EMAIL>"
        );

        for (String email : emails) {
            ProfileInfoDTO profileDTO = new ProfileInfoDTO();
            profileDTO.setFirstName("Lookup");
            profileDTO.setLastName("User");
            profileDTO.setEmail(email);
            profileDTO.setDefaultOrganizationId(TEST_ORG_ID);

            given()
                .contentType(ContentType.JSON)
                .header("x-email", email)
                .body(profileDTO)
            .when()
                .post();
        }

        // Test retrieve by emails endpoint
        given()
            .contentType(ContentType.JSON)
            .body(emails)
        .when()
            .post("/retrieveManyByEmails")
        .then()
            .statusCode(HttpStatus.OK.value())
            .body("size()", equalTo(emails.size()))
            .body("findAll { it.email =~ /lookup\\<EMAIL>/ }.size()", equalTo(emails.size()));
    }
}