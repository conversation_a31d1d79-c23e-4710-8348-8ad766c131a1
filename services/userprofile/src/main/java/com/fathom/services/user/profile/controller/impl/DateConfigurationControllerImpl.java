package com.fathom.services.user.profile.controller.impl;

import static com.fathom.services.user.profile.controller.impl.ProfileInfoControllerImpl.EMAIL_HEADER_NAME;

import com.fathom.services.user.profile.controller.DateConfigurationController;
import com.fathom.services.user.profile.domain.dto.DateConfigurationDTO;
import com.fathom.services.user.profile.domain.dto.TimeZoneDTO;
import com.fathom.services.user.profile.service.DateService;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(path = "/date/configurations")
@Slf4j
public class DateConfigurationControllerImpl implements DateConfigurationController {
  @Autowired private DateService timeZoneService;

  @Override
  @GetMapping("/timezones")
  public List<TimeZoneDTO> getAll(@RequestHeader(EMAIL_HEADER_NAME) String email) {
    log.info("Received GET request for TimeZone list from user with Email: {}", email);
    return timeZoneService.createTimeZoneList();
  }

  @Override
  @GetMapping("/configuration")
  public DateConfigurationDTO getTimeConfiguration(@RequestHeader(EMAIL_HEADER_NAME) String email) {
    log.info("Received GET time configuration from user:{}", email);
    return timeZoneService.getTimeConfiguration(email);
  }
}
