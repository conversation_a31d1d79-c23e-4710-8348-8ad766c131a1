package com.fathom.services.user.profile.utils.converter;

import static java.time.ZoneOffset.UTC;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.time.ZoneId;
import java.time.format.TextStyle;
import java.util.Locale;
import java.util.Objects;
import java.util.Set;

@Converter(autoApply = true)
public class ZoneIdConverter implements AttributeConverter<ZoneId, String> {

  @Override
  public String convertToDatabaseColumn(ZoneId attribute) {
    if (Objects.isNull(attribute)) {
      return UTC.getDisplayName(TextStyle.FULL, Locale.ENGLISH);
    }
    Set<String> allZones = ZoneId.getAvailableZoneIds();
    if (!allZones.contains(attribute.getId())) {

      return UTC.getId();
    }
    return attribute.getId();
  }

  @Override
  public ZoneId convertToEntityAttribute(String dbData) {
    return ZoneId.of(dbData);
  }
}
