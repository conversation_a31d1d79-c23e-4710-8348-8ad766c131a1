package com.fathom.services.user.profile.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Entity
@Table(name = "office_address")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OfficeAddress extends Address {

  @Column(name = "work_place")
  private String workPlace;

  @Column
  @PositiveOrZero
  @Max(value = 10000)
  private Integer floor;

  @Column(name = "office_unit")
  private String officeUnit;
}
