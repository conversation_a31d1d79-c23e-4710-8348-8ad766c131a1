package com.fathom.services.user.profile.service.impl;

import static com.fathom.services.user.profile.exception.InvalidDataException.attemptToSaveNullEntity;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fathom.services.user.profile.domain.dto.DepartmentDTO;
import com.fathom.services.user.profile.domain.dto.PageDTO;
import com.fathom.services.user.profile.domain.entity.Department;
import com.fathom.services.user.profile.domain.repository.DepartmentRepository;
import com.fathom.services.user.profile.domain.repository.ProfileInfoRepository;
import com.fathom.services.user.profile.exception.InvalidDataException;
import com.fathom.services.user.profile.service.DepartmentService;
import com.fathom.services.user.profile.utils.mapper.CycleAvoidingMappingContext;
import com.fathom.services.user.profile.utils.mapper.DepartmentMapper;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DepartmentServiceImpl implements DepartmentService {

  @Autowired DepartmentRepository departmentRepository;
  @Autowired ProfileInfoRepository profileInfoRepository;

  @Autowired ObjectMapper objectMapper;

  @Override
  public DepartmentDTO save(String email, DepartmentDTO departmentDTO) {
    if (Objects.isNull(departmentDTO)) {
      throw attemptToSaveNullEntity();
    }
    Department department =
        DepartmentMapper.INSTANCE.dtoToNewEntity(departmentDTO, new CycleAvoidingMappingContext());
    Department savedDepartment = departmentRepository.save(department);
    return DepartmentMapper.INSTANCE.entityToNewDTO(
        savedDepartment, new CycleAvoidingMappingContext());
  }

  @Override
  public Set<DepartmentDTO> saveAll(String email, UUID orgId, Set<DepartmentDTO> departmentDTOS) {
    Set<Department> departments =
        departmentDTOS.stream()
            .map(
                departmentDTO ->
                    DepartmentMapper.INSTANCE.dtoToNewEntity(
                        departmentDTO, new CycleAvoidingMappingContext()))
            .collect(toSet());
    Set<Department> savedDepartments = departmentRepository.saveAll(departments);
    return savedDepartments.stream()
        .map(
            department ->
                DepartmentMapper.INSTANCE.entityToNewDTO(
                    department, new CycleAvoidingMappingContext()))
        .collect(Collectors.toSet());
  }

  @Override
  public void deleteById(String email, Integer id) {
    Department department = departmentRepository.findById(id);
    if (Objects.isNull(department)) {
      log.info("Department with Id:{} was not found in the database.", id);
      return;
    }
    if (!departmentRepository.deleteById(id)) {
      log.warn("Department with provided ID:{} was not found in Database", id);
      return;
    }
    log.info("Department with provided ID:{} was deleted form Database", id);
  }

  @Override
  public PageDTO findAll(String email, UUID userOrgId, int pageNumber, int pageSize) {
    Pageable pageable = PageRequest.of(pageNumber - 1, pageSize);
    Page<Department> page;
    if (userOrgId != null) {
      page = departmentRepository.findAllByOrganizationId(userOrgId, pageable);
    } else {
      page = departmentRepository.findAll(pageable);
    }
    List<DepartmentDTO> pageContent =
        page.get()
            .map(
                department ->
                    DepartmentMapper.INSTANCE.entityToNewDTO(
                        department, new CycleAvoidingMappingContext()))
            .collect(toList());
    return new PageDTO<>(
        pageContent,
        page.getNumber() + 1,
        page.getSize(),
        page.getTotalPages(),
        page.getTotalElements());
  }

  public DepartmentDTO findById(String email, Integer id) {
    Department department = departmentRepository.findById(id);
    if (Objects.isNull(department)) {
      log.info("Department with Id: {} was not found in the database", id);
      return null;
    }
    return DepartmentMapper.INSTANCE.entityToNewDTO(department, new CycleAvoidingMappingContext());
  }

  @Override
  public DepartmentDTO update(String email, DepartmentDTO departmentDTO, Integer departmentId) {
    if (Objects.isNull(departmentDTO)) {
      throw InvalidDataException.attemptToSaveNullEntity();
    }
    Department department =
        DepartmentMapper.INSTANCE.dtoToNewEntity(departmentDTO, new CycleAvoidingMappingContext());
    Department storedDepartment = departmentRepository.findById(departmentId);
    department.setId(departmentId);
    department.setProfiles(storedDepartment.getProfiles());
    if (storedDepartment.equals(department)) {
      throw InvalidDataException.attemptToUpdateDepartmentWithTheSameData();
    }
    Department savedDepartment = departmentRepository.save(department);
    log.info("Department with provided ID:{} was updated.", departmentId);
    return DepartmentMapper.INSTANCE.entityToNewDTO(
        savedDepartment, new CycleAvoidingMappingContext());
  }
}
