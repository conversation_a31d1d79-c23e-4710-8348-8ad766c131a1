package com.fathom.services.user.profile.domain.repository;

import com.fathom.services.user.profile.domain.entity.Department;
import java.util.Set;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface DepartmentRepository {
  Department save(Department department);

  Set<Department> saveAll(Set<Department> departments);

  // false if not found
  boolean deleteById(Integer id);

  // null if not found
  Department findById(Integer id);

  Page<Department> findAllByOrganizationId(UUID organizationId, Pageable pageable);

  Page<Department> findAll(Pageable pageable);

  Set<Department> findAllByIds(Set<Integer> ids);
}
