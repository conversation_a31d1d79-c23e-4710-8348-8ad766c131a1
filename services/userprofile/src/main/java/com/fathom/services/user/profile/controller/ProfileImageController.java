package com.fathom.services.user.profile.controller;

import static com.fathom.services.user.profile.config.constants.SwaggerMessage.*;

import com.fathom.services.user.profile.config.constants.SwaggerMessage;
import com.fathom.services.user.profile.domain.dto.ProfileImageDTO;
import com.fathom.services.user.profile.utils.ProfileImageFile;
import com.fathom.services.user.profile.utils.validator.ValidImage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Encoding;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import org.springframework.web.multipart.MultipartFile;

public interface ProfileImageController {
  @Operation(
      summary = "Remove user profile picture",
      description = SwaggerMessage.ENDPOINT_DELETE_PROFILE_IMAGE_BY_EMAIL)
  void deleteImage(
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Valid @NotNull @Parameter(description = ENDPOINT_IMAGE_ID_PARAM) String id)
      throws IOException;

  @Operation(
      summary = "Upload user profile picture",
      description = SwaggerMessage.ENDPOINT_GET_PROFILE_IMAGE_BY_EMAIL,
      requestBody =
          @RequestBody(
              content =
                  @Content(
                      mediaType = "multipart/form-data",
                      schema = @Schema(implementation = ProfileImageFile.class),
                      encoding =
                          @Encoding(name = "file", contentType = "application/pdf, image/png"))),
      responses = {
        @ApiResponse(responseCode = "200", description = "The UUID of the newly uploaded image"),
        @ApiResponse(
            responseCode = "403",
            description = "The provided credentials are insufficient to see this resource."),
        @ApiResponse(responseCode = "415", description = "The provided file type is not supported"),
        @ApiResponse(
            responseCode = "500",
            description = "We messed up. Please let us know so we can fix it ASAP.")
      })
  ProfileImageDTO uplaodImage(
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @ValidImage @Parameter(hidden = true) MultipartFile file)
      throws IOException;

  @Operation(
      summary = "Find user profile picture",
      description = SwaggerMessage.ENDPOINT_GET_PROFILE_IMAGE_BY_EMAIL)
  ProfileImageDTO findImage(@Parameter(description = ENDPOINT_EMAIL_PARAM) String email)
      throws IOException;
}
