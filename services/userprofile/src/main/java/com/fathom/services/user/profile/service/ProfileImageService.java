package com.fathom.services.user.profile.service;

import com.fathom.services.user.profile.domain.dto.ProfileImageDTO;
import java.io.IOException;
import org.springframework.web.multipart.MultipartFile;

public interface ProfileImageService {
  void removeAvatar(String email, String id);

  ProfileImageDTO setAvatar(String email, MultipartFile multipartFile) throws IOException;

  ProfileImageDTO getAvatar(String email) throws IOException;
}
