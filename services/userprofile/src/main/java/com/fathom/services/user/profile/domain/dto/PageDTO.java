package com.fathom.services.user.profile.domain.dto;

import static com.fathom.services.user.profile.config.constants.SwaggerMessage.*;

import com.fathom.services.user.profile.config.constants.SwaggerMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = SwaggerMessage.MODEL_PAGE_DTO_CLASS)
public class PageDTO<T> {

  @Schema(description = MODEL_CONTENT_FIELD)
  private List<T> content;

  @Schema(description = MODEL_PAGE_NUMBER_FIELD)
  private Integer pageNumber;

  @Schema(description = MODEL_PAGE_SIZE_FIELD)
  private Integer pageSize;

  @Schema(description = MODEL_PAGES_QUANTITY_FIELD)
  private Integer pagesQuantity;

  @Schema(description = MODEL_TOTAL_COUNT_FIELD)
  private Long totalCount;
}
