package com.fathom.services.user.profile.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import com.fathom.services.user.profile.config.constants.SwaggerMessage;
import com.fathom.services.user.profile.domain.entity.Gender;
import com.fathom.services.user.profile.domain.entity.OfficeAddress;
import com.fathom.services.user.profile.utils.Views;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = SwaggerMessage.MODEL_USER_PROFILE_DTO_CLASS)
public class ProfileInfoDTO {
  @JsonView(Views.Update.class)
  private UUID profileId;

  @JsonView(Views.Update.class)
  private String email;

  @Size(min = 2, max = 100)
  @Pattern(regexp = "^[A-Za-z]*$")
  @NotEmpty
  private String firstName;

  @Size(min = 2, max = 100)
  @Pattern(regexp = "^[A-Za-z]*$")
  @NotEmpty
  private String lastName;

  @Past
  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
  private LocalDateTime birthday;

  private List<HomeAddressDTO> homeAddress;
  private OfficeAddress officeAddress;
  private TimeZoneDTO timeZone;
  private String dateFormat;

  @Min(12)
  @Max(24)
  private Integer timeFormat;

  private Gender gender;
  private String phoneNumber;
  @JsonIgnore private String pictureId;
  private String language;
  private String jobTitle;
  private String jobRole;
  @NotNull private UUID defaultOrganizationId;
  @JsonIgnore private String picture;
}
