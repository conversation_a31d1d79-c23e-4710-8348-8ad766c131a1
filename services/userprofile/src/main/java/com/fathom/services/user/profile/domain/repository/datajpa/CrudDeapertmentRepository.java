package com.fathom.services.user.profile.domain.repository.datajpa;

import com.fathom.services.user.profile.domain.entity.Department;
import java.util.UUID;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface CrudDeapertmentRepository extends JpaRepository<Department, Integer> {

  @Query(
      value =
          "SELECT department " + "FROM Department department WHERE organizationId =:organizationId")
  Page<Department> findAllByOrganizationId(UUID organizationId, Pageable pageable);
}
