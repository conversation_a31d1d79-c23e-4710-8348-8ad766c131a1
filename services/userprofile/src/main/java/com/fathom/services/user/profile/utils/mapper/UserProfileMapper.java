package com.fathom.services.user.profile.utils.mapper;

import static com.fathom.services.user.profile.exception.NotFoundException.newProfileImageDafaultNotFound;
import static java.time.format.TextStyle.SHORT;
import static java.util.Locale.ENGLISH;
import static java.util.Locale.getISOLanguages;
import static java.util.Objects.isNull;

import com.fathom.services.user.profile.domain.dto.ProfileInfoDTO;
import com.fathom.services.user.profile.domain.dto.TimeZoneDTO;
import com.fathom.services.user.profile.domain.entity.Gender;
import com.fathom.services.user.profile.domain.entity.ProfileInfo;
import com.fathom.services.user.profile.utils.converter.DateUtils;
import com.google.common.base.Strings;
import com.mongodb.client.gridfs.model.GridFSFile;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Base64;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
@Slf4j
public abstract class UserProfileMapper extends BaseMapper {

  protected static String imagePath = "classpath:/static/";
  protected static String imageExtention = ".png";
  protected static final String IMAGE_PREFIX = "data:image/png;base64, ";

  public static final UserProfileMapper INSTANCE = Mappers.getMapper(UserProfileMapper.class);

  @Mapping(target = "profileId", source = "profileInfo.compositeId.profileId")
  @Mapping(target = "email", source = "profileInfo.compositeId.email")
  @Mapping(target = "timeZone", ignore = true)
  public abstract ProfileInfoDTO entityToNewDTO(
      ProfileInfo profileInfo, @Context CycleAvoidingMappingContext context);

  @BeanMapping(qualifiedByName = "AfterMapping")
  @Mapping(target = "zoneId", ignore = true)
  @Mapping(target = "language", ignore = true)
  public abstract ProfileInfo dtoToNewEntity(
      ProfileInfoDTO profileInfoDTO, @Context CycleAvoidingMappingContext context);

  @AfterMapping
  @Named("AfterMapping")
  void dtoToEntityAfterMapping(
      ProfileInfoDTO profileInfoDTO, @MappingTarget ProfileInfo profileInfo) {
    if (Objects.isNull(profileInfoDTO.getTimeZone())
        || Strings.isNullOrEmpty(profileInfoDTO.getTimeZone().getName())) {
      profileInfo.setZoneId(ZoneId.of("Etc/UTC"));
    } else {
      profileInfo.setZoneId(ZoneId.of(profileInfoDTO.getTimeZone().getName().replace(": ", "/")));
    }
    if (!CollectionUtils.isEmpty(profileInfoDTO.getHomeAddress())) {
      profileInfo.getHomeAddress().forEach(homeAddress -> homeAddress.setProfile(profileInfo));
    }
    for (String isoLanguage : getISOLanguages()) {
      if (isoLanguage.equalsIgnoreCase(profileInfoDTO.getLanguage())) {
        profileInfo.setLanguage(isoLanguage);
        return;
      }
    }
    profileInfo.setLanguage(ENGLISH.getLanguage());
  }

  @AfterMapping
  void entityToDTOAfterMapping(
      ProfileInfo profileInfo, @MappingTarget ProfileInfoDTO profileInfoDTO) {
    String name = profileInfo.getZoneId().getId().replace("/", ": ");
    String offset = DateUtils.getOffset(LocalDateTime.now(), profileInfo.getZoneId());
    String abbr = profileInfo.getZoneId().getDisplayName(SHORT, ENGLISH);
    profileInfoDTO.setTimeZone(new TimeZoneDTO(name, offset, abbr));
    profileInfoDTO.setPicture(loadPicture(profileInfo));
  }

  public String loadPicture(ProfileInfo profileInfo) {
    if (isNull(profileInfo.getPictureId())) {
      return loadDefaultPicture(profileInfo.getGender());
    } else {
      return loadStoredPicture(profileInfo.getPictureId(), profileInfo.getGender());
    }
  }

  private String loadDefaultPicture(Gender gender) {
    if (Objects.isNull(gender)) {
      gender = Gender.OTHER;
    }
    log.info("Loading picture by Gender:{}", gender);
    String picture = null;
    try (InputStream input =
        resourceLoader
            .getResource(imagePath + gender.getCode() + imageExtention)
            .getInputStream()) {
      picture = IMAGE_PREFIX + Base64.getEncoder().encodeToString(IOUtils.toByteArray(input));
    } catch (IOException e) {
      log.error("IOException is thrown:{}", e.getMessage());
      newProfileImageDafaultNotFound();
    }
    return picture;
  }

  private String loadStoredPicture(String id, Gender gender) {
    log.info("Loading picture by Id:{}", id);
    String picture;
    try {
      GridFSFile file = gridFsTemplate.findOne(new Query(Criteria.where("_id").is(id)));
      InputStream is = operations.getResource(file).getInputStream();
      picture = IMAGE_PREFIX + Base64.getEncoder().encodeToString(IOUtils.toByteArray(is));
    } catch (IOException ex) {
      log.error("IOException is thrown: {}", ex.getMessage());
      picture = loadDefaultPicture(gender);
    }
    return picture;
  }
}
