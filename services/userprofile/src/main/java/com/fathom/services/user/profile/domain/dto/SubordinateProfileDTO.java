package com.fathom.services.user.profile.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import com.fathom.services.user.profile.domain.entity.Gender;
import com.fathom.services.user.profile.domain.entity.OfficeAddress;
import com.fathom.services.user.profile.utils.Views;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SubordinateProfileDTO implements Serializable {
  @JsonView(Views.UpdateTimeZoneOffset.class)
  private UUID profileId;

  @Size(min = 2, max = 100)
  @Pattern(regexp = "[^0-9]*")
  private String firstName;

  @Size(min = 2, max = 100)
  @Pattern(regexp = "[^0-9]*")
  private String lastName;

  @Past
  @DateTimeFormat(pattern = "yyyy-MM-dd")
  private LocalDateTime birthday;

  private List<HomeAddressDTO> homeAddress;
  private OfficeAddress officeAddress;
  private TimeZoneDTO timeZone;
  private String dateFormat;
  private Gender gender;
  private String phoneNumber;

  @JsonView(Views.UpdateTimeZoneOffset.class)
  private String pictureId;

  private String language;
  private String jobTitle;
  private String jobRole;
  private List<DepartmentDTO> departments;
  @NotNull private UUID defaultOrganizationId;
  @JsonIgnore private String picture;
}
