package com.fathom.services.user.profile.exception;

import com.fathom.services.user.profile.config.constants.ErrorMessage;

public class NotFoundException extends ContractException {

  protected NotFoundException(String message) {
    super(message);
  }

  public static NotFoundException newProfileNotFoundWithID() {
    return new NotFoundException(ErrorMessage.USER_PROFILE_NOT_FOUND_WITH_ID);
  }

  public static NotFoundException newProfileContactsNotFound() {
    return new NotFoundException(ErrorMessage.PROFILE_CONTACTS_NOT_FOUND);
  }

  public static NotFoundException newProfileNotFoundWithEmail() {
    return new NotFoundException(ErrorMessage.USER_PROFILE_NOT_FOUND_WITH_EMAIL);
  }

  public static NotFoundException newProfileImageDafaultNotFound() {
    return new NotFoundException(ErrorMessage.USER_PROFILE_IMAGE_NOT_FOUND);
  }

  public static NotFoundException newSubordinateProfilesNotFoundWithEmails() {
    return new NotFoundException(ErrorMessage.SUBORDINATE_PROFILES_NOT_FOUND_WITH_EMAIL);
  }

  public static NotFoundException newSubordinateProfilesNotFound() {
    return new NotFoundException(ErrorMessage.SUBORDINATE_PROFILES_NOT_FOUND);
  }

  public static NotFoundException newDepartmentNotFoundWithId() {
    return new NotFoundException(ErrorMessage.DEPARTMENT_NOT_FOUND_WITH_ID);
  }
}
