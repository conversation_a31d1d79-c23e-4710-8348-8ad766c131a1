package com.fathom.services.user.profile.domain.entity;

import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum Gender {
  FEMALE("FEMALE"),
  MALE("MALE"),
  OTHER("OTHER");

  private final String code;

  private Gender(String code) {
    this.code = code;
  }

  public String getCode() {
    return code;
  }

  private static Map<String, Gender> genderMap =
      Stream.of(Gender.values()).collect(Collectors.toMap(s -> s.code, Function.identity()));

  @JsonCreator
  public static Gender fromString(String string) {
    return Optional.ofNullable(genderMap.get(string.toUpperCase())).orElse(Gender.OTHER);
  }
}
