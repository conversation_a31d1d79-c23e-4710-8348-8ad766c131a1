package com.fathom.services.user.profile.config.constants;

public final class ErrorMessage {

  public static final String USER_PROFILE_NOT_FOUND_WITH_ID =
      "User profile with given id was not found in the Database";
  public static final String PROFILE_CONTACTS_NOT_FOUND =
      "User profile contacts not found in the database";
  public static final String USER_PROFILE_NOT_FOUND_WITH_EMAIL =
      "User profile with given email was not found in the Database";
  public static final String SUBORDINATE_PROFILES_NOT_FOUND_WITH_EMAIL =
      "Subordinate(s) profile(s) with given email(s) was not found in the Database";
  public static final String SUBORDINATE_PROFILES_NOT_FOUND =
      "Subordinate(s) profile(s) for provided profile was not found in the Database";
  public static final String USER_PROFILE_IMAGE_NOT_FOUND = "Default user profile image not found";
  public static final String CONDITIONS_NOT_CORRECT =
      "Invalid request with conditions. Cannot delete default picture.";
  public static final String ATTEMPT_TO_SET_INVALID_VALUE =
      "Invalid request with conditions. Time format value should be 12 or 24.";
  public static final String ATTEMPT_TO_HAVE_FEW_PRIMARY_HOMES =
      "Invalid request. Please, set up only one primary home address.";
  public static final String ATTEMPT_TO_UPDATE_DEPARTMENT_WITHOUT_CHANGES =
      "Invalid request. Stored department with provided data already exist.";
  public static final String ATTEMPT_TO_DELETE_PICTURE_WITH_INCORRECT_ID =
      "Invalid request. Picture with provided id not belongs to the User with provided email.";
  public static final String ATTEMPT_TO_DELETE_EMPTY_DEPARTMENTS_SET =
      "Invalid request. Attempt to delete empty departments set.";
  public static final String ATTEMPT_TO_SAVE_EMPTY_DEPARTMENTS_SET =
      "Invalid request. Attempt to save empty departments set.";
  public static final String ATTEMPT_TO_DELETE_FROM_EMPTY_SUBORDINATES_LIST =
      "Invalid request. List of subordinates already empty.";
  public static final String ATTEMPT_TO_SAVE_EMPTY_ENTITY =
      "Invalid request. Attempt to store null entity.";
  public static final String ATTEMPT_TO_CREATE_DEPARTMENT_IN_INVALID_COMPANY =
      "Invalid request. User cannot create a department in a company where he is not employed.";
  public static final String ATTEMPT_TO_SEARCH_DEPARTMENT_IN_INVALID_COMPANY =
      "Invalid request. User cannot search departments in a company where he is not employed.";
  public static final String ATTEMPT_TO_UPDATE_DEPARTMENT_IN_INVALID_COMPANY =
      "Invalid request. User cannot update departments in a company where he is not employed.";
  public static final String ATTEMPT_TO_DELETE_DEPARTMENT_IN_INVALID_COMPANY =
      "Invalid request. User cannot delete departments in a company where he is not employed.";
  public static final String ALLOWED_IMAGES_EXTENTIONS = "Only PNG or JPG images are allowed.";

  public static final String DEPARTMENT_NOT_FOUND_WITH_ID =
      "Department with given id was not found in the Database";

  private ErrorMessage() {}
}
