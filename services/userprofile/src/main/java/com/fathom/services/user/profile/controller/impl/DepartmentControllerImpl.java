package com.fathom.services.user.profile.controller.impl;

import static com.fathom.services.user.profile.controller.impl.ProfileInfoControllerImpl.EMAIL_HEADER_NAME;
import static com.fathom.services.user.profile.controller.impl.ProfileInfoControllerImpl.ORG_ID_HEADER_NAME;

import com.fathom.services.user.profile.controller.DepartmentController;
import com.fathom.services.user.profile.domain.dto.DepartmentDTO;
import com.fathom.services.user.profile.domain.dto.PageDTO;
import com.fathom.services.user.profile.service.DepartmentService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import java.io.IOException;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(path = "/departments")
@Slf4j
public class DepartmentControllerImpl implements DepartmentController {
  @Autowired private DepartmentService departmentService;

  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public DepartmentDTO create(
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @RequestBody @Valid DepartmentDTO departmentDTO)
      throws IOException {
    log.info(
        "Received POST request from user with Email: {}, DepartmentDTO:{}", email, departmentDTO);
    return departmentService.save(email, departmentDTO);
  }

  @PutMapping(path = "/{department_id}")
  public DepartmentDTO update(
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @PathVariable("department_id") Integer departmentId,
      @RequestBody DepartmentDTO departmentDTO)
      throws IOException {
    log.info(
        "Received PUT request from user with Email: {}, DepartmentId: {}, DepartmentDTO:{}",
        email,
        departmentId,
        departmentDTO);
    return departmentService.update(email, departmentDTO, departmentId);
  }

  @DeleteMapping("/{department_id}")
  public void delete(
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @PathVariable("department_id") Integer departmentId) {
    log.info(
        "Received DELETE request from user with Email: {} and DepartmentID: {}.",
        email,
        departmentId);
    departmentService.deleteById(email, departmentId);
  }

  @GetMapping
  public PageDTO viewAll(
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @RequestHeader(ORG_ID_HEADER_NAME) UUID organizationId,
      @RequestParam(required = false, defaultValue = "false") boolean byOrgId,
      @RequestParam(required = false) UUID chosenOrgId,
      @RequestParam(required = false, defaultValue = "1") @Min(1) Integer pageNumber,
      @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
    if (byOrgId && chosenOrgId != null) {
      log.info(
          "Received GET all departments request from user with Email: {} for organization with OrgId:{}",
          email,
          chosenOrgId);
      return departmentService.findAll(email, chosenOrgId, pageNumber, pageSize);
    }
    if (byOrgId && chosenOrgId == null) {
      log.info("Received GET all departments request from user with Email: {} ", email);
      return departmentService.findAll(email, null, pageNumber, pageSize);
    }
    log.info(
        "Received GET all departments request from user with Email: {} for organization with OrgId:{}",
        email,
        organizationId);
    return departmentService.findAll(email, organizationId, pageNumber, pageSize);
  }

  @GetMapping("/{department_id}")
  public DepartmentDTO view(
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @PathVariable("department_id") Integer departmentId) {
    log.info(
        "Received GET department request from user with Email: {} , DepartmentId: {}",
        email,
        departmentId);
    return departmentService.findById(email, departmentId);
  }
}
