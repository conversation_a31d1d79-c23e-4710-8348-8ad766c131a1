package com.fathom.services.user.profile.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import com.fathom.services.user.profile.utils.DepartmentRole;
import com.fathom.services.user.profile.utils.Views;
import java.io.Serializable;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateDepartmentDTO implements Serializable {
  private DepartmentRole typeOfParticipant;
  private Set<Integer> departmentsIds;

  @JsonView(Views.Response.class)
  private Set<DepartmentDTO> departments;
}
