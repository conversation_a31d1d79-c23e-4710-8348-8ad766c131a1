package com.fathom.services.user.profile.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@MappedSuperclass
public class Address extends BaseEntity {
  @Column
  @Size(min = 2, max = 60)
  private String country;

  @Column
  @Size(min = 2, max = 60)
  private String city;

  @Column
  @Size(min = 2, max = 60)
  private String street;

  @Column private Integer buildingNumber;

  @Column
  @Size(max = 32)
  @Pattern(regexp = "[0-9a-zA-Z]+")
  private String zipcode;
}
