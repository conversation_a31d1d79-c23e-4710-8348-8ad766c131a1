package com.fathom.services.user.profile.utils.mapper;

import com.fathom.services.user.profile.domain.dto.SubordinateProfileDTO;
import com.fathom.services.user.profile.domain.entity.ProfileInfo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class SubordinatesMapper extends BaseMapper {

  public static final SubordinatesMapper INSTANCE = Mappers.getMapper(SubordinatesMapper.class);

  @Mapping(target = "profileId", source = "profileInfo.compositeId.profileId")
  @Mapping(target = "timeZone", ignore = true)
  @Mapping(target = "dateFormat", ignore = true)
  @Mapping(target = "language", ignore = true)
  @Mapping(target = "departments", ignore = true)
  @InheritInverseConfiguration
  public abstract SubordinateProfileDTO entityToNewDTO(
      ProfileInfo profileInfo, @Context CycleAvoidingMappingContext context);

  @BeanMapping(qualifiedByName = "AfterMapping")
  @Mapping(target = "zoneId", ignore = true)
  @Mapping(target = "language", ignore = true)
  public abstract ProfileInfo dtoToNewEntity(
      SubordinateProfileDTO subordinateProfileDTO, @Context CycleAvoidingMappingContext context);

  @AfterMapping
  void entityToDTOAfterMapping(
      ProfileInfo profileInfo, @MappingTarget SubordinateProfileDTO subordinateProfileDTO) {
    subordinateProfileDTO.setPicture(UserProfileMapper.INSTANCE.loadPicture(profileInfo));
  }
}
