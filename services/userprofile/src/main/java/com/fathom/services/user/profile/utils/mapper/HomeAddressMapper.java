package com.fathom.services.user.profile.utils.mapper;

import com.fathom.services.user.profile.domain.dto.HomeAddressDTO;
import com.fathom.services.user.profile.domain.entity.HomeAddress;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class HomeAddressMapper {

  public static final HomeAddressMapper INSTANCE = Mappers.getMapper(HomeAddressMapper.class);

  @InheritInverseConfiguration
  @Mapping(target = "primaryHome", ignore = true)
  public abstract HomeAddressDTO entityToNewDTO(
      HomeAddress homeAddress, @Context CycleAvoidingMappingContext context);

  @BeanMapping(qualifiedByName = "AfterMapping")
  @Mapping(target = "primaryHome", ignore = true)
  public abstract HomeAddress dtoToNewEntity(
      HomeAddressDTO homeAddressDTO, @Context CycleAvoidingMappingContext context);

  @AfterMapping
  @Named("AfterMapping")
  void dtoToEntityAfterMapping(
      HomeAddressDTO homeAddressDTO, @MappingTarget HomeAddress homeAddress) {
    homeAddress.setPrimaryHome(homeAddressDTO.getPrimaryHome());
  }

  @AfterMapping
  void entityToDTOAfterMapping(
      HomeAddress homeAddress, @MappingTarget HomeAddressDTO homeAddressDTO) {
    homeAddressDTO.setPrimaryHome(homeAddress.getPrimaryHome());
  }
}
