package com.fathom.services.user.profile;

import com.fathom.diagnostics.configuration.DiagnosticProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@EnableDiscoveryClient
@EnableConfigurationProperties(DiagnosticProperties.class)
@ComponentScan(basePackages = {
        "com.fathom.services.userprofile",
        "com.fathom.diagnostics"
})
public class Application {
  public static void main(String[] args) {
    SpringApplication.run(Application.class, args);
  }
}
