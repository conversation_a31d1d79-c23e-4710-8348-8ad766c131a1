package com.fathom.services.user.profile.utils.mapper;

import com.fathom.services.user.profile.domain.dto.DepartmentDTO;
import com.fathom.services.user.profile.domain.entity.Department;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(
    componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class DepartmentMapper {

  public static final DepartmentMapper INSTANCE = Mappers.getMapper(DepartmentMapper.class);

  @InheritInverseConfiguration
  public abstract DepartmentDTO entityToNewDTO(
      Department department, @Context CycleAvoidingMappingContext context);

  public abstract Department dtoToNewEntity(
      DepartmentDTO departmentDTO, @Context CycleAvoidingMappingContext context);
}
