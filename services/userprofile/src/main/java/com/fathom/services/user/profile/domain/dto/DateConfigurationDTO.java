package com.fathom.services.user.profile.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.fathom.services.user.profile.utils.Views;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DateConfigurationDTO implements Serializable {
  @JsonView(Views.Update.class)
  @JsonProperty("timeZone")
  private TimeZoneDTO timeZoneDTO;

  private Integer timeFormat;

  @JsonView(Views.Update.class)
  private String dateFormat;
}
