package com.fathom.services.user.profile.controller.impl;

import static com.fathom.services.user.profile.controller.impl.ProfileInfoControllerImpl.EMAIL_HEADER_NAME;

import com.fathom.services.user.profile.controller.ProfileImageController;
import com.fathom.services.user.profile.domain.dto.ProfileImageDTO;
import com.fathom.services.user.profile.service.ProfileImageService;
import com.fathom.services.user.profile.utils.validator.ValidImage;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(path = "/photos")
@ConditionalOnExpression("${controllers.image.enable}")
@Validated
@Slf4j
public class ProfileImageControllerImpl implements ProfileImageController {

  @Autowired private ProfileImageService profileImageService;

  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public ProfileImageDTO uplaodImage(
      @RequestHeader(name = EMAIL_HEADER_NAME) String email,
      @ValidImage @RequestParam MultipartFile file)
      throws IOException {
    log.info("Received POST profile image request from user with Email: {} ", email);
    return profileImageService.setAvatar(email, file);
  }

  @DeleteMapping("/{id}")
  public void deleteImage(
      @RequestHeader(name = EMAIL_HEADER_NAME) String email, @PathVariable String id)
      throws IOException {
    log.info(
        "Received DELETE profile image request from user with Email: {} and image ID:{} ",
        email,
        id);
    profileImageService.removeAvatar(email, id);
  }

  @GetMapping
  public ProfileImageDTO findImage(@RequestHeader(name = EMAIL_HEADER_NAME) String email)
      throws IOException {
    log.info("Received GET profile image request from user with Email: {} ", email);
    return profileImageService.getAvatar(email);
  }
}
