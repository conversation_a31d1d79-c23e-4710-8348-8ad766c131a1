package com.fathom.services.user.profile.controller.advice;

import static org.springframework.http.HttpStatus.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.fathom.services.user.profile.domain.dto.ErrorDTO;
import com.fathom.services.user.profile.exception.ContractException;
import com.fathom.services.user.profile.exception.InvalidDataException;
import com.fathom.services.user.profile.exception.NotFoundException;
import jakarta.validation.ConstraintViolationException;
import java.io.IOException;
import java.time.DateTimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.client.HttpClientErrorException;

@RestControllerAdvice
@Slf4j
public class AdviceController {

  @ExceptionHandler({NotFoundException.class})
  public ResponseEntity<ErrorDTO> notFound(ContractException ex) {
    log.error("NotFoundException is thrown ", ex);
    return convert(ex, NOT_FOUND);
  }

  @ExceptionHandler({MethodArgumentNotValidException.class})
  public ResponseEntity<ErrorDTO> handleMethodArgumentNotValidException(
      MethodArgumentNotValidException ex) {
    log.error("MethodArgumentNotValidException is thrown ", ex);
    return convert(ex, BAD_REQUEST);
  }

  @ExceptionHandler({JsonProcessingException.class})
  public ResponseEntity<ErrorDTO> handleJsonProcessingException(JsonProcessingException ex) {
    log.error("JsonProcessingException is thrown ", ex);
    return convert(ex, BAD_REQUEST);
  }

  @ExceptionHandler({DateTimeException.class})
  public ResponseEntity<ErrorDTO> handleDateTimeException(DateTimeException ex) {
    log.error("DateTimeException is thrown ", ex);
    return convert(ex, BAD_REQUEST);
  }

  @ExceptionHandler({IOException.class})
  public ResponseEntity<ErrorDTO> handleIOException(IOException ex) {
    log.error("IOException is thrown ", ex);
    return convert(ex);
  }

  @ExceptionHandler({IllegalStateException.class})
  public ResponseEntity<ErrorDTO> handleIllegalStateExceptionException(IllegalStateException ex) {
    log.error("IllegalStateException is thrown ", ex);
    return convert(ex);
  }

  @ExceptionHandler({InvalidDataException.class})
  public ResponseEntity<ErrorDTO> badRequest(ContractException ex) {
    log.error("InvalidDataException is thrown ", ex);
    return convert(ex, BAD_REQUEST);
  }

  @ExceptionHandler(HttpClientErrorException.class)
  public ResponseEntity<ErrorDTO> httpClientError(HttpClientErrorException exc) {
    log.error("HttpClientErrorException is thrown ", exc);
    return convert(exc, (HttpStatus) exc.getStatusCode());
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<ErrorDTO> internalError(Exception exc) {
    log.error("Exception is thrown ", exc);
    return convert(exc);
  }

  @ExceptionHandler(MissingRequestHeaderException.class)
  public ResponseEntity<ErrorDTO> internalError(MissingRequestHeaderException exc) {
    log.error("MissingRequestHeaderException is thrown ", exc);
    return convert(exc, BAD_REQUEST);
  }

  @ExceptionHandler(DataIntegrityViolationException.class)
  public ResponseEntity<ErrorDTO> internalError(DataIntegrityViolationException exc) {
    log.error("DataIntegrityViolationException is thrown ", exc);
    return convert(exc, BAD_REQUEST);
  }

  @ExceptionHandler(ConstraintViolationException.class)
  public ResponseEntity<ErrorDTO> internalError(ConstraintViolationException exc) {
    log.error("ConstraintViolationException is thrown ", exc);
    return convert(exc, BAD_REQUEST);
  }

  @ExceptionHandler(InvalidFormatException.class)
  public ResponseEntity<ErrorDTO> internalError(InvalidFormatException exc) {
    log.error("InvalidFormatException is thrown ", exc);
    return convert(exc, BAD_REQUEST);
  }

  @ExceptionHandler(HttpMessageNotReadableException.class)
  public ResponseEntity<ErrorDTO> internalError(HttpMessageNotReadableException exc) {
    log.error("HttpMessageNotReadableException is thrown ", exc);
    return convert(exc, BAD_REQUEST);
  }

  private ResponseEntity<ErrorDTO> convert(Exception ex) {
    return convert(ex, INTERNAL_SERVER_ERROR);
  }

  private ResponseEntity<ErrorDTO> convert(Exception ex, HttpStatus httpStatus) {
    String message = ex.getMessage();
    ErrorDTO error = new ErrorDTO();
    error.setMessage(message);
    error.setStatus(httpStatus.toString());
    error.setError(ex.getClass().getCanonicalName());
    return new ResponseEntity<>(error, httpStatus);
  }
}
