package com.fathom.services.user.profile.config.constants;

public final class SwaggerMessage {
  private SwaggerMessage() {}

  public static final String ENDPOINT_GET_PROFILE =
      "Endpoint for get user profile by provided email";
  public static final String ENDPOINT_POST_PROFILE_SUBORDINATES =
      "Endpoint for set user profile subordinates by provided emails";
  public static final String ENDPOINT_POST_PROFILE_DEPARTMENTS =
      "Endpoint for set user profile departments by provided emails";
  public static final String ENDPOINT_DELETE_PROFILE_SUBORDINATES =
      "Endpoint for delete user profile subordinates by provided emails";
  public static final String ENDPOINT_DELETE_PROFILE_DEPARTMENTS =
      "Endpoint for delete user profile departments by provided emails";
  public static final String ENDPOINT_GET_PROFILE_SUBORDINATES =
      "Endpoint for get user profile subordinates.";
  public static final String ENDPOINT_GET_PROFILE_DEPARTMENTS =
      "Endpoint for get user profile departments.";
  public static final String ENDPOINT_GET_ALL_PROFILES =
      "Endpoint for get all users profile for superuser";
  public static final String ENDPOINT_GET_ALL_PROFILES_EMAILS =
      "Endpoint for get all emails of registered users";
  public static final String ENDPOINT_DELETE_PROFILE_BY_ID =
      "Endpoint for delete user profile by profileId";
  public static final String ENDPOINT_PUT_UPDATE_PROFILE_BY_ID =
      "Endpoint for updating user profile by Id";
  public static final String ENDPOINT_CREATE_PROFILE = "Endpoint for creating user profile";
  public static final String ENDPOINT_CREATE_DEPARTMENT = "Endpoint for creating department";
  public static final String ENDPOINT_UPDATE_DEPARTMENT = "Endpoint for updating department";
  public static final String ENDPOINT_DELETE_DEPARTMENT = "Endpoint for deleting department";
  public static final String ENDPOINT_GET_ALL_DEPARTMENTS =
      "Endpoint for viewing all existing departments";
  public static final String ENDPOINT_GET_DEPARTMENT_BY_ID =
      "Endpoint for get department by provided department id";
  public static final String ENDPOINT_CREATE_TIME_ZONE_LIST =
      "Endpoint for get a list of all time zones.";
  public static final String ENDPOINT_GET_TIME_CONFIGURATION =
      "Endpoint for get time configuration.";
  public static final String ENDPOINT_EMAIL_PARAM = "User email param. Case sensitive";
  public static final String ENDPOINT_ORGANIZATION_ID_PARAM = "User's organization Id param.";
  public static final String ENDPOINT_IS_SEARCH_BY_PROVIDED_ORG =
      "Param to filter by chosen organization or by assigned";
  public static final String ENDPOINT_ORGANIZATION_ID =
      "Organization Id param that was assign to user with authorization token.";
  public static final String ENDPOINT_CHOSEN_ORGANIZATION_ID_PARAM =
      "Organization Id param for filtering.";
  public static final String ENDPOINT_SUBORDINATES_IDS_PARAM = "Subordinates Ids param.";
  public static final String ENDPOINT_DEPARTMENTS_ID_PARAM = "Department(s) id param. Integer";
  public static final String ENDPOINT_IMAGE_ID_PARAM = "Image ID param Case sensitive";
  public static final String ENDPOINT_PROFILE_ID_PARAM = "User profile id param. UUID";
  public static final String ENDPOINT_DEPARTMENT_ID_PARAM = "Department id param. Integer";
  public static final String ENDPOINT_GET_PROFILE_IMAGE_BY_EMAIL =
      "Endpoint for get user image by email";
  public static final String ENDPOINT_DELETE_PROFILE_IMAGE_BY_EMAIL =
      "Endpoint for delete user image by email";

  public static final String MODEL_CREATE_USER_PROFILE_DTO_CLASS = "Create user profile DTO";
  public static final String MODEL_USER_PROFILE_DTO_CLASS = "Information about user profile";

  // (PageDTO)
  public static final String MODEL_CONTENT_FIELD = "Contains data of selected page.";
  public static final String MODEL_PAGE_NUMBER_FIELD = "Displays number of current page.";
  public static final String MODEL_PAGE_SIZE_FIELD = "Displays current page size.";
  public static final String MODEL_PAGES_QUANTITY_FIELD = "Displays overall pages quantity.";
  public static final String MODEL_TOTAL_COUNT_FIELD =
      "Displays total count of objects from all pages.";

  public static final String MODEL_PAGE_DTO_CLASS = "General data transfer object for pagination.";
  public static final String ENDPOINT_PAGE_NUMBER_PARAM =
      "Specifies the number of page to be displayed.";
  public static final String ENDPOINT_PAGE_SIZE_PARAM =
      "Specifies the number of entities that one page can contain.";
}
