package com.fathom.services.user.profile.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

@MappedSuperclass
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class NamedEntity extends BaseEntity {
  @NotBlank
  @Column(name = "name", nullable = false)
  protected String name;
}
