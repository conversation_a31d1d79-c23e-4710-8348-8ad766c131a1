package com.fathom.services.user.profile.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.PrePersist;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.sql.Types;
import java.util.Objects;
import java.util.UUID;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;

@Data
@Embeddable
@NoArgsConstructor
public class CompositeId implements Serializable {

  @Column(name = "profile_id", unique = true, nullable = false, updatable = false)
  @JdbcTypeCode(Types.VARCHAR)
  private UUID profileId;

  @Column(nullable = false, unique = true)
  @Email
  @NotBlank
  private String email;

  public CompositeId(UUID profileId, String email) {
    this.profileId = profileId;
    this.email = email;
  }

  public CompositeId(String profileId, String email) {
    this.profileId = UUID.fromString(profileId);
    this.email = email;
  }

  public CompositeId(String email) {
    this.profileId = UUID.randomUUID();
    this.email = email;
  }

  @PrePersist
  private void profilePrePersist() {
    if (Objects.isNull(this.profileId)) {
      this.profileId = UUID.randomUUID();
    }
  }
}
