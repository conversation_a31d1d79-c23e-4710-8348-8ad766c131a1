package com.fathom.services.user.profile.service.impl;

import static java.time.format.TextStyle.SHORT;
import static java.util.Locale.ENGLISH;

import com.fathom.services.user.profile.domain.dto.CityCountryDto;
import com.fathom.services.user.profile.domain.dto.DateConfigurationDTO;
import com.fathom.services.user.profile.domain.dto.ProfileInfoDTO;
import com.fathom.services.user.profile.domain.dto.TimeZoneDTO;
import com.fathom.services.user.profile.service.DateService;
import com.fathom.services.user.profile.service.ProfileInfoService;
import com.fathom.services.user.profile.utils.converter.DateUtils;
import com.google.common.collect.Sets;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DateServiceImpl implements DateService {

  @Autowired private ProfileInfoService profileInfoService;
  @Autowired private ResourceLoader resourceLoader;

  public List<TimeZoneDTO> createTimeZoneList() {
    Set<String> allZones = new TreeSet<>(ZoneId.getAvailableZoneIds());
    try {
      InputStream inputStream =
          resourceLoader
              .getResource("classpath:timezones/canonical_timezones.txt")
              .getInputStream();
      File tmp = File.createTempFile(this.getClass().getSimpleName(), null);
      FileUtils.copyInputStreamToFile(inputStream, tmp);
      Set<String> canonicalZones = new HashSet<>(FileUtils.readLines(tmp, StandardCharsets.UTF_8));
      allZones = allZones.stream().filter(canonicalZones::contains).collect(Collectors.toSet());
    } catch (IOException ioex) {
      log.error(
          " Cannot read file with canonical timeZones. Will use full IANA list of timezones. IOException is thrown : {}",
          ioex.getMessage());
    }
    List<TimeZoneDTO> timeZoneDTOList = new ArrayList<>();
    String name;
    String offset;
    String abbr;
    for (String zoneId : Sets.newTreeSet(allZones)) {
      name = ZoneId.of(zoneId).getId().replace("/", ": ");
      offset = formatTimeOffset(DateUtils.getOffset(LocalDateTime.now(), ZoneId.of(zoneId)));
      abbr = getTimeZoneAbbreviation(zoneId);
      timeZoneDTOList.add(new TimeZoneDTO(name, offset, abbr));
    }
    return timeZoneDTOList;
  }

  @Override
  public DateConfigurationDTO getTimeConfiguration(String email) {
    DateConfigurationDTO timeConfigurationDTO = new DateConfigurationDTO();
    ProfileInfoDTO userProfile = profileInfoService.findUserProfile(email);
    if (Objects.isNull(userProfile)) {
      log.info("Profile for user with Email:{} was not found in database.", email);
      return null;
    }
    timeConfigurationDTO.setTimeZoneDTO(userProfile.getTimeZone());
    timeConfigurationDTO.setDateFormat(userProfile.getDateFormat());
    timeConfigurationDTO.setTimeFormat(userProfile.getTimeFormat());
    return timeConfigurationDTO;
  }

  @Override
  public List<CityCountryDto> getCities() {
    try {
      InputStream inputStream =
          resourceLoader.getResource("classpath:cities/world_cities_txt.txt").getInputStream();
      File tmp = File.createTempFile(this.getClass().getSimpleName(), null);
      FileUtils.copyInputStreamToFile(inputStream, tmp);
      Set<String> cites = new HashSet<>(FileUtils.readLines(tmp, StandardCharsets.UTF_8));

      return cites.stream()
          .map(x -> x.split(","))
          .map(x -> new CityCountryDto(x[0], x[1], x[2], Integer.parseInt(x[3])))
          .toList();

    } catch (IOException ioex) {
      log.error(
          " Cannot read file with cities. Will use full IANA list of timezones. IOException is thrown : {}",
          ioex.getMessage());
    }
    return List.of();
  }

  private String formatTimeOffset(String offset) {
    if (offset.length() != 5
        || (offset.charAt(0) != '+' && offset.charAt(0) != '-')
        || !offset.substring(1).matches("\\d{4}")) {
      throw new IllegalArgumentException("Invalid time offset format");
    }

    String sign = offset.substring(0, 1);
    String hours = offset.substring(1, 3);
    String minutes = offset.substring(3);

    return sign + hours + ":" + minutes;
  }

  private String getTimeZoneAbbreviation(String zoneId) {
    return ZoneId.of(zoneId).getDisplayName(SHORT, ENGLISH).split("[+-]")[0];
  }
}
