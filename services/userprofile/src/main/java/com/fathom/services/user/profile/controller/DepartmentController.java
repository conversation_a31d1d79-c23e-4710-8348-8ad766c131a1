package com.fathom.services.user.profile.controller;

import static com.fathom.services.user.profile.config.constants.SwaggerMessage.*;

import com.fasterxml.jackson.annotation.JsonView;
import com.fathom.services.user.profile.config.constants.SwaggerMessage;
import com.fathom.services.user.profile.domain.dto.DepartmentDTO;
import com.fathom.services.user.profile.domain.dto.PageDTO;
import com.fathom.services.user.profile.utils.Views;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.constraints.Min;
import java.io.IOException;
import java.util.UUID;

public interface DepartmentController {

  @Operation(summary = "Create department", description = SwaggerMessage.ENDPOINT_CREATE_DEPARTMENT)
  @JsonView(Views.Update.class)
  DepartmentDTO create(
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @JsonView(Views.HasId.class) @Parameter DepartmentDTO departmentDTO)
      throws IOException;

  @Operation(
      summary = "Put update department",
      description = SwaggerMessage.ENDPOINT_UPDATE_DEPARTMENT)
  @JsonView({Views.Update.class})
  DepartmentDTO update(
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_DEPARTMENT_ID_PARAM) Integer departmentId,
      @JsonView(Views.HasId.class) @Parameter DepartmentDTO departmentDTO)
      throws IOException;

  @Operation(summary = "Delete department", description = ENDPOINT_DELETE_DEPARTMENT)
  void delete(
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_DEPARTMENT_ID_PARAM) Integer departmentId);

  @Operation(summary = "Get all departments", description = ENDPOINT_GET_ALL_DEPARTMENTS)
  @JsonView(Views.Update.class)
  PageDTO viewAll(
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_ORGANIZATION_ID) UUID orgId,
      @Parameter(description = ENDPOINT_IS_SEARCH_BY_PROVIDED_ORG) boolean byOrgId,
      @Parameter(description = ENDPOINT_CHOSEN_ORGANIZATION_ID_PARAM) UUID chosenOrgId,
      @Parameter(description = ENDPOINT_PAGE_NUMBER_PARAM) @Min(1) Integer pageNumber,
      @Parameter(description = ENDPOINT_PAGE_SIZE_PARAM) Integer pageSize);

  @Operation(summary = "Get department by Id", description = ENDPOINT_GET_DEPARTMENT_BY_ID)
  @JsonView(Views.Update.class)
  DepartmentDTO view(
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_DEPARTMENT_ID_PARAM) Integer departmentId);
}
