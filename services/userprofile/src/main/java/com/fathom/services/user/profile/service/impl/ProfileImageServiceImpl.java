package com.fathom.services.user.profile.service.impl;

import static com.google.common.base.Strings.isNullOrEmpty;
import static java.util.Objects.isNull;

import com.fathom.services.user.profile.domain.dto.ProfileImageDTO;
import com.fathom.services.user.profile.domain.entity.ProfileInfo;
import com.fathom.services.user.profile.domain.repository.ProfileInfoRepository;
import com.fathom.services.user.profile.exception.InvalidDataException;
import com.fathom.services.user.profile.service.ProfileImageService;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.client.gridfs.model.GridFSFile;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsOperations;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
public class ProfileImageServiceImpl implements ProfileImageService {
  @Value("${image.path}")
  private String imagePath;

  @Value("${image.extention}")
  private String imageExtention;

  private static final String IMAGE_PREFIX = "data:image/png;base64, ";
  private static final String PROFILE_NOT_FOUND = "Profile was not found in the database. Email:{}";

  @Autowired private ProfileInfoRepository profileRepository;

  @Autowired private GridFsTemplate gridFsTemplate;

  @Autowired private GridFsOperations operations;
  @Autowired ResourceLoader resourceLoader;

  @Override
  public ProfileImageDTO getAvatar(String email) throws IOException {
    ProfileInfo profile = getProfileBy(email);
    if (Objects.isNull(profile)) {
      log.info(PROFILE_NOT_FOUND, email);
      return null;
    }
    log.info("Searching image for user email:{}", email);
    String pictureId = profile.getPictureId();
    ProfileImageDTO profileImageDTO = new ProfileImageDTO();
    if (isNullOrEmpty(pictureId)) {
      try (InputStream input =
          resourceLoader
              .getResource(imagePath + profile.getGender().getCode() + imageExtention)
              .getInputStream()) {
        profileImageDTO.setImage(
            IMAGE_PREFIX + Base64.getEncoder().encodeToString(IOUtils.toByteArray(input)));
        return profileImageDTO;
      } catch (IOException e) {
        log.error("Profile image default was not found in the database. Email:{}", email);
        log.error("IOException is thrown:{}", e.getMessage());
        return null;
      }
    }
    GridFSFile file = gridFsTemplate.findOne(new Query(Criteria.where("_id").is(pictureId)));
    InputStream is = operations.getResource(file).getInputStream();
    profileImageDTO.setImage(
        IMAGE_PREFIX + Base64.getEncoder().encodeToString(IOUtils.toByteArray(is)));
    profileImageDTO.setPictureId(pictureId);
    return profileImageDTO;
  }

  @Override
  public void removeAvatar(String email, String id) {
    if (isNull(id)) {
      log.debug("Attempt to remove picture with ID:{}", id);
      throw InvalidDataException.conditionsIsNotCorrect();
    }

    ProfileInfo profile = getProfileBy(email);
    if (Objects.isNull(profile)) {
      log.info(PROFILE_NOT_FOUND, email);
      return;
    }
    String storedPictureId = profile.getPictureId();
    if (!id.equalsIgnoreCase(storedPictureId)) {
      throw InvalidDataException.attemptToDeletePictureNotBelongToUser();
    }
    gridFsTemplate.delete(new Query(Criteria.where("_id").is(id)));
    setProfilePictureBy(email, null);
  }

  @Override
  @Transactional
  public ProfileImageDTO setAvatar(String email, MultipartFile multipartFile) throws IOException {
    ProfileInfo profile = getProfileBy(email);
    if (Objects.isNull(profile)) {
      log.info(PROFILE_NOT_FOUND, email);
      return null;
    }
    DBObject metaData = new BasicDBObject();
    metaData.put("user", email);
    if (!isNull(profile.getPictureId())) {
      gridFsTemplate.delete(new Query(Criteria.where("_id").is(profile.getPictureId())));
    }
    ObjectId id =
        gridFsTemplate.store(
            multipartFile.getInputStream(),
            profile.getCompositeId().getProfileId() + imageExtention,
            multipartFile.getContentType(),
            metaData);
    profile.setPictureId(id.toString());
    profileRepository.save(profile);
    ProfileImageDTO profileImageDTO = new ProfileImageDTO();
    profileImageDTO.setPictureId(id.toString());
    profileImageDTO.setImage(
        IMAGE_PREFIX
            + Base64.getEncoder()
                .encodeToString(IOUtils.toByteArray(multipartFile.getInputStream())));
    return profileImageDTO;
  }

  private ProfileInfo getProfileBy(String email) {
    ProfileInfo profile = profileRepository.findByEmail(email);
    if (isNull(profile)) {
      log.info(PROFILE_NOT_FOUND, email);
      return null;
    }
    return profile;
  }

  private ProfileInfo setProfilePictureBy(String email, String pictureId) {
    ProfileInfo profile = profileRepository.findByEmail(email);
    if (isNull(profile)) {
      log.info(PROFILE_NOT_FOUND, email);
      return null;
    }
    profile.setPictureId(pictureId);
    profileRepository.save(profile);
    return profile;
  }
}
