package com.fathom.services.user.profile.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonView;
import com.fathom.services.user.profile.config.constants.SwaggerMessage;
import com.fathom.services.user.profile.utils.Views;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = SwaggerMessage.MODEL_CREATE_USER_PROFILE_DTO_CLASS)
@JsonIgnoreProperties(value = {"profileId"})
@JsonView(Views.Create.class)
public class UpdateProfileInfoDTO extends ProfileInfoDTO {}
