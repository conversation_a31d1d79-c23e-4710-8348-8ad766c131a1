package com.fathom.services.user.profile.domain.dto;

import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ProfileContactDTO implements Serializable {
  private String firstName;
  private String lastName;
  private UUID profileId;
}
