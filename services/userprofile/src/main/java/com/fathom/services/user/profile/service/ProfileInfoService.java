package com.fathom.services.user.profile.service;

import com.fathom.services.user.profile.domain.dto.*;
import com.fathom.services.user.profile.utils.DepartmentRole;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

public interface ProfileInfoService {
  PageDTO findAllUserProfiles(String email, Integer pageNumber, Integer pageSize);

  ProfileInfoDTO findUserProfile(String email);

  void deleteUserProfile(UUID profileId, String email);

  ProfileInfoDTO createUserFrom(ProfileInfoDTO profileInfoDTO, String email) throws IOException;

  ProfileInfoDTO updateUserFrom(ProfileInfoDTO profileInfoDTO, String email, UUID profileId)
      throws IOException;

  void updateSubordinates(
      UUID profileId, String email, UUID organizationId, Set<UUID> subordinateIds);

  void deleteSubordinates(
      UUID profileId, String email, UUID organizationId, Set<UUID> subordinateIds);

  PageDTO findSubordinates(
      UUID profileId, String email, UUID organizationId, Integer pageNumber, Integer pageSize);

  List<ProfileContactDTO> finAllSubordinatesContacts(String email, UUID organizationId);

  PageDTO<DepartmentDTO> findDepartments(
      UUID profileId, String email, UUID orgId, Integer pageNumber, Integer pageSize);

  Map<DepartmentRole, Set<DepartmentDTO>> saveDepartments(
      UUID profileId, String email, UUID orgId, List<UpdateDepartmentDTO> departments);

  void deleteDepartments(
      UUID profileId, String email, UUID orgId, List<UpdateDepartmentDTO> departments);

  List<ProfileInfoDTO> findManyUsers(Set<String> emails);
}
