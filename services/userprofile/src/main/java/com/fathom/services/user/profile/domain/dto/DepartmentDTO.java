package com.fathom.services.user.profile.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonView;
import com.fathom.services.user.profile.utils.DepartmentRole;
import com.fathom.services.user.profile.utils.Views;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DepartmentDTO implements Serializable {
  @JsonView(Views.Update.class)
  private Integer id;

  private String name;
  private String abbr;
  private String description;
  @NotNull private UUID organizationId;

  @JsonView(Views.Create.class)
  private DepartmentRole typeOfParticipant;
}
