package com.fathom.services.user.profile.domain.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fathom.services.user.profile.utils.validator.DateValidator;
import com.google.common.base.Strings;
import jakarta.persistence.*;
import jakarta.validation.constraints.Past;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import java.sql.Types;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.JdbcTypeCode;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;

@Data
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
@Entity
@Table(
    name = "user_profile",
    uniqueConstraints = @UniqueConstraint(columnNames = {"profile_id", "email"}))
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProfileInfo implements Serializable {
  @EmbeddedId @EqualsAndHashCode.Include private CompositeId compositeId;

  @Column(name = "first_name")
  @Size(min = 2, max = 100)
  @Pattern(regexp = "[^0-9]*")
  @EqualsAndHashCode.Include
  private String firstName;

  @Column(name = "last_name")
  @Size(min = 2, max = 100)
  @Pattern(regexp = "[^0-9]*")
  @EqualsAndHashCode.Include
  private String lastName;

  @Column(name = "birthday")
  @Past
  @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z' ")
  @EqualsAndHashCode.Include
  private LocalDateTime birthday;

  @JsonManagedReference
  @OneToMany(mappedBy = "profile", cascade = CascadeType.ALL)
  private List<HomeAddress> homeAddress;

  @OneToOne(cascade = CascadeType.ALL)
  @JoinColumn(name = "office_address_id")
  private OfficeAddress officeAddress;

  @Column(name = "time_zone")
  @EqualsAndHashCode.Include
  private ZoneId zoneId;

  @Column(name = "picture_id")
  @EqualsAndHashCode.Include
  private String pictureId;

  @Column(name = "date_format")
  @EqualsAndHashCode.Include
  private String dateFormat;

  @EqualsAndHashCode.Include private Gender gender;

  @Column(name = "phone")
  @EqualsAndHashCode.Include
  private String phoneNumber;

  @Column(name = "language")
  @EqualsAndHashCode.Include
  private String language;

  @Column(name = "job_title")
  @EqualsAndHashCode.Include
  private String jobTitle;

  @Column(name = "job_role")
  @EqualsAndHashCode.Include
  private String jobRole;

  @Column(name = "time_format")
  @EqualsAndHashCode.Include
  private Integer timeFormat;

  @ManyToOne @JsonBackReference private ProfileInfo manager;

  @OneToMany(mappedBy = "manager", fetch = FetchType.LAZY)
  private List<ProfileInfo> subordinates;

  @Column(name = "default_organization_id")
  @EqualsAndHashCode.Include
  @JdbcTypeCode(Types.VARCHAR)
  private UUID defaultOrganizationId;

  @ManyToMany(
      fetch = FetchType.LAZY,
      cascade = {CascadeType.PERSIST, CascadeType.MERGE},
      mappedBy = "profiles")
  private Set<Department> departments = new HashSet<>();

  @OneToMany(mappedBy = "departmentManager", fetch = FetchType.LAZY)
  private Set<Department> managedDepartments = new HashSet<>();

  @PrePersist
  void prePersist() {
    if (Objects.isNull(timeFormat) || (timeFormat > 12 && timeFormat < 24)) {
      timeFormat = 24;
    }
    if (Strings.isNullOrEmpty(this.dateFormat) || !DateValidator.isValid(this.dateFormat)) {
      this.dateFormat = "MMM dd, yyyy";
    }
  }

  @PreUpdate
  void preUpdate() {
    if (Objects.isNull(timeFormat) || (timeFormat > 12 && timeFormat < 24)) {
      timeFormat = 24;
    }
    if (Strings.isNullOrEmpty(this.dateFormat) || !DateValidator.isValid(this.dateFormat)) {
      this.dateFormat = "MMM dd, yyyy";
    }
  }

  @PreRemove
  private void preRemove() {
    if (!CollectionUtils.isEmpty(this.departments)) {
      this.departments.forEach(department -> department.getProfiles().remove(this));
    }
    if (!CollectionUtils.isEmpty(this.homeAddress)) {
      this.homeAddress.forEach(home -> home.setProfile(null));
    }
    if (!CollectionUtils.isEmpty(this.subordinates)) {
      this.subordinates.forEach(subordinate -> subordinate.setManager(null));
    }
  }
}
