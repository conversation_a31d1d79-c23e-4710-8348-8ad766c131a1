package com.fathom.services.user.profile.controller;

import static com.fathom.services.user.profile.config.constants.SwaggerMessage.*;

import com.fasterxml.jackson.annotation.JsonView;
import com.fathom.services.user.profile.config.constants.SwaggerMessage;
import com.fathom.services.user.profile.domain.dto.*;
import com.fathom.services.user.profile.utils.DepartmentRole;
import com.fathom.services.user.profile.utils.Views;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

public interface ProfileInfoController {

  @Operation(summary = "Create user profile", description = SwaggerMessage.ENDPOINT_CREATE_PROFILE)
  @JsonView({Views.Update.class})
  ProfileInfoDTO createUserProfile(
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @JsonView(Views.Create.class) @Parameter ProfileInfoDTO profileInfoDTO)
      throws IOException;

  @Operation(summary = "Put update user profile", description = ENDPOINT_PUT_UPDATE_PROFILE_BY_ID)
  @JsonView(Views.Update.class)
  ProfileInfoDTO updateUserProfile(
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_PROFILE_ID_PARAM) UUID profileId,
      @JsonView(Views.Update.class) @Parameter(description = MODEL_USER_PROFILE_DTO_CLASS)
          ProfileInfoDTO profileInfoDTO)
      throws IOException;

  @Operation(
      summary = "Delete user profile by profile id",
      description = ENDPOINT_DELETE_PROFILE_BY_ID)
  void deleteUserProfile(
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_PROFILE_ID_PARAM) UUID profileId);

  @Operation(
      summary = "Get all user's profiles by email for Superuser",
      description = ENDPOINT_GET_ALL_PROFILES)
  PageDTO viewAllUserProfiles(
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_PAGE_NUMBER_PARAM) @Min(1) Integer pageNumber,
      @Parameter(description = ENDPOINT_PAGE_SIZE_PARAM) Integer pageSize);

  @Operation(summary = "Get user profile by email", description = ENDPOINT_GET_PROFILE)
  @JsonView(Views.Update.class)
  ProfileInfoDTO viewUserProfiles(@Parameter(description = ENDPOINT_EMAIL_PARAM) String email);

  @Operation(summary = "Get user profile by emails", description = ENDPOINT_GET_PROFILE)
  @JsonView(Views.Update.class)
  List<ProfileInfoDTO> viewUserProfiles(
      @Parameter(description = ENDPOINT_EMAIL_PARAM) Set<String> emails);

  @Operation(
      summary = "Post user profile subordinates",
      description = ENDPOINT_POST_PROFILE_SUBORDINATES)
  void addSubordinates(
      @Parameter(description = ENDPOINT_PROFILE_ID_PARAM) UUID profileId,
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_ORGANIZATION_ID_PARAM) UUID orgId,
      @Parameter(description = ENDPOINT_SUBORDINATES_IDS_PARAM) Set<UUID> subordinateIds);

  @Operation(
      summary = "Delete user profile subordinates",
      description = ENDPOINT_DELETE_PROFILE_SUBORDINATES)
  void deleteSubordinates(
      @Parameter(description = ENDPOINT_PROFILE_ID_PARAM) UUID profileId,
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_ORGANIZATION_ID_PARAM) UUID orgId,
      @Parameter(description = ENDPOINT_SUBORDINATES_IDS_PARAM) Set<UUID> profileIds);

  @Operation(
      summary = "Get user profile subordinates",
      description = ENDPOINT_GET_PROFILE_SUBORDINATES)
  public PageDTO findSubordinates(
      @Parameter(description = ENDPOINT_PROFILE_ID_PARAM) UUID profileId,
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_ORGANIZATION_ID_PARAM) UUID orgId,
      @Parameter(description = ENDPOINT_PAGE_NUMBER_PARAM) @Min(1) Integer pageNumber,
      @Parameter(description = ENDPOINT_PAGE_SIZE_PARAM) Integer pageSize);

  @Operation(
      summary = "Get all possible subordinates contacts.",
      description = ENDPOINT_GET_ALL_PROFILES_EMAILS)
  List<ProfileContactDTO> viewAllEmails(
      @Valid @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_ORGANIZATION_ID_PARAM) UUID orgId);

  @Operation(
      summary = "Get user profile departments",
      description = ENDPOINT_GET_PROFILE_DEPARTMENTS)
  PageDTO<DepartmentDTO> findDepartments(
      @Parameter(description = ENDPOINT_PROFILE_ID_PARAM) UUID profileId,
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_ORGANIZATION_ID_PARAM) UUID orgId,
      @Parameter(description = ENDPOINT_IS_SEARCH_BY_PROVIDED_ORG) boolean byOrgId,
      @Parameter(description = ENDPOINT_CHOSEN_ORGANIZATION_ID_PARAM) UUID chosenOrgId,
      @Parameter(description = ENDPOINT_PAGE_NUMBER_PARAM) @Min(1) Integer pageNumber,
      @Parameter(description = ENDPOINT_PAGE_SIZE_PARAM) Integer pageSize);

  @Operation(
      summary = "Add user profile departments",
      description = ENDPOINT_POST_PROFILE_DEPARTMENTS)
  Map<DepartmentRole, Set<DepartmentDTO>> addDepartments(
      @Parameter(description = ENDPOINT_PROFILE_ID_PARAM) UUID profileId,
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_ORGANIZATION_ID_PARAM) UUID orgId,
      @JsonView(Views.Update.class) @Parameter(description = ENDPOINT_DEPARTMENTS_ID_PARAM)
          List<UpdateDepartmentDTO> departmentsIds);

  @Operation(
      summary = "Delete user profile departments",
      description = ENDPOINT_DELETE_PROFILE_DEPARTMENTS)
  void deleteDepartments(
      @Parameter(description = ENDPOINT_PROFILE_ID_PARAM) UUID profileId,
      @Parameter(description = ENDPOINT_EMAIL_PARAM) String email,
      @Parameter(description = ENDPOINT_ORGANIZATION_ID_PARAM) UUID orgId,
      @JsonView(Views.Update.class) @Parameter(description = ENDPOINT_DEPARTMENTS_ID_PARAM)
          List<UpdateDepartmentDTO> departmentsIds);
}
