package com.fathom.services.user.profile.exception;

public abstract class ContractException extends RuntimeException {

  private final String message;
  private transient Exception exception;
  private Object[] params;

  protected ContractException(String message) {
    this.message = message;
  }

  protected ContractException(String message, Object... params) {
    this.message = message;
    this.params = params;
  }

  @Override
  public String getMessage() {
    return message;
  }

  public Exception getException() {
    return exception;
  }

  public Object[] getParams() {
    return params;
  }

  public void setParams(Object... params) {
    this.params = params;
  }

  public void setException(Exception exception) {
    this.exception = exception;
  }
}
