package com.fathom.services.user.profile.controller.impl;

import com.fasterxml.jackson.annotation.JsonView;
import com.fathom.services.user.profile.controller.ProfileInfoController;
import com.fathom.services.user.profile.domain.dto.*;
import com.fathom.services.user.profile.service.ProfileInfoService;
import com.fathom.services.user.profile.utils.DepartmentRole;
import com.fathom.services.user.profile.utils.Views;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Min;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@RequestMapping(path = "/profiles")
@RestController
@Slf4j
public class ProfileInfoControllerImpl implements ProfileInfoController {
  @Autowired private ProfileInfoService profileInfoService;

  public static final String EMAIL_HEADER_NAME = "x-email";
  public static final String ORG_ID_HEADER_NAME = "x-organizationid";

  @Override
  @PostMapping(consumes = "application/json", produces = "application/json")
  @ResponseStatus(HttpStatus.CREATED)
  public ProfileInfoDTO createUserProfile(
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @RequestBody @Valid ProfileInfoDTO profileInfoDTO)
      throws IOException {
    log.info(
        "Received POST request from user with Email: {}, ProfileInfoDTO:{}", email, profileInfoDTO);
    return profileInfoService.createUserFrom(profileInfoDTO, email);
  }

  @Override
  @PutMapping(path = "/{profile_id}")
  public ProfileInfoDTO updateUserProfile(
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @PathVariable("profile_id") UUID profileId,
      @RequestBody ProfileInfoDTO profileInfoDTO)
      throws IOException {
    log.info(
        "Received PUT request from user with Email: {}, ProfileID: {}, ProfileInfoDTO:{}",
        email,
        profileId,
        profileInfoDTO);
    return profileInfoService.updateUserFrom(profileInfoDTO, email, profileId);
  }

  @Override
  @DeleteMapping("/{profile_id}")
  public void deleteUserProfile(
      @RequestHeader(EMAIL_HEADER_NAME) String email, @PathVariable("profile_id") UUID profileId) {
    log.info(
        "Received DELETE request from user with Email: {} and ProfileID: {}.", email, profileId);
    profileInfoService.deleteUserProfile(profileId, email);
  }

  @Override
  @GetMapping("/retrieveAll")
  public PageDTO viewAllUserProfiles(
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @RequestParam(required = false, defaultValue = "1") @Min(1) Integer pageNumber,
      @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
    log.info("Received GET all profiles request from user with Email: {}", email);
    return profileInfoService.findAllUserProfiles(email, pageNumber, pageSize);
  }

  @Override
  @GetMapping(produces = MediaType.APPLICATION_JSON_VALUE)
  public ProfileInfoDTO viewUserProfiles(@RequestHeader(EMAIL_HEADER_NAME) String email) {
    log.info("Received GET profile request from user with Email: {} ", email);
    return profileInfoService.findUserProfile(email);
  }

  @Override
  @PostMapping("/retrieveManyByEmails")
  public List<ProfileInfoDTO> viewUserProfiles(@RequestBody Set<String> emails) {
    return profileInfoService.findManyUsers(emails);
  }

  @Override
  @GetMapping(path = "/{profile_id}/subordinates")
  public PageDTO findSubordinates(
      @PathVariable("profile_id") UUID profileId,
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @RequestHeader(ORG_ID_HEADER_NAME) UUID orgId,
      @RequestParam(required = false, defaultValue = "1") @Min(1) Integer pageNumber,
      @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
    log.info(
        "Received GET request from user with email:{} to get subordinates for profileId: {} ",
        email,
        profileId);
    return profileInfoService.findSubordinates(profileId, email, orgId, pageNumber, pageSize);
  }

  @Override
  @PostMapping(
      path = "/{profile_id}/subordinates",
      consumes = "application/json",
      produces = "application/json")
  @ResponseStatus(HttpStatus.CREATED)
  public void addSubordinates(
      @PathVariable("profile_id") UUID profileId,
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @RequestHeader(ORG_ID_HEADER_NAME) UUID orgId,
      @RequestBody Set<UUID> subordinateIds) {
    log.info(
        "Received POST request from user with email:{} to set subordinates with Ids:{} for profileId: {} ",
        email,
        subordinateIds,
        profileId);
    profileInfoService.updateSubordinates(profileId, email, orgId, subordinateIds);
  }

  @Override
  @DeleteMapping(path = "/{profile_id}/subordinates")
  public void deleteSubordinates(
      @PathVariable("profile_id") UUID profileId,
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @RequestHeader(ORG_ID_HEADER_NAME) UUID orgId,
      @RequestBody Set<UUID> subordinatesIds) {
    log.info(
        "Received DELETE request from user with email:{} to set subordinates for profileId: {} ",
        email,
        profileId);
    profileInfoService.deleteSubordinates(profileId, email, orgId, subordinatesIds);
  }

  @Override
  @GetMapping("/subordinates/contacts/available")
  public List<ProfileContactDTO> viewAllEmails(
      @Email @RequestHeader(EMAIL_HEADER_NAME) String email,
      @RequestHeader(ORG_ID_HEADER_NAME) UUID orgId) {
    log.info("Received GET all emails request from user with Email: {}", email);
    return profileInfoService.finAllSubordinatesContacts(email, orgId);
  }

  @Override
  @GetMapping(path = "/{profile_id}/departments")
  public PageDTO<DepartmentDTO> findDepartments(
      @PathVariable("profile_id") UUID profileId,
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @RequestHeader(ORG_ID_HEADER_NAME) UUID orgId,
      @RequestParam(required = false, defaultValue = "false") boolean byOrgId,
      @RequestParam(required = false) UUID chosenOrgId,
      @RequestParam(required = false, defaultValue = "1") @Min(1) Integer pageNumber,
      @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
    if (byOrgId && chosenOrgId != null) {
      log.info(
          "Received GET profile departments from user with Email: {} for ProfileId: {} for organization with OrgId:{}",
          email,
          profileId,
          chosenOrgId);
      return profileInfoService.findDepartments(
          profileId, email, chosenOrgId, pageNumber, pageSize);
    }
    if (byOrgId && chosenOrgId == null) {
      log.info(
          "Received GET profile departments from user with Email: {} for ProfileId: {}",
          email,
          profileId);
      return profileInfoService.findDepartments(profileId, email, null, pageNumber, pageSize);
    }
    log.info(
        "Received GET profile departments from user with Email: {} for ProfileId: {}",
        email,
        profileId);
    return profileInfoService.findDepartments(profileId, email, orgId, pageNumber, pageSize);
  }

  @Override
  @PostMapping(
      path = "/{profile_id}/departments",
      consumes = "application/json",
      produces = "application/json")
  @JsonView(Views.Update.class)
  public Map<DepartmentRole, Set<DepartmentDTO>> addDepartments(
      @PathVariable("profile_id") UUID profileId,
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @RequestHeader(ORG_ID_HEADER_NAME) UUID orgId,
      @RequestBody List<UpdateDepartmentDTO> departmentsIds) {
    log.info(
        "Received POST profile departments from user with Email: {} for ProfileId: {}",
        email,
        profileId);
    return profileInfoService.saveDepartments(profileId, email, orgId, departmentsIds);
  }

  @Override
  @DeleteMapping(path = "/{profile_id}/departments")
  public void deleteDepartments(
      @PathVariable("profile_id") UUID profileId,
      @RequestHeader(EMAIL_HEADER_NAME) String email,
      @RequestHeader(ORG_ID_HEADER_NAME) UUID orgId,
      @RequestBody List<UpdateDepartmentDTO> departmentsIds) {
    log.info(
        "Received DELETE profile departments from user with Email: {} for ProfileId: {}",
        email,
        profileId);
    profileInfoService.deleteDepartments(profileId, email, orgId, departmentsIds);
  }
}
