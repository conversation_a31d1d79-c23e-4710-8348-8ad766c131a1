package com.fathom.services.user.profile.utils.validator;

import static com.fathom.services.user.profile.config.constants.ErrorMessage.ALLOWED_IMAGES_EXTENTIONS;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.web.multipart.MultipartFile;

public class ImageFileValidator implements ConstraintValidator<ValidImage, MultipartFile> {

  @Override
  public void initialize(ValidImage constraintAnnotation) {}

  @Override
  public boolean isValid(MultipartFile multipartFile, ConstraintValidatorContext context) {

    boolean result = Boolean.TRUE;

    String contentType = multipartFile.getContentType();
    if (!isSupportedContentType(contentType)) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(ALLOWED_IMAGES_EXTENTIONS)
          .addConstraintViolation();
      result = Boolean.FALSE;
    }
    return result;
  }

  private boolean isSupportedContentType(String contentType) {
    return contentType.equals("image/png")
        || contentType.equals("image/jpg")
        || contentType.equals("image/jpeg");
  }
}
