package com.fathom.services.user.profile.service;

import com.fathom.services.user.profile.domain.dto.CityCountryDto;
import com.fathom.services.user.profile.domain.dto.DateConfigurationDTO;
import com.fathom.services.user.profile.domain.dto.TimeZoneDTO;
import java.util.List;

public interface DateService {
  List<TimeZoneDTO> createTimeZoneList();

  DateConfigurationDTO getTimeConfiguration(String email);

  List<CityCountryDto> getCities();
}
