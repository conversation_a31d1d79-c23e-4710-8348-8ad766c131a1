package com.fathom.services.user.profile.domain.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Entity
@Table(name = "home_address")
@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@ToString(callSuper = true)
public class HomeAddress extends Address {

  @Column @EqualsAndHashCode.Include private String apartment;

  @JsonBackReference
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumns({@JoinColumn(name = "profile_profile_id"), @JoinColumn(name = "profile_email")})
  private ProfileInfo profile;

  @Column(columnDefinition = "boolean default false")
  @NotNull
  private Boolean primaryHome;
}
