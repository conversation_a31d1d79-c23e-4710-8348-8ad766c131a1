package com.fathom.services.user.profile.service.impl;

import static java.lang.Integer.min;
import static java.util.Collections.emptyList;
import static java.util.Objects.isNull;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;
import static org.springframework.util.CollectionUtils.isEmpty;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fathom.services.user.profile.domain.dto.*;
import com.fathom.services.user.profile.domain.entity.CompositeId;
import com.fathom.services.user.profile.domain.entity.Department;
import com.fathom.services.user.profile.domain.entity.HomeAddress;
import com.fathom.services.user.profile.domain.entity.ProfileInfo;
import com.fathom.services.user.profile.domain.repository.DepartmentRepository;
import com.fathom.services.user.profile.domain.repository.ProfileInfoRepository;
import com.fathom.services.user.profile.exception.InvalidDataException;
import com.fathom.services.user.profile.exception.NotFoundException;
import com.fathom.services.user.profile.service.ProfileInfoService;
import com.fathom.services.user.profile.utils.DepartmentRole;
import com.fathom.services.user.profile.utils.mapper.CycleAvoidingMappingContext;
import com.fathom.services.user.profile.utils.mapper.DepartmentMapper;
import com.fathom.services.user.profile.utils.mapper.SubordinatesMapper;
import com.fathom.services.user.profile.utils.mapper.UserProfileMapper;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsOperations;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class ProfileInfoServiceImpl implements ProfileInfoService {
  private static final int SINGLE_HOUSE = 1;
  private static final int SINGLE_ENTRY = 0;
  private static final String PROFILE_NOT_FOUND =
      "Profile was not found in the database. ProfileId: {}, email:{}";

  @Autowired private ProfileInfoRepository profileRepository;
  @Autowired private DepartmentRepository departmentRepository;

  @Autowired private ObjectMapper objectMapper;

  @Value("${image.path}")
  private String imagePath;

  @Value("${image.extention}")
  private String imageExtention;

  @Autowired private GridFsTemplate gridFsTemplate;

  @Autowired private GridFsOperations operations;
  @Autowired private ResourceLoader resourceLoader;

  @Override
  public PageDTO findAllUserProfiles(String email, Integer pageNumber, Integer pageSize) {
    Page<ProfileInfo> page = getPageWithProfileInfo(email, pageNumber, pageSize);
    if (Objects.isNull(page)) {
      return null;
    }
    List<ProfileInfoDTO> pageContent =
        page.get()
            .map(
                profileInfo ->
                    UserProfileMapper.INSTANCE.entityToNewDTO(
                        profileInfo, new CycleAvoidingMappingContext()))
            .collect(Collectors.toList());
    return new PageDTO<>(
        pageContent,
        page.getNumber() + 1,
        page.getSize(),
        page.getTotalPages(),
        page.getTotalElements());
  }

  private Page<ProfileInfo> getPageWithProfileInfo(String email, int pageNumber, int pageSize) {
    if (Objects.isNull(profileRepository.findByEmail(email))) {
      log.info("Profile was not found in the database. Email:{}", email);
      return null;
    }
    Pageable pageable = PageRequest.of(pageNumber - 1, pageSize);
    return profileRepository.findAll(pageable);
  }

  @Override
  public ProfileInfoDTO findUserProfile(String email) {
    ProfileInfo profileInfo = profileRepository.findByEmail(email);
    if (isNull(profileInfo)) {
      return createDefaultProfile(email);
    }
    return UserProfileMapper.INSTANCE.entityToNewDTO(
        profileInfo, new CycleAvoidingMappingContext());
  }

  private ProfileInfoDTO createDefaultProfile(String email) {
    log.info("Creating default user for not registered email {}", email);
    return createUserFrom(new ProfileInfoDTO(), email);
  }

  @Override
  @Transactional
  public void deleteUserProfile(UUID profileId, String email) {
    ProfileInfo updatableProfile = getUpdatableProfile(profileId, email);
    if (Objects.isNull(updatableProfile)) {
      log.info(PROFILE_NOT_FOUND, profileId, email);
      return;
    }
    if (!isNull(updatableProfile.getPictureId())) {
      gridFsTemplate.delete(new Query(Criteria.where("_id").is(updatableProfile.getPictureId())));
    }
    profileRepository.deleteById(updatableProfile.getCompositeId());
  }

  @Override
  @Transactional
  public ProfileInfoDTO createUserFrom(ProfileInfoDTO profileInfoDTO, String email) {
    ProfileInfo profileInfo =
        UserProfileMapper.INSTANCE.dtoToNewEntity(
            profileInfoDTO, new CycleAvoidingMappingContext());
    CompositeId compositeId = new CompositeId(email);
    profileInfo.setCompositeId(compositeId);
    ProfileInfo savedProfileInfo = profileRepository.save(validatePrimaryHome(profileInfo));
    return UserProfileMapper.INSTANCE.entityToNewDTO(
        savedProfileInfo, new CycleAvoidingMappingContext());
  }

  private ProfileInfo validatePrimaryHome(ProfileInfo profileInfo) {
    List<HomeAddress> homeAddresses = profileInfo.getHomeAddress();
    if (isEmpty(homeAddresses)) {
      return profileInfo;
    }
    if (homeAddresses.size() == SINGLE_HOUSE) {
      homeAddresses.get(SINGLE_ENTRY).setPrimaryHome(Boolean.TRUE);
      return profileInfo;
    }
    long primaryHomesCount = homeAddresses.stream().filter(HomeAddress::getPrimaryHome).count();
    if (primaryHomesCount != SINGLE_HOUSE) {
      throw InvalidDataException.attempToStoreMoreThanOnePrimaryHome();
    }
    return profileInfo;
  }

  @Override
  @Transactional(rollbackFor = InvalidDataException.class)
  public ProfileInfoDTO updateUserFrom(
      ProfileInfoDTO profileInfoDTO, String email, UUID profileId) {
    ProfileInfo oldProfileInfo = getUpdatableProfile(profileId, email);
    if (Objects.isNull(oldProfileInfo)) {
      log.info(PROFILE_NOT_FOUND, profileId, email);
      return null;
    }
    oldProfileInfo.getHomeAddress().forEach(home -> home.setProfile(null));
    CompositeId compositeId = new CompositeId(profileId, email);
    ProfileInfo updatedProfileInfo =
        UserProfileMapper.INSTANCE.dtoToNewEntity(
            profileInfoDTO, new CycleAvoidingMappingContext());
    updatedProfileInfo.setCompositeId(compositeId);
    updatedProfileInfo.setPictureId(oldProfileInfo.getPictureId());
    if (!Objects.isNull(oldProfileInfo.getManager())) {
      updatedProfileInfo.setManager(oldProfileInfo.getManager());
    }
    if (!Objects.isNull(updatedProfileInfo.getHomeAddress())) {
      updatedProfileInfo.getHomeAddress().forEach(home -> home.setProfile(updatedProfileInfo));
    }
    ProfileInfo updatedProfile = profileRepository.save(validatePrimaryHome(updatedProfileInfo));
    return UserProfileMapper.INSTANCE.entityToNewDTO(
        updatedProfile, new CycleAvoidingMappingContext());
  }

  @Override
  @Transactional(rollbackFor = NotFoundException.class)
  public void updateSubordinates(
      UUID profileId, String email, UUID organizationId, Set<UUID> subordinateIds) {
    CompositeId compositeId = new CompositeId(profileId, email);
    Optional<ProfileInfo> managerProfile = profileRepository.findById(compositeId);
    if (managerProfile.isEmpty()) {
      log.info(PROFILE_NOT_FOUND, profileId, email);
      return;
    }
    List<ProfileInfo> subordinatesProfiles = new ArrayList<>();
    ProfileInfo profileInfo = managerProfile.get();
    List<ProfileInfo> existProfiles = profileRepository.findAllByProfileId(subordinateIds);
    if (isEmpty(existProfiles)) {
      log.error("No profiles were found with provided Ids: {}", subordinateIds);
      return;
    }
    existProfiles.forEach(
        subordinate -> {
          subordinate.setManager(profileInfo);
          subordinatesProfiles.add(subordinate);
        });
    profileInfo.setSubordinates(subordinatesProfiles);
    profileRepository.save(profileInfo);
    profileRepository.saveAll(subordinatesProfiles);
  }

  @Override
  @Transactional(rollbackFor = NotFoundException.class)
  public void deleteSubordinates(
      UUID profileId, String email, UUID organizationId, Set<UUID> subordinatesIds) {
    CompositeId compositeId = new CompositeId(profileId, email);
    Optional<ProfileInfo> managerProfile = profileRepository.findById(compositeId);
    if (managerProfile.isEmpty()) {
      log.info(PROFILE_NOT_FOUND, profileId, email);
      return;
    }
    List<ProfileInfo> subordinatesProfiles = new ArrayList<>();
    ProfileInfo profileInfo = managerProfile.get();
    List<ProfileInfo> currentSubordinates = profileInfo.getSubordinates();
    if (isEmpty(currentSubordinates)) {
      log.info("Attemp to delete empty subordinates ids set");
      return;
    }
    List<ProfileInfo> existProfiles = profileRepository.findAllByProfileId(subordinatesIds);
    if (isEmpty(existProfiles)) {
      log.error("No profiles were found with provided ids: {}", subordinatesIds);
      return;
    }

    existProfiles.forEach(
        subordinate -> {
          if (currentSubordinates.contains(subordinate)) {
            subordinate.setManager(null);
          }
        });
    currentSubordinates.removeAll(existProfiles);

    profileInfo.setSubordinates(currentSubordinates);
    profileRepository.save(profileInfo);
    profileRepository.saveAll(subordinatesProfiles);
  }

  @Override
  public PageDTO findSubordinates(
      UUID profileId, String email, UUID organizationId, Integer pageNumber, Integer pageSize) {
    CompositeId compositeId = new CompositeId(profileId, email);
    Optional<ProfileInfo> managerProfile = profileRepository.findById(compositeId);
    if (managerProfile.isEmpty()) {
      log.info(PROFILE_NOT_FOUND, profileId, email);
      return new PageDTO<>(null, pageNumber, pageSize, 0, 0L);
    }
    List<ProfileInfo> subordinatesProfiles = managerProfile.get().getSubordinates();
    if (isEmpty(subordinatesProfiles)) {
      log.info("Profile subordinates list was empty. ProfileId: {}, email:{}", profileId, email);
      return new PageDTO<>(null, pageNumber, pageSize, 0, 0L);
    }
    Map<Integer, List<ProfileInfo>> pageContent = divideByPage(subordinatesProfiles, pageSize);
    if (!pageContent.containsKey(pageNumber - 1)) {
      return new PageDTO<>(
          emptyList(),
          pageNumber,
          pageSize,
          pageContent.size(),
          (long) subordinatesProfiles.size());
    }
    List<SubordinateProfileDTO> pageContentDTO =
        pageContent.get(pageNumber - 1).stream()
            .map(
                profile ->
                    SubordinatesMapper.INSTANCE.entityToNewDTO(
                        profile, new CycleAvoidingMappingContext()))
            .collect(Collectors.toList());
    return new PageDTO<>(
        pageContentDTO,
        pageNumber,
        pageSize,
        pageContent.size(),
        (long) subordinatesProfiles.size());
  }

  @Override
  public PageDTO<DepartmentDTO> findDepartments(
      UUID profileId, String email, UUID orgId, Integer pageNumber, Integer pageSize) {
    CompositeId compositeId = new CompositeId(profileId, email);
    Optional<ProfileInfo> storedProfile = profileRepository.findById(compositeId);
    if (storedProfile.isEmpty()) {
      log.info(PROFILE_NOT_FOUND, profileId, email);
      return null;
    }
    if (orgId == null) {
      orgId = storedProfile.get().getDefaultOrganizationId();
    }
    final UUID valiOrgId = orgId;
    List<DepartmentDTO> departmentDTOList = new ArrayList<>();
    Set<DepartmentDTO> employeeDepartments =
        storedProfile.get().getDepartments().stream()
            .filter(department -> department.getOrganizationId().equals(valiOrgId))
            .map(
                department ->
                    DepartmentMapper.INSTANCE.entityToNewDTO(
                        department, new CycleAvoidingMappingContext()))
            .collect(Collectors.toSet());
    departmentDTOList.addAll(
        employeeDepartments.stream()
            .map(
                departmentDTO -> {
                  departmentDTO.setTypeOfParticipant(DepartmentRole.employee);
                  return departmentDTO;
                })
            .collect(Collectors.toSet()));
    Set<DepartmentDTO> managerDepartments =
        storedProfile.get().getManagedDepartments().stream()
            .filter(department -> department.getOrganizationId().equals(valiOrgId))
            .map(
                department ->
                    DepartmentMapper.INSTANCE.entityToNewDTO(
                        department, new CycleAvoidingMappingContext()))
            .collect(Collectors.toSet());
    departmentDTOList.addAll(
        managerDepartments.stream()
            .map(
                departmentDTO -> {
                  departmentDTO.setTypeOfParticipant(DepartmentRole.manager);
                  return departmentDTO;
                })
            .collect(Collectors.toSet()));
    Map<Integer, List<DepartmentDTO>> pageContent = divideByPage(pageSize, departmentDTOList);
    if (!pageContent.containsKey(pageNumber - 1)) {
      return new PageDTO<>(
          departmentDTOList,
          pageNumber,
          pageSize,
          pageContent.size(),
          (long) departmentDTOList.size());
    }
    return new PageDTO<>(
        pageContent.get(pageNumber - 1),
        pageNumber,
        pageSize,
        pageContent.size(),
        (long) departmentDTOList.size());
  }

  @Override
  @Transactional(rollbackFor = NotFoundException.class)
  public Map<DepartmentRole, Set<DepartmentDTO>> saveDepartments(
      UUID profileId, String email, UUID orgId, List<UpdateDepartmentDTO> departments) {
    CompositeId compositeId = new CompositeId(profileId, email);
    Optional<ProfileInfo> storedProfile = profileRepository.findById(compositeId);
    if (storedProfile.isEmpty()) {
      log.info(PROFILE_NOT_FOUND, profileId, email);
      return null;
    }
    ProfileInfo profileInfo = storedProfile.get();
    Map<DepartmentRole, Set<Integer>> departmentsMap =
        departments.stream()
            .collect(
                toMap(
                    UpdateDepartmentDTO::getTypeOfParticipant,
                    UpdateDepartmentDTO::getDepartmentsIds));

    if (isEmpty(departmentsMap.get(DepartmentRole.employee))
        && isEmpty(departmentsMap.get(DepartmentRole.manager))) {
      throw InvalidDataException.attemptToSaveEmptyDepartmentsSet();
    }

    Set<Department> departmentsWithMember = new HashSet<>();
    if (!isEmpty(departmentsMap.get(DepartmentRole.employee))) {
      Set<Department> employeeDepartments =
          departmentRepository.findAllByIds(departmentsMap.get(DepartmentRole.employee)).stream()
              .filter(department -> department.getOrganizationId().equals(orgId))
              .collect(Collectors.toSet());
      if (!isEmpty(employeeDepartments)) {
        profileInfo.setDepartments(employeeDepartments);
        departmentsWithMember =
            employeeDepartments.stream()
                .map(
                    employeeDepartment -> {
                      employeeDepartment.getProfiles().add(profileInfo);
                      return employeeDepartment;
                    })
                .collect(toSet());
      }
    }

    Set<Department> departmentsWithManager = new HashSet<>();
    if (!isEmpty(departmentsMap.get(DepartmentRole.manager))) {
      Set<Department> managerDepartments =
          departmentRepository.findAllByIds(departmentsMap.get(DepartmentRole.manager)).stream()
              .filter(department -> department.getOrganizationId().equals(orgId))
              .collect(Collectors.toSet());
      if (!isEmpty(managerDepartments)) {
        profileInfo.setDepartments(managerDepartments);
        departmentsWithManager =
            managerDepartments.stream()
                .map(
                    managedDepartment -> {
                      managedDepartment.setDepartmentManager(profileInfo);
                      return managedDepartment;
                    })
                .collect(toSet());
      }
    }

    profileRepository.save(profileInfo);
    Map<DepartmentRole, Set<DepartmentDTO>> newDepartmentsMap = new EnumMap<>(DepartmentRole.class);

    if (!isEmpty(departmentsWithManager)) {
      Set<Department> savedDepartments = departmentRepository.saveAll(departmentsWithManager);
      newDepartmentsMap.put(
          DepartmentRole.manager,
          savedDepartments.stream()
              .map(
                  department ->
                      DepartmentMapper.INSTANCE.entityToNewDTO(
                          department, new CycleAvoidingMappingContext()))
              .collect(toSet()));
    }

    if (!isEmpty(departmentsWithMember)) {
      Set<Department> savedDepartments = departmentRepository.saveAll(departmentsWithMember);
      newDepartmentsMap.put(
          DepartmentRole.employee,
          savedDepartments.stream()
              .map(
                  department ->
                      DepartmentMapper.INSTANCE.entityToNewDTO(
                          department, new CycleAvoidingMappingContext()))
              .collect(toSet()));
    }
    return newDepartmentsMap;
  }

  @Override
  @Transactional(rollbackFor = NotFoundException.class)
  public void deleteDepartments(
      UUID profileId, String email, UUID orgId, List<UpdateDepartmentDTO> departments) {
    CompositeId compositeId = new CompositeId(profileId, email);
    Optional<ProfileInfo> storedProfile = profileRepository.findById(compositeId);
    if (storedProfile.isEmpty()) {
      log.info(PROFILE_NOT_FOUND, profileId, email);
      return;
    }
    Map<DepartmentRole, Set<Integer>> departmentsMap =
        departments.stream()
            .collect(
                toMap(
                    UpdateDepartmentDTO::getTypeOfParticipant,
                    UpdateDepartmentDTO::getDepartmentsIds));

    if (isEmpty(departmentsMap.get(DepartmentRole.employee))
        && isEmpty(departmentsMap.get(DepartmentRole.manager))) {
      throw InvalidDataException.attemptToDeleteEmptyDepartmentsSet();
    }
    Set<Integer> managerDepartmentsIds = departmentsMap.get(DepartmentRole.manager);
    Set<Integer> employeeDepartmentsIds = departmentsMap.get(DepartmentRole.employee);

    ProfileInfo profileInfo = storedProfile.get();
    Set<Department> managedDepartments =
        profileInfo.getManagedDepartments().stream()
            .filter(department -> department.getOrganizationId().equals(orgId))
            .collect(Collectors.toSet());
    Set<Department> departmentsWithManager = null;
    if (!isEmpty(managedDepartments) && !isEmpty(managerDepartmentsIds)) {
      Set<Department> toRemove =
          managedDepartments.stream()
              .filter(storedDepartment -> managerDepartmentsIds.contains(storedDepartment.getId()))
              .collect(toSet());
      if (!isEmpty(toRemove)) {
        managedDepartments.removeAll(toRemove);
        profileInfo.setManagedDepartments(managedDepartments);
        departmentsWithManager =
            toRemove.stream()
                .map(
                    managedDepartment -> {
                      managedDepartment.setDepartmentManager(null);
                      return managedDepartment;
                    })
                .collect(toSet());
      }
    }

    Set<Department> storedEmployeeDepartments =
        profileInfo.getDepartments().stream()
            .filter(department -> department.getOrganizationId().equals(orgId))
            .collect(Collectors.toSet());
    Set<Department> departmentsWithMember = null;
    if (!isEmpty(storedEmployeeDepartments) && !isEmpty(employeeDepartmentsIds)) {
      Set<Department> toRemove =
          storedEmployeeDepartments.stream()
              .filter(storedDepartment -> employeeDepartmentsIds.contains(storedDepartment.getId()))
              .collect(toSet());
      if (!isEmpty(toRemove)) {
        storedEmployeeDepartments.removeAll(toRemove);
        profileInfo.setDepartments(storedEmployeeDepartments);
        departmentsWithMember =
            toRemove.stream()
                .map(
                    employeeDepartment -> {
                      employeeDepartment.getProfiles().remove(profileInfo);
                      return employeeDepartment;
                    })
                .collect(toSet());
      }
    }
    profileRepository.save(profileInfo);

    if (!isEmpty(departmentsWithManager)) {
      departmentRepository.saveAll(departmentsWithManager);
    }
    if (!isEmpty(departmentsWithMember)) {
      departmentRepository.saveAll(departmentsWithMember);
    }
  }

  @Override
  public List<ProfileInfoDTO> findManyUsers(Set<String> emails) {

    return profileRepository.findAllByEmail(emails).stream()
        .map(x -> UserProfileMapper.INSTANCE.entityToNewDTO(x, new CycleAvoidingMappingContext()))
        .collect(Collectors.toList());
  }

  private Map<Integer, List<ProfileInfo>> divideByPage(List<ProfileInfo> list, Integer pageSize) {
    return IntStream.iterate(0, i -> i + pageSize)
        .limit((list.size() + pageSize - 1) / pageSize)
        .boxed()
        .collect(toMap(i -> i / pageSize, i -> list.subList(i, min(i + pageSize, list.size()))));
  }

  private Map<Integer, List<DepartmentDTO>> divideByPage(
      Integer pageSize, List<DepartmentDTO> list) {
    return IntStream.iterate(0, i -> i + pageSize)
        .limit((list.size() + pageSize - 1) / pageSize)
        .boxed()
        .collect(toMap(i -> i / pageSize, i -> list.subList(i, min(i + pageSize, list.size()))));
  }

  @Override
  public List<ProfileContactDTO> finAllSubordinatesContacts(String email, UUID orgId) {
    ProfileInfo callerProfile = profileRepository.findByEmail(email);
    if (Objects.isNull(callerProfile)) {
      log.info("Profile was not found in the database.  Email:{}", email);
      return Collections.emptyList();
    }
    Set<UUID> alreadyAddedSubordinates =
        callerProfile.getSubordinates().stream()
            //                .filter(subordinate ->
            // subordinate.getDefaultOrganizationId().equals(orgId) ||
            // Sets.union(subordinate.getDepartments(), subordinate.getManagedDepartments())
            //                        .stream()
            //                        .anyMatch(department ->
            // department.getOrganizationId().equals(orgId)))
            .map(subordinate -> subordinate.getCompositeId().getProfileId())
            .collect(toSet());
    UUID organizationId = callerProfile.getDefaultOrganizationId();
    List<UUID> alreadyExist = new ArrayList<>(alreadyAddedSubordinates);
    alreadyExist.add(callerProfile.getCompositeId().getProfileId());
    List<ProfileContactDTO> contactDTOList =
        profileRepository.findAllContacts(organizationId, alreadyExist);
    if (CollectionUtils.isEmpty(contactDTOList)) {
      log.info("Not found emails in the database.");
      return Collections.emptyList();
    }
    return contactDTOList;
  }

  private ProfileInfo getUpdatableProfile(UUID profileId, String email) {
    CompositeId compositeId = new CompositeId(profileId, email);
    Optional<ProfileInfo> userProfile = profileRepository.findById(compositeId);
    if (userProfile.isEmpty()) {
      log.info(PROFILE_NOT_FOUND, profileId, email);
      return null;
    }
    return userProfile.get();
  }
}
