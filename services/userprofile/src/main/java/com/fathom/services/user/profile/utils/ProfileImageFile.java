package com.fathom.services.user.profile.utils;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.eclipse.aether.resolution.MetadataRequest;

@Data
@NoArgsConstructor
public class ProfileImageFile {
  @Schema(type = "string", format = "binary", description = "image file")
  private String file;

  @Schema(description = "image metadata", hidden = true)
  private MetadataRequest metadata;
}
