package com.fathom.services.user.profile.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.fathom.services.user.profile.utils.Views;
import java.io.Serializable;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
public class HomeAddressDTO implements Serializable {
  @JsonView(Views.Update.class)
  private Integer id;

  private String apartment;
  private String country;
  private String city;
  private String street;
  private Integer buildingNumber;
  private String zipcode;

  @JsonProperty("primary")
  private Boolean primaryHome;
}
