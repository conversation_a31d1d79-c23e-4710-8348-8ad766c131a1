package com.fathom.services.user.profile.exception;

import com.fathom.services.user.profile.config.constants.ErrorMessage;

public class InvalidDataException extends ContractException {

  protected InvalidDataException(String message, Object... params) {
    super(message, params);
  }

  protected InvalidDataException(String message) {
    super(message);
  }

  public static InvalidDataException conditionsIsNotCorrect() {
    return new InvalidDataException(ErrorMessage.CONDITIONS_NOT_CORRECT);
  }

  public static InvalidDataException attemptToSetNotValidValue() {
    return new InvalidDataException(ErrorMessage.ATTEMPT_TO_SET_INVALID_VALUE);
  }

  public static InvalidDataException attemptToCreateDepartmentInInvalidCompany() {
    return new InvalidDataException(ErrorMessage.ATTEMPT_TO_CREATE_DEPARTMENT_IN_INVALID_COMPANY);
  }

  public static InvalidDataException attemptToSerchDepartmentInInvalidCompany() {
    return new InvalidDataException(ErrorMessage.ATTEMPT_TO_SEARCH_DEPARTMENT_IN_INVALID_COMPANY);
  }

  public static InvalidDataException attemptToUpdateDepartmentInInvalidCompany() {
    return new InvalidDataException(ErrorMessage.ATTEMPT_TO_UPDATE_DEPARTMENT_IN_INVALID_COMPANY);
  }

  public static InvalidDataException attemptToDeleteDepartmentInInvalidCompany() {
    return new InvalidDataException(ErrorMessage.ATTEMPT_TO_DELETE_DEPARTMENT_IN_INVALID_COMPANY);
  }

  public static InvalidDataException attemptToSaveNullEntity() {
    return new InvalidDataException(ErrorMessage.ATTEMPT_TO_SAVE_EMPTY_ENTITY);
  }

  public static InvalidDataException attemptToUpdateDepartmentWithTheSameData() {
    return new InvalidDataException(ErrorMessage.ATTEMPT_TO_UPDATE_DEPARTMENT_WITHOUT_CHANGES);
  }

  public static InvalidDataException attemptToDeletePictureNotBelongToUser() {
    return new InvalidDataException(ErrorMessage.ATTEMPT_TO_DELETE_PICTURE_WITH_INCORRECT_ID);
  }

  public static InvalidDataException attemptToDeleteFromEmptySubordinatesList() {
    return new InvalidDataException(ErrorMessage.ATTEMPT_TO_DELETE_FROM_EMPTY_SUBORDINATES_LIST);
  }

  public static InvalidDataException attempToStoreMoreThanOnePrimaryHome() {
    return new InvalidDataException(ErrorMessage.ATTEMPT_TO_HAVE_FEW_PRIMARY_HOMES);
  }

  public static InvalidDataException attemptToDeleteEmptyDepartmentsSet() {
    return new InvalidDataException(ErrorMessage.ATTEMPT_TO_DELETE_EMPTY_DEPARTMENTS_SET);
  }

  public static InvalidDataException attemptToSaveEmptyDepartmentsSet() {
    return new InvalidDataException(ErrorMessage.ATTEMPT_TO_SAVE_EMPTY_DEPARTMENTS_SET);
  }
}
