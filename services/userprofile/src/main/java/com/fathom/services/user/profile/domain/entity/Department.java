package com.fathom.services.user.profile.domain.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.sql.Types;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.JdbcTypeCode;

@Entity
@Table(
    name = "department",
    uniqueConstraints = @UniqueConstraint(columnNames = {"name", "organization_id"}))
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Department extends NamedEntity {

  private String abbr;
  private String description;

  @JsonBackReference
  @ManyToMany(
      fetch = FetchType.LAZY,
      cascade = {CascadeType.PERSIST, CascadeType.MERGE})
  @JoinTable(
      name = "department_user_profiles",
      joinColumns = {@JoinColumn(name = "department_id")},
      inverseJoinColumns = {
        @JoinColumn(name = "profile_profile_id"),
        @JoinColumn(name = "profile_email")
      })
  private Set<ProfileInfo> profiles = new HashSet<>();

  @ManyToOne @JsonBackReference private ProfileInfo departmentManager;

  @Column(name = "organization_id")
  @JdbcTypeCode(Types.VARCHAR)
  private UUID organizationId;
}
