package com.fathom.services.user.profile.service;

import com.fathom.services.user.profile.domain.dto.DepartmentDTO;
import com.fathom.services.user.profile.domain.dto.PageDTO;
import java.util.Set;
import java.util.UUID;

public interface DepartmentService {

  DepartmentDTO save(String email, DepartmentDTO departmentDTO);

  Set<DepartmentDTO> saveAll(String email, UUID orgId, Set<DepartmentDTO> departmentDTOSet);

  void deleteById(String email, Integer departmentId);

  PageDTO findAll(String email, UUID userOrgId, int pageNumber, int pageSize);

  DepartmentDTO findById(String email, Integer departmentId);

  DepartmentDTO update(String email, DepartmentDTO departmentDTO, Integer departmentId);
}
