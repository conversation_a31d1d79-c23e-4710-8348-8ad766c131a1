package com.fathom.services.user.profile.utils.converter;

import com.fathom.services.user.profile.domain.entity.Gender;
import com.google.common.base.Strings;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import java.util.stream.Stream;

@Converter(autoApply = true)
public class GenderConverter implements AttributeConverter<Gender, String> {

  @Override
  public String convertToDatabaseColumn(Gender gender) {
    if (gender == null) {
      return Gender.OTHER.getCode();
    }
    return gender.getCode();
  }

  @Override
  public Gender convertToEntityAttribute(String code) {
    if (Strings.isNullOrEmpty(code)) {
      return Gender.OTHER;
    }

    return Stream.of(Gender.values())
        .filter(c -> c.getCode().equals(code))
        .findFirst()
        .orElseThrow(IllegalArgumentException::new);
  }
}
