image:
  path: ${DEFAULT_PROFILE_IMAGE_PATH:classpath:/static/}
  extention: ${PROFILE_IMAGE_EXTENTION:.png}
server:
  port: ${PROFILES_PORT:8089}

controllers:
  image:
    enable: false

spring:
  application:
    name: ${PROFILES_NAME:profiles}
  datasource:
    url: jdbc:postgresql://${POSTGRES_HOST:localhost}:${POSTGRES_PORT:5432}/${POSTGRES_DATABASE:fathom}?currentSchema=${PROFILES_SCHEMA:profiles}
    username: ${POSTGRES_USER:postgres}
    password: ${POSTGRES_PASSWORD:postgres}
    hikari:
      connectionTimeout: 20000
      maximumPoolSize: 5
      leak-detection-threshold: 15000
  jpa:
    show-sql: true
    database: postgresql
    hibernate:
      ddl-auto: update
  liquibase:
    enabled: false
    change-log: classpath:db/changelog/db.changelog-master.xml
    default-schema: ${PROFILES_SCHEMA:profiles}
  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017}
      database: ${MONGODB_DATABASE:dev}
  cloud:
    loadbalancer:
      ribbon:
        enabled: false
    kubernetes:
      discovery:
        all-namespaces: false
  management:
    endpoints:
      web:
        exposure:
          include: "*"
    endpoint:
      health:
        show-details: ALWAYS
  servlet.multipart.enabled: true
  servlet.multipart.max-file-size: 512KB
  servlet.multipart.max-request-size: 512KB
  sql.init.mode: always
  sql.init.schema-locations: classpath:schema-postgres.sql
logging:
  level:
    org.springframework.web.filter.CommonsRequestLoggingFilter: INFO
    com:
      zaxxer:
        hikari: info
  pattern:
    # Logging pattern containing traceId and spanId; no longer provided through Sleuth by default
    level: "%5p [${spring.application.name:},%X{traceId:-},%X{spanId:-}]"

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics

springdoc:
  api-docs:
    version: "openapi_3_0"

diagnostics:
  service-name: "userprofile"
  version: ${VERSION:0.0.1-dev}
  build-timestamp: ${BUILD_TIMESTAMP:1970-01-01T00:00:00Z}
  git-hash: ${GIT_HASH:unknown}
  git-branch: ${GIT_BRANCH:main}
  git-commit-timestamp: ${GIT_COMMIT_TIMESTAMP:1970-01-01T00:00:00Z}