<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-3.8.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="liquibase (generated)" id="1594277242492-1">
        <createTable tableName="office_address">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="office_address_pkey"/>
            </column>
            <column name="building_number" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="city" type="VARCHAR(60)">
                <constraints nullable="false"/>
            </column>
            <column name="country" type="VARCHAR(60)">
                <constraints nullable="false"/>
            </column>
            <column name="street" type="VARCHAR(60)">
                <constraints nullable="false"/>
            </column>
            <column name="zipcode" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="floor" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="office_unit" type="VARCHAR(255)"/>
            <column name="work_place" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="liquibase (generated)" id="1594277242492-2">
        <createTable tableName="home_address">
            <column autoIncrement="true" name="id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="home_address_pkey"/>
            </column>
            <column name="building_number" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="city" type="VARCHAR(60)">
                <constraints nullable="false"/>
            </column>
            <column name="country" type="VARCHAR(60)">
                <constraints nullable="false"/>
            </column>
            <column name="street" type="VARCHAR(60)">
                <constraints nullable="false"/>
            </column>
            <column name="zipcode" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="apartment" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="liquibase (generated)" id="1594277242492-3">
        <createTable tableName="user_profile">
            <column name="email" type="VARCHAR(255)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="user_profile_pkey"/>
            </column>
            <column name="profile_id" type="VARCHAR(255)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="user_profile_pkey"/>
            </column>
            <column name="birthday" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="date_format" type="VARCHAR(255)"/>
            <column name="first_name" type="VARCHAR(100)"/>
            <column name="gender" type="VARCHAR(255)"/>
            <column name="last_name" type="VARCHAR(100)"/>
            <column name="phone" type="VARCHAR(255)"/>
            <column name="picture" type="VARCHAR(255)"/>
            <column name="time_zone" type="VARCHAR(255)"/>
            <column name="home_address_id" type="INTEGER"/>
            <column name="office_address_id" type="INTEGER"/>
        </createTable>
    </changeSet>
    <changeSet author="liquibase (generated)" id="1594277242492-4">
        <addForeignKeyConstraint baseColumnNames="home_address_id" baseTableName="user_profile" constraintName="fkj32443d2gsdrukkgewmqiixqq" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="home_address" validate="true"/>
    </changeSet>
    <changeSet author="liquibase (generated)" id="1594277242492-5">
        <addForeignKeyConstraint baseColumnNames="office_address_id" baseTableName="user_profile" constraintName="fkqaitqewnbeq7bbb206eg981n5" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id" referencedTableName="office_address" validate="true"/>
    </changeSet>
    <changeSet author="liquibase (generated)" id="1594277242492-6">
        <addUniqueConstraint columnNames="profile_id" constraintName="uk_dnnx1gqmln4no0py3hn5fy334" tableName="user_profile"/>
    </changeSet>
    <changeSet author="liquibase (generated)" id="1594277242492-7">
        <addUniqueConstraint columnNames="email" constraintName="uk_tcks72p02h4dp13cbhxne17ad" tableName="user_profile"/>
    </changeSet>
</databaseChangeLog>
