# Monorepo Overview

This repository contains multiple Java-based microservices and libraries that make up the Fathom platform. The codebase is structured as a monorepo to facilitate shared development, consistent versioning, and coordinated releases.

## Components

- **Services**: Independent microservices built with Spring Boot (e.g., `usermanagement` service)
- **Libraries**: Shared code modules that can be used across services (e.g., `advancedfilters` library)

The architecture supports containerized deployments managed via ArgoCD

# Release Process

[The rest of the existing content remains unchanged]# Release Process

To release a stable version of services or libraries from this monorepo:

1. Create a GitHub release with a semantic version tag (e.g., `v1.2.3`)
2. In the release body, specify which components to release using the following format:
    - For services: include `service:servicename` (e.g., `service:usermanagement`)
    - For libraries: include `lib:libname` (e.g., `lib:advancedfilters`)
3. Multiple components can be released simultaneously by including multiple entries
4. The CI workflow will process the release and only build/publish stable versions for the specified components
5. Components not mentioned in the release body will be skipped

Example release body:
```
Release v1.2.3

Includes:
- service:usermanagement
- lib:advancedfilters

Release notes:
- Fixed password reset functionality
- Added new filters for advanced queries
```

The pushed images will be picked up by argocd and be deployed to QA

