# Advanced Filters Library - `com.fathom.lib.advancedfilters`

## Overview

The `com.fathom.lib.advancedfilters` library provides an intuitive and powerful way to apply advanced filtering and sorting capabilities to retrieve data in a structured and efficient manner. This library is designed to simplify complex filtering scenarios by supporting various conditions and logical operators, making it a versatile tool for building applications with dynamic querying requirements.

---

## Features

The library supports a rich set of filtering options:

### Filtering Operators
- **`@and`**: Combines multiple filters that must all be satisfied.
- **`@or`**: Combines multiple filters where at least one must be satisfied.
- **`@gt`**: Filters for values greater than a specified value.
- **`@lt`**: Filters for values less than a specified value.
- **`@eq`**: Filters for values equal to a specified value.
- **`@noteq`**: Filters for values not equal to a specified value.
- **`@gte`**: Filters for values greater than or equal to a specified value.
- **`@lte`**: Filters for values less than or equal to a specified value.
- **`@in`**: Filters for values present in a list of specified values.
- **`@not`**: Filters for values not present in a specified list.
- **`@contains`**: Filters for strings containing a specified substring.
- **`@between`**: Filters for values within a specified range.
- **`@regex`**: Filters for strings matching a specified regular expression.

---

## How to Use

### Defining Filters

Filters are defined in a JSON-like structure and passed to your application to process data dynamically. The structure can include logical combinations and individual conditions.

#### Example Filter

```json
{
  "filter": {
    "@and": [
      {
        "@gt": {
          "path": "price",
          "value": 10
        }
      },
      {
        "@lt": {
          "path": "price",
          "value": 99
        }
      }
    ]
  },
  "sort": {
    "by": "price",
    "ord": "ASC"
  }
}
```

### Pagination Support

The library seamlessly integrates with pagination utilities like Spring's `Pageable`. By providing parameters like `pageNumber` and `pageSize`, users can retrieve data efficiently.

---

## Example Usage

### Spring Boot Integration

The library can be used in a Spring Boot controller to fetch filtered and paginated data. Here's an example:

```java
@Operation(
    summary = "Retrieve filtered users",
    description = "Fetch users based on advanced filtering and sorting criteria."
)
public ResponseEntity<Page<ProfileInfoDTO>> getUsersFiltered(
    CustomFilter customFilter,
    int pageNumber,
    int pageSize
) {
    // Implementation logic here
}
```

---

## Benefits

- **Dynamic Filtering**: Build complex queries at runtime without hardcoding.
- **Logical Combinations**: Combine multiple filters with `@and` and `@or`.
- **Rich Operators**: Support for numeric, string, range, and regex-based filters.
- **Efficient Pagination**: Retrieve large datasets in manageable chunks.

---

## Example Application Scenarios

1. **User Management Systems**: Filter users by attributes like age, location, or roles.
2. **E-commerce Platforms**: Search products by price range, categories, and availability.
3. **Data Analytics**: Extract subsets of data based on custom logic for analysis.

---

## Installation

To install the library, include the following dependency in your `pom.xml` file:

```xml
<dependency>
    <groupId>com.fathom.lib</groupId>
    <artifactId>advancedfilters</artifactId>
    <version>1.0.0</version>
</dependency>
```

---

## Contribution

We welcome contributions to improve and expand the functionality of `com.fathom.lib.advancedfilters`. Feel free to open issues or submit pull requests on our GitHub repository.

---

## License

This library is licensed under the MIT License. For more details, see the LICENSE file.

---

## Support

For questions or support, reach out to our team at [<EMAIL>](mailto:<EMAIL>).

---

## Start Building Smarter Filters!

Start building smarter filters with `com.fathom.lib.advancedfilters` today!
