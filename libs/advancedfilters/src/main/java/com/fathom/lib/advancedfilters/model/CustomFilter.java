package com.fathom.lib.advancedfilters.model;

import java.util.Map;
import lombok.Data;

@Data
public class CustomFilter {
  private Map<String, Object> filter;
  private CustomSort sort; // Add this line to include sorting

  @Data
  public static class CustomSort {
    private String by;
    private SortOrder ord; // Order: "ASC" or "DESC"

    public CustomSort() {}

    public CustomSort(String by, SortOrder ord) {
      this.by = by;
      this.ord = ord;
    }

    public static CustomSort of(String price, SortOrder asc) {
      return new CustomSort(price, asc);
    }
  }
}
