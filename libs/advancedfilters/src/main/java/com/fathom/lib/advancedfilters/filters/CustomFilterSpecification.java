package com.fathom.lib.advancedfilters.filters;

import com.fathom.lib.advancedfilters.model.CustomFilter;
import com.fathom.lib.advancedfilters.model.SortOrder;
import jakarta.persistence.Lob;
import jakarta.persistence.criteria.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.lang.NonNull;

@Slf4j
public class CustomFilterSpecification<T> implements Specification<T> {

  private final transient CustomFilter customFilter;

  public CustomFilterSpecification(CustomFilter customFilter) {
    this.customFilter = customFilter;
  }

  @Override
  public Predicate toPredicate(
      @NonNull Root<T> root, CriteriaQuery<?> query, @NonNull CriteriaBuilder criteriaBuilder) {
    Predicate filterPredicate = buildPredicate(customFilter.getFilter(), root, criteriaBuilder);

    // Apply sorting if present
    CustomFilter.CustomSort sort = customFilter.getSort();
    if (sort != null && sort.getBy() != null) {
      if (SortOrder.ASCENDING.equals(sort.getOrd())) {
        query.orderBy(criteriaBuilder.asc(root.get(sort.getBy())));
      } else if (SortOrder.DESCENDING.equals(sort.getOrd())) {
        query.orderBy(criteriaBuilder.desc(root.get(sort.getBy())));
      }
    }

    return filterPredicate;
  }

  @SuppressWarnings("unchecked")
  private Predicate buildPredicate(Map<String, Object> filter, Root<T> root, CriteriaBuilder cb) {
    if (filter == null || filter.isEmpty()) {
      return cb.conjunction();
    }

    List<Predicate> predicates = new ArrayList<>();

    // Process each entry in the filter map
    for (Map.Entry<String, Object> entry : filter.entrySet()) {
      String key = entry.getKey();
      Object value = entry.getValue();

      Predicate predicate;
      switch (key) {
        case "@and" -> predicate = handleAnd((List<Map<String, Object>>) value, root, cb);
        case "@or" -> predicate = handleOr((List<Map<String, Object>>) value, root, cb);
        default ->
            predicate = handleComparisonOperation(key, (Map<String, Object>) value, root, cb);
      }

      if (predicate != null) {
        predicates.add(predicate);
      }
    }

    // If predicates exist, combine them with AND (or OR based on your logic)
    if (predicates.isEmpty()) {
      return cb.conjunction();
    } else {
      return cb.and(predicates.toArray(new Predicate[0])); // Combine predicates with AND
    }
  }

  private Predicate handleAnd(
      List<Map<String, Object>> conditions, Root<T> root, CriteriaBuilder cb) {
    return cb.and(
        conditions.stream()
            .map(condition -> buildPredicate(condition, root, cb))
            .toArray(Predicate[]::new));
  }

  private Predicate handleOr(
      List<Map<String, Object>> conditions, Root<T> root, CriteriaBuilder cb) {
    return cb.or(
        conditions.stream()
            .map(condition -> buildPredicate(condition, root, cb))
            .toArray(Predicate[]::new));
  }

  @SuppressWarnings("unchecked")
  private Predicate handleComparisonOperation(
      String operation, Map<String, Object> condition, Root<T> root, CriteriaBuilder cb) {
    String path = (String) condition.get("path");
    Object value = condition.get("value");
    Path<Object> fieldPath = root.get(path);

    // Check if the field is an enum type
    Class<?> fieldType = fieldPath.getJavaType();
    if (fieldType.isEnum()) {
      // Convert value to the appropriate enum type if it's a string
      if (value instanceof String string) {
        value = Enum.valueOf((Class<Enum>) fieldType, string);
      }
    }

    // Check if the field is a LOB type
    boolean isLobField = false;
    try {
      isLobField =
          String.class.isAssignableFrom(fieldType)
              && root.getJavaType().getDeclaredField(path).isAnnotationPresent(Lob.class);
    } catch (NoSuchFieldException e) {
      log.warn("Field '{}' not found in {}", path, root.getJavaType().getName());
    }

    // Special handling for LOB fields
    if (isLobField) {
      return switch (operation) {
        case "@eq" -> createLobPredicate(value.toString(), root, cb, path, "eq");
        case "@eqignorecase" ->
            createLobPredicate(value.toString().toLowerCase(), root, cb, path, "eqignorecase");
        case "@contains" -> createLobPredicate(value.toString(), root, cb, path, "contains");
        case "@containsignorecase" ->
            createLobPredicate(
                value.toString().toLowerCase(), root, cb, path, "containsignorecase");
        default ->
            throw new IllegalArgumentException(
                "Unsupported operation for LOB (TEXT) field: " + path + ", operation " + operation);
      };
    }
    return switch (operation) {
      case "@eq" -> cb.equal(fieldPath, value);
      case "@notEq" -> cb.notEqual(fieldPath, value);
      case "@gt" -> cb.greaterThan(fieldPath.as(Comparable.class), (Comparable) value);
      case "@lt" -> cb.lessThan(fieldPath.as(Comparable.class), (Comparable) value);
      case "@gte" -> cb.greaterThanOrEqualTo(fieldPath.as(Comparable.class), (Comparable) value);
      case "@lte" -> cb.lessThanOrEqualTo(fieldPath.as(Comparable.class), (Comparable) value);
      case "@in" -> {
        if (fieldType.isEnum() && value instanceof List<?> list) {
          value =
              list.stream()
                  .map(
                      v ->
                          v instanceof String string
                              ? Enum.valueOf((Class<Enum>) fieldType, string)
                              : v)
                  .toList();
        }
        yield fieldPath.in((List<?>) value);
      }
      case "@not" -> {
        if (fieldType.isEnum() && value instanceof List<?> list) {
          value =
              list.stream()
                  .map(
                      v ->
                          v instanceof String string
                              ? Enum.valueOf((Class<Enum>) fieldType, string)
                              : v)
                  .toList();
        }
        yield cb.not(fieldPath.in((List<?>) value));
      }
      case "@contains" -> cb.like(fieldPath.as(String.class), "%" + value + "%");
      case "@containsignorecase" -> {
        if (!(value instanceof String)) {
          throw new IllegalArgumentException("Value for @containsignorecase must be a string");
        }
        yield cb.like(
            cb.lower(fieldPath.as(String.class)), "%" + value.toString().toLowerCase() + "%");
      }
      case "@regex" -> {
        yield cb.like(fieldPath.as(String.class), (String) value);
      }
      case "@eqignorecase" -> {
        if (!(value instanceof String)) {
          throw new IllegalArgumentException("Value for @eqignorecase must be a string");
        }
        yield cb.equal(cb.lower(fieldPath.as(String.class)), ((String) value).toLowerCase());
      }
      default -> throw new IllegalArgumentException("Unsupported operation: " + operation);
    };
  }

  /**
   * Dynamically builds a {@link Predicate} for fields annotated with {@code @Lob} in JPA, using
   * PostgreSQL-specific native functions. This method supports operations such as equality (`eq`,
   * `eqignorecase`) and partial matching (`contains`, `containsignorecase`) on the content of large
   * object (LOB) fields.
   *
   * <p>PostgreSQL native functions used:
   *
   * <ul>
   *   <li><b>lo_open</b>: Opens a large object (LOB) by its object ID (OID) for reading.
   *   <li><b>loread</b>: Reads a specified number of bytes from the opened LOB.
   *   <li><b>convert_from</b>: Converts the binary data from the LOB into a UTF-8 encoded string.
   * </ul>
   *
   * @param value The value to compare or search within the LOB content.
   * @param root The root of the query, representing the entity being queried.
   * @param cb The {@link CriteriaBuilder} instance used to construct the query predicates.
   * @param operation The type of operation to perform on the LOB content. Supported operations:
   *     <ul>
   *       <li>{@code eq}: Checks for exact equality between the LOB content and the value.
   *       <li>{@code eqignorecase}: Checks for case-insensitive equality.
   *       <li>{@code contains}: Checks if the LOB content contains the value as a substring.
   *       <li>{@code containsignorecase}: Checks if the LOB content contains the value as a
   *           substring, ignoring case.
   *     </ul>
   *
   * @return A {@link Predicate} representing the condition for filtering rows based on the LOB
   *     content.
   * @throws IllegalArgumentException If an unsupported operation is specified.
   *     <ul>
   *       <li><b>PostgreSQL-Specific:</b> This method relies on PostgreSQL's native LOB functions
   *           and will not work with other databases.
   *       <li><b>Performance:</b> Accessing and processing LOB fields may be resource-intensive for
   *           large datasets. Use this method judiciously and ensure proper indexing and query
   *           optimization.
   *     </ul>
   */
  private Predicate createLobPredicate(
      String value, Root<T> root, CriteriaBuilder cb, String path, String operation) {
    var lobContent =
        cb.function(
            "convert_from",
            String.class,
            cb.function(
                "loread",
                byte[].class,
                cb.function(
                    "lo_open", Object.class, root.get(path).as(Integer.class), cb.literal(262144)),
                cb.literal(262144)),
            cb.literal("UTF8"));

    return switch (operation) {
      case "eq" -> cb.equal(lobContent, value);
      case "eqignorecase" -> cb.equal(cb.lower(lobContent), value.toLowerCase());
      case "contains" -> cb.like(lobContent, "%" + value + "%");
      case "containsignorecase" -> cb.like(cb.lower(lobContent), "%" + value.toLowerCase() + "%");
      default -> throw new IllegalArgumentException("Unsupported operation for LOB: " + operation);
    };
  }
}
