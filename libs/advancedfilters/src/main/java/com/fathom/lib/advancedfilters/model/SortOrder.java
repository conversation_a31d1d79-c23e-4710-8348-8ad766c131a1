package com.fathom.lib.advancedfilters.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum SortOrder {
  ASCENDING("ASC"),
  DESCENDING("DESC");

  private final String direction;

  SortOrder(String val) {
    direction = val;
  }

  @JsonCreator
  public static SortOrder valueOfIgnoreCase(String val) {
    for (SortOrder order : SortOrder.values()) {
      if (order.direction.equalsIgnoreCase(val)) {
        return order;
      }
    }
    throw new IllegalArgumentException(
        "No enum constant " + SortOrder.class.getCanonicalName() + "." + val);
  }

  @Override
  public String toString() {
    return direction;
  }

  @JsonValue
  public String getValue() {
    return direction;
  }
}
