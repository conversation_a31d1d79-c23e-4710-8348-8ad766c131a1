package com.fathom.lib.advancedfilters.filters;

import com.fathom.lib.advancedfilters.model.CustomFilter;
import com.fathom.lib.advancedfilters.model.SortOrder;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

public class FilterAndSortInMemoryUtil<T> {

  public Page<T> applyFiltersAndSort(
      List<T> items, CustomFilter customFilter, Comparator<T> comparator, int pageNumber, int pageSize) {

    List<T> filteredItems = applyFiltersInMemoryAndSort(items, customFilter, comparator);

    Pageable pageable = PageRequest.of(pageNumber, pageSize);
    int totalItems = filteredItems.size();
    int startIndex = pageable.getPageNumber() * pageable.getPageSize();
    int endIndex = Math.min(startIndex + pageable.getPageSize(), totalItems);

    List<T> paginatedItems =
        (startIndex < totalItems)
            ? filteredItems.subList(startIndex, endIndex)
            : Collections.emptyList();

    return new PageImpl<>(paginatedItems, PageRequest.of(pageNumber, pageSize), totalItems);
  }

  public List<T> applyFiltersInMemoryAndSort(List<T> items, CustomFilter customFilter, Comparator<T> comparator) {
    List<T> filteredItems = applyFilterRecursive(items, customFilter.getFilter());

    if (customFilter.getSort() != null) {
      SortOrder order = customFilter.getSort().getOrd();

      switch (order) {
        case ASCENDING -> filteredItems.sort(comparator);
        case DESCENDING -> filteredItems.sort(comparator.reversed());
      }
    }

    return filteredItems;
  }

  @SuppressWarnings("unchecked")
  private List<T> applyFilterRecursive(List<T> items, Map<String, Object> filter) {
    if (Objects.isNull(filter) || filter.isEmpty()) {
      return new ArrayList<>(items);
    }

    if (filter.containsKey("@and")) {
      List<T> filteredItems = items;
      List<Map<String, Object>> andFilters = (List<Map<String, Object>>) filter.get("@and");
      if (andFilters.isEmpty()) {
        return new ArrayList<>(items);
      }
      for (Map<String, Object> andFilter : andFilters) {
        filteredItems = applyFilterRecursive(filteredItems, andFilter);
      }
      return filteredItems;
    } else if (filter.containsKey("@or")) {
      // Use LinkedHashSet to maintain order while eliminating duplicates
      Set<T> combinedResults = new LinkedHashSet<>();
      List<Map<String, Object>> orFilters = (List<Map<String, Object>>) filter.get("@or");
      for (Map<String, Object> orFilter : orFilters) {
        List<T> orResults = applyFilterRecursive(items, orFilter);
        combinedResults.addAll(orResults);
      }
      return new ArrayList<>(combinedResults);
    } else if (filter.containsKey("@not")) {
      List<T> combinedResults = new ArrayList<>();
      Object notValue = filter.get("@not");

      if (!(notValue instanceof List)) {
        if (notValue instanceof Map) {
          Map<String, Object> notFilter = (Map<String, Object>) notValue;
          List<T> filtered = applyFilterRecursive(items, notFilter);
          combinedResults.addAll(excludeItems(items, filtered));
        } else {
          throw new IllegalArgumentException(
              "The @not operator requires a list of conditions, but was provided with: "
                  + Objects.toString(notValue, "null"));
        }
      } else {
        List<?> notList = (List<?>) notValue;
        for (Object item : notList) {
          if (!(item instanceof Map)) {
            throw new IllegalArgumentException(
                "Each condition in @not must be a map, but found: "
                    + Objects.toString(item, "null"));
          }

          Map<String, Object> notFilter = (Map<String, Object>) item;
          List<T> filtered = applyFilterRecursive(items, notFilter);
          combinedResults.addAll(excludeItems(items, filtered));
        }
      }

      return combinedResults;
    } else {
      return applyCondition(items, filter);
    }
  }

  private List<T> excludeItems(List<T> originalItems, List<T> filteredItems) {
    return originalItems.stream().filter(item -> !filteredItems.contains(item)).toList();
  }

  @SuppressWarnings("unchecked")
  private List<T> applyCondition(List<T> items, Map<String, Object> condition) {
    List<T> filteredItems = new ArrayList<>();
    String conditionType = condition.keySet().iterator().next();
    Map<String, Object> criteria = (Map<String, Object>) condition.get(conditionType);
    final String valueKeyName = "value";

    String path = (String) criteria.get("path");
    Object value = criteria.get(valueKeyName);

    for (T item : items) {
      Object itemValue = getValueByPath(item, path);

      // Handle empty strings - they are considered is if they were not present
      if (value instanceof String strValue && strValue.isBlank()) {
        filteredItems.add(item);
        continue;
      }

      // Handle null field values differently based on operation
      if (Objects.isNull(itemValue)) {
        switch (conditionType) {
          case "@notEq":
            // If field is null and we're checking not equal to something,
            // the condition is true (null IS not equal to any non-null value)
            if (value != null) {
              filteredItems.add(item);
            }
            break;
          case "@eq":
            // For equals, null only equals null
            if (value == null) {
              filteredItems.add(item);
            }
            break;
          case "@contains":
          case "@containsignorecase":
            // Null collections can't contain anything
            break;
          default:
            // For other operations (gt, lt, regex, etc.), null fails the condition
            break;
        }
        continue;
      }

      if (itemValue instanceof Enum<?> enumValue) {
        value = getEnumValue(enumValue.getClass(), value);
      }

      if (itemValue instanceof Number && value instanceof Number) {
        Double numItemValue = toDouble(itemValue);
        Double numValue = toDouble(value);
        switch (conditionType) {
          case "@gt":
            if (numItemValue > numValue) filteredItems.add(item);
            break;
          case "@lt":
            if (numItemValue < numValue) filteredItems.add(item);
            break;
          case "@gte":
            if (numItemValue >= numValue) filteredItems.add(item);
            break;
          case "@lte":
            if (numItemValue <= numValue) filteredItems.add(item);
            break;
          case "@eq":
            if (numItemValue.equals(numValue)) filteredItems.add(item);
            break;
          case "@in":
            List<Object> inValues = (List<Object>) criteria.get(valueKeyName);
            if (inValues.stream().map(this::toDouble).anyMatch(val -> val.equals(numItemValue)))
              filteredItems.add(item);
            break;
          case "@between":
            List<Object> rangeValues = (List<Object>) criteria.get(valueKeyName);
            if (numItemValue >= toDouble(rangeValues.get(0))
                && numItemValue <= toDouble(rangeValues.get(1))) filteredItems.add(item);
            break;
        }
        continue;
      }

      // Handling @in for Collection (List, Set, Array)
      if ("@in".equals(conditionType)
          && (itemValue instanceof Collection<?> || itemValue.getClass().isArray())) {
        Collection<?> itemCollection =
            (itemValue instanceof Collection<?>)
                ? (Collection<?>) itemValue
                : Arrays.asList((Object[]) itemValue);

        List<Object> inValues = (List<Object>) criteria.get(valueKeyName);
        if (itemCollection.stream().anyMatch(inValues::contains)) {
          filteredItems.add(item);
        }
        continue;
      }

      switch (conditionType) {
        case "@eq":
          if (itemValue.equals(value)) filteredItems.add(item);
          break;
        case "@eqignorecase":
          if (itemValue.toString().equalsIgnoreCase(value.toString())) filteredItems.add(item);
          break;
        case "@notEq":
          if (!itemValue.equals(value)) filteredItems.add(item);
          break;
        case "@contains":
          if (value instanceof Collection<?> collection && collection.size() == 1)
            value = collection.iterator().next();

          if (itemValue instanceof String strItemValue && strItemValue.contains(value.toString()))
            filteredItems.add(item);
          else if (itemValue instanceof Collection<?> collectionItemValue) {
            if (value instanceof Collection<?> valueCollection) {
              // Convert both collections to strings for comparison
              Set<String> itemValueStrings = collectionItemValue.stream()
                      .map(Object::toString)
                      .collect(Collectors.toSet());

              Set<String> valueStrings = valueCollection.stream()
                      .map(Object::toString)
                      .collect(Collectors.toSet());

              // Check if all elements from valueCollection are present in collectionItemValue
              if (itemValueStrings.containsAll(valueStrings)) {
                filteredItems.add(item);
              }
            } else {
              // Original behavior for single value - also convert to string
              Object finalValue = value;
              if (collectionItemValue.stream()
                      .map(Object::toString)
                      .anyMatch(str -> str.equals(finalValue.toString()))) {
                filteredItems.add(item);
              }
            }
          }
          else if (itemValue.getClass().isArray()
              && Arrays.asList((Object[]) itemValue).contains(value)) filteredItems.add(item);
          break;
        case "@containsignorecase":
          if (value instanceof Collection<?> collection && collection.size() == 1)
            value = collection.iterator().next();

          if (itemValue instanceof String strItemValue && strItemValue.toLowerCase().contains(value.toString().toLowerCase()))
            filteredItems.add(item);
          else if (itemValue instanceof Collection<?> collectionItemValue) {
            if (value instanceof Collection<?> valueCollection) {
              // Convert both collections to strings for comparison
              Set<String> itemValueStrings = collectionItemValue.stream()
                      .map(Object::toString)
                      .map(String::toLowerCase)
                      .collect(Collectors.toSet());

              Set<String> valueStrings = valueCollection.stream()
                      .map(Object::toString)
                      .map(String::toLowerCase)
                      .collect(Collectors.toSet());

              // Check if all elements from valueCollection are present in collectionItemValue
              if (itemValueStrings.containsAll(valueStrings)) {
                filteredItems.add(item);
              }
            } else {
              // Original behavior for single value - also convert to string
              Object finalValue = value;
              if (collectionItemValue.stream()
                      .map(Object::toString)
                      .map(String::toLowerCase)
                      .anyMatch(str -> str.equals(finalValue.toString().toLowerCase()))) {
                filteredItems.add(item);
              }
            }
          }
          else if (itemValue.getClass().isArray()
                  && Arrays.asList((Object[]) itemValue).contains(value)) filteredItems.add(item);
          break;
        case "@regex":
          if (itemValue.toString().matches(value.toString())) filteredItems.add(item);
          break;
        default:
          throw new UnsupportedOperationException("Condition not supported: " + conditionType);
      }
    }
    return filteredItems;
  }

  private Object getValueByPath(T item, String path) {
    try {
      Method method;
      Class<?> itemClass = item.getClass();

      // First try direct field accessor for records (field()) - java records
      try {
        method = itemClass.getMethod(path);
      } catch (NoSuchMethodException e) {
        // If not found, try traditional getter (getField()) - java beans
        String getterName = "get" + Character.toUpperCase(path.charAt(0)) + path.substring(1);
        method = itemClass.getMethod(getterName);
      }

      return method.invoke(item);
    } catch (Exception e) {
      throw new RuntimeException(
          "Error accessing path: " + path + " on class " + item.getClass().getName(), e);
    }
  }

  private <E extends Enum<E>> E getEnumValue(Class<E> enumClass, Object value) {
    if (value == null) {
      throw new IllegalArgumentException("Enum value cannot be null");
    }
    // If value is already an enum, return it after type checking
    if (value.getClass().isEnum()) {
      if (enumClass.isInstance(value)) {
        @SuppressWarnings("unchecked")
        E enumValue = (E) value;
        return enumValue;
      } else {
        throw new IllegalArgumentException(
            "Enum value does not belong to the specified enum class: " + enumClass.getName());
      }
    }

    if (value instanceof String strValue) { // Pattern matching for instanceof
      for (E enumConstant : enumClass.getEnumConstants()) {
        if (enumConstant.name().equalsIgnoreCase(strValue)) {
          return enumConstant;
        }
      }
    } else if (value instanceof Number number) { // Pattern matching for instanceof
      for (E enumConstant : enumClass.getEnumConstants()) {
        try {
          Object enumValue = enumClass.getMethod("getValue").invoke(enumConstant);
          if (enumValue instanceof Number numValue && numValue.equals(number)) {
            return enumConstant;
          }
        } catch (NoSuchMethodException
            | IllegalAccessException
            | InvocationTargetException ignored) {
          // Ignore if getValue method doesn't exist
        }
      }
    }
    throw new IllegalArgumentException("Invalid enum value: " + value);
  }

  /** Converts any numeric value to `Double` for consistent comparisons. */
  private Double toDouble(Object value) {
    if (value instanceof Number num) {
      return num.doubleValue();
    }
    throw new IllegalArgumentException("Expected a numeric value but got: " + value);
  }
}
