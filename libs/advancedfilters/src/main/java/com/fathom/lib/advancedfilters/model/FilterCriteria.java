package com.fathom.lib.advancedfilters.model;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class FilterCriteria {
  public static LogicalBuilder filter() {
    return new LogicalBuilder();
  }

  public interface FilterExpression {
    Map<String, Object> build();
  }

  public static class LogicalBuilder implements FilterExpression {
    private final Map<String, Object> criteria = new HashMap<>();
    private String operator;
    private List<FilterExpression> expressions;

    public LogicalBuilder() {}

    private LogicalBuilder(String operator, FilterExpression... expressions) {
      this.operator = operator;
      this.expressions = Arrays.asList(expressions);
    }

    // Logical operators
    public LogicalBuilder and(FilterExpression... expressions) {
      appendToMap("@and", expressions);
      return this;
    }

    public LogicalBuilder or(FilterExpression... expressions) {
      appendToMap("@or", expressions);
      return this;
    }

    public LogicalBuilder not(FilterExpression... expressions) {
      appendToMap("@not", expressions);
      return this;
    }

    @Override
    public Map<String, Object> build() {
      // For nested builders, create the structure based on operator and expressions
      if (operator != null && expressions != null) {
        Map<String, Object> result = new LinkedHashMap<>();
        result.put(
            operator,
            expressions.stream().map(FilterExpression::build).collect(Collectors.toList()));
        return result;
      }
      // For the root builder, return the criteria map as LinkedHashMap
      return new LinkedHashMap<>(criteria);
    }

    private void appendToMap(String key, FilterExpression... expressions) {
      if (criteria.containsKey(key)) {
        var existingExpressions = (List<FilterExpression>) criteria.get(key);
        if (existingExpressions != null) {
          var newExpressions =
              Arrays.stream(expressions)
                  .map(FilterExpression::build)
                  .filter(ex -> !existingExpressions.contains(ex));
          List<Object> concatenated =
              Stream.concat(existingExpressions.stream(), newExpressions).toList();
          criteria.put(key, concatenated);
        }
      } else {
        criteria.put(key, Arrays.stream(expressions).map(FilterExpression::build).toList());
      }
    }
  }

  // Condition factories
  public static Condition eq(String path, Object value) {
    return new Condition("@eq", path, value);
  }

  public static Condition gt(String path, Object value) {
    return new Condition("@gt", path, value);
  }

  public static Condition lt(String path, Object value) {
    return new Condition("@lt", path, value);
  }

  public static Condition gte(String path, Object value) {
    return new Condition("@gte", path, value);
  }

  public static Condition lte(String path, Object value) {
    return new Condition("@lte", path, value);
  }

  public static Condition notEq(String path, Object value) {
    return new Condition("@notEq", path, value);
  }

  public static Condition contains(String path, Object value) {
    return new Condition("@contains", path, value);
  }

  public static Condition containsIgnoreCase(String path, String value) {
    return new Condition("@containsignorecase", path, value);
  }

  public static Condition eqIgnoreCase(String path, String value) {
    return new Condition("@eqignorecase", path, value);
  }

  public static Condition regex(String path, String pattern) {
    return new Condition("@regex", path, pattern);
  }

  public static Condition in(String path, List<?> values) {
    return new Condition("@in", path, values);
  }

  public static Condition between(String path, Object min, Object max) {
    return new Condition("@between", path, List.of(min, max));
  }

  public static class Condition implements FilterExpression {
    private final String operator;
    private final String path;
    private final Object value;

    private Condition(String operator, String path, Object value) {
      this.operator = operator;
      this.path = path;
      this.value = value;
    }

    @Override
    public Map<String, Object> build() {
      Map<String, Object> innerMap = new LinkedHashMap<>();
      innerMap.put("path", path);
      innerMap.put("value", value);

      Map<String, Object> outerMap = new LinkedHashMap<>();
      outerMap.put(operator, innerMap);
      return outerMap;
    }
  }
}
