package com.fathom.lib.advancedfilters.util;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class FilterCleaner {

  private FilterCleaner() {}

  // A special object used to signal that the parent entry should be removed.
  private static final Object REMOVAL_MARKER = new Object();

  /**
   * Recursively checks for a map with a "path" key that matches any of the values in
   * targetPathValues. If found, instructs the parent to remove the entry with a key that starts
   * with "@".
   *
   * @param input The input Map<String, Object>
   * @param targetPathValues A set of path values that trigger removal if encountered
   * @return The cleaned Map<String, Object>
   */
  @SuppressWarnings("unchecked")
  public static Map<String, Object> removeParentIfPathEquals(
      Map<String, Object> input, Set<String> targetPathValues) {
    List<String> keysToRemove = new ArrayList<>();

    for (Map.Entry<String, Object> entry : input.entrySet()) {
      Object processed = processObject(entry.getValue(), targetPathValues);

      // If the child returned the REMOVAL_MARKER, remove this parent entry if it starts with '@'
      if (processed == REMOVAL_MARKER && entry.getKey().startsWith("@")) {
        keysToRemove.add(entry.getKey());
      } else {
        entry.setValue(processed);
      }
    }

    // Remove any flagged parent keys
    for (String key : keysToRemove) {
      input.remove(key);
    }

    return input;
  }

  /**
   * Removes every parent entry whose subtree does not contain any "path" value from the provided
   * set. Parents are identified as entries whose keys start with '@'.
   *
   * @param input The input Map<String, Object>
   * @param targetPathValues A set of target path values to look for
   * @return The cleaned Map<String, Object>
   */
  @SuppressWarnings("unchecked")
  public static Map<String, Object> removeParentsWithoutTargetPaths(
      Map<String, Object> input, Set<String> targetPathValues) {
    // We'll store keys to remove after iteration
    List<String> keysToRemove = new ArrayList<>();

    for (Map.Entry<String, Object> entry : input.entrySet()) {
      Object processed = processObjectForNonexistentPaths(entry.getValue(), targetPathValues);

      // If processed is null, it means no target path was found in the child's subtree
      // If this entry's key starts with '@', we remove it from the parent.
      if (processed == null && entry.getKey().startsWith("@")) {
        keysToRemove.add(entry.getKey());
      } else {
        entry.setValue(processed);
      }
    }

    // Remove parent entries that had no target paths in their subtree
    for (String key : keysToRemove) {
      input.remove(key);
    }

    return input;
  }

  /**
   * Recursively processes the object to determine if it contains any path from targetPathValues.
   * Returns: - The processed object if at least one matching path was found in its subtree. - null
   * if no matching path was found, signaling that the parent might need to be removed.
   */
  @SuppressWarnings("unchecked")
  private static Object processObjectForNonexistentPaths(Object obj, Set<String> targetPathValues) {
    if (obj instanceof Map) {
      Map<String, Object> map = (Map<String, Object>) obj;

      boolean foundPath = false;

      // Check if this map has a path in targetPathValues
      if (map.containsKey("path") && targetPathValues.contains(map.get("path"))) {
        foundPath = true;
      }

      // Process children
      List<String> keysToRemove = new ArrayList<>();
      for (Map.Entry<String, Object> entry : map.entrySet()) {
        if (!entry.getKey().equals("path")) { // We already checked 'path'
          Object processed = processObjectForNonexistentPaths(entry.getValue(), targetPathValues);
          if (processed == null) {
            // Child subtree has no matching path
            keysToRemove.add(entry.getKey());
          } else {
            // Child subtree found a matching path
            foundPath = true;
            entry.setValue(processed);
          }
        }
      }

      // Remove children that returned null
      for (String key : keysToRemove) {
        map.remove(key);
      }

      // If no path found in this map or its children, return null
      return foundPath ? map : null;

    } else if (obj instanceof List) {
      List<Object> list = (List<Object>) obj;
      boolean foundPath = false;

      // We'll rebuild the list or remove elements that don't have paths
      for (int i = 0; i < list.size(); i++) {
        Object processed = processObjectForNonexistentPaths(list.get(i), targetPathValues);
        if (processed == null) {
          // Remove elements that don't have any matching path
          list.remove(i);
          i--;
        } else {
          // If processed is not null, means a path was found
          foundPath = true;
          list.set(i, processed);
        }
      }

      // If after processing all elements no path was found, return null
      return foundPath ? list : null;
    }

    // For primitives (String, Number, etc.) without a 'path' structure, just return null
    // since they don't contain a path
    return null;
  }

  @SuppressWarnings("unchecked")
  private static Object processObject(Object obj, Set<String> targetPathValues) {
    if (obj instanceof Map) {
      Map<String, Object> map = (Map<String, Object>) obj;

      // If this map has a "path" key and matches one of the target path values, signal parent
      // removal
      if (map.containsKey("path") && targetPathValues.contains(map.get("path"))) {
        return REMOVAL_MARKER;
      }

      // Otherwise, recurse through its values
      List<String> keysToRemove = new ArrayList<>();
      for (Map.Entry<String, Object> entry : map.entrySet()) {
        Object processed = processObject(entry.getValue(), targetPathValues);
        if (processed == REMOVAL_MARKER) {
          // The current map encountered a child needing removal.
          // In this implementation, just remove that entry.
          keysToRemove.add(entry.getKey());
        } else {
          entry.setValue(processed);
        }
      }

      for (String key : keysToRemove) {
        map.remove(key);
      }

      return map;

    } else if (obj instanceof List) {
      List<Object> list = (List<Object>) obj;
      for (int i = 0; i < list.size(); i++) {
        Object processed = processObject(list.get(i), targetPathValues);
        if (processed == REMOVAL_MARKER) {
          // Remove this element from the list if it triggers removal
          list.remove(i);
          i--;
        } else {
          list.set(i, processed);
        }
      }
      return list;
    }

    // For other types (String, Number, etc.) just return as is.
    return obj;
  }

  public static Map<String, Object> deepCopy(Map<String, Object> original) {
    // Use your preferred library or custom implementation for deep cloning
    try {
      ByteArrayOutputStream bos = new ByteArrayOutputStream();
      ObjectOutputStream out = new ObjectOutputStream(bos);
      out.writeObject(original);
      out.flush();
      ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
      ObjectInputStream in = new ObjectInputStream(bis);
      return (Map<String, Object>) in.readObject();
    } catch (IOException | ClassNotFoundException e) {
      throw new RuntimeException("Error during deep copy", e);
    }
  }
}
