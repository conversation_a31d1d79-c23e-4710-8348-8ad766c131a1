package com.fathom.lib.advancedfilters.model;

import static org.assertj.core.api.Assertions.*;

import java.util.List;
import org.junit.jupiter.api.Test;

class FilterCriteriaTest {

  @Test
  void usingAndTwiceShouldContenateAndInsteadOfWiping() {
    var criteria =
        FilterCriteria.filter()
            .and(FilterCriteria.eq("category", "Electronics"), FilterCriteria.eq("price", 999.99))
            .and(FilterCriteria.lt("blah", 99.99))
            .build();

    assertThat(criteria.get("@and")).isNotNull().asInstanceOf(LIST).hasSize(3);
  }

  @Test
  void usingOrTwiceShouldConcatenateResultsAndNotRemoveTHem() {
    var criteria =
        FilterCriteria.filter()
            .or(FilterCriteria.eq("category", "Electronics"), FilterCriteria.eq("price", 999.99))
            .or(FilterCriteria.lt("price", 99.99))
            .build();

    assertThat(criteria.get("@or"))
        .isNotNull()
        .isInstanceOf(List.class)
        .asInstanceOf(LIST)
        .hasSize(3);
  }

  @Test
  void usingCombinationOfAndAndOrMultipleTimesShouldConstructProperly() {
    var criteria =
        FilterCriteria.filter()
            .or(FilterCriteria.eq("category", "Electronics"), FilterCriteria.eq("price", 999.99))
            .or(FilterCriteria.lt("price", 99.99))
            .and(FilterCriteria.eq("category", "Furniture"), FilterCriteria.eq("price", 199.99))
            .and(FilterCriteria.lt("price", 49.99), FilterCriteria.gt("price", 99.99))
            .build();

    assertThat(criteria.get("@or"))
        .isNotNull()
        .isInstanceOf(List.class)
        .asInstanceOf(LIST)
        .hasSize(3);

    assertThat(criteria.get("@and"))
        .isNotNull()
        .isInstanceOf(List.class)
        .asInstanceOf(LIST)
        .hasSize(4);
  }

  @Test
  void usingCombinationofAndsIfTheCriteriAreTheSameTheyAreNotDuplicated() {
    var criteria =
        FilterCriteria.filter()
            .or(FilterCriteria.eq("category", "Electronics"), FilterCriteria.eq("price", 999.99))
            .or(FilterCriteria.eq("price", 999.99))
            .build();

    assertThat(criteria.get("@or"))
        .isNotNull()
        .isInstanceOf(List.class)
        .asInstanceOf(LIST)
        .hasSize(2);
  }
}
