package com.fathom.lib.advancedfilters.filters;

import static org.assertj.core.api.Assertions.*;

import com.fathom.lib.advancedfilters.model.CustomFilter;
import com.fathom.lib.advancedfilters.model.FilterCriteria;
import com.fathom.lib.advancedfilters.model.SortOrder;

import java.util.Comparator;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;

public class FilterAndSortInMemoryUtilTest {

    @Test
    public void testAndEq() {
        List<Product> products =
                List.of(
                        new Product("Laptop", "Electronics", 999.99, 30, Set.of("prop1", "prop2")),
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("prop1", "prop2")),
                        new Product("Chair", "Furniture", 89.99, 15, Set.of("prop1", "prop2")),
                        new Product("Table", "Furniture", 199.99, 5, Set.of("prop1", "prop2")));

        CustomFilter filter = new CustomFilter();
        filter.setFilter(
                FilterCriteria.filter()
                        .and(FilterCriteria.eq("category", "Electronics"), FilterCriteria.eq("price", 999.99))
                        .build());
        CustomFilter.CustomSort sort = new CustomFilter.CustomSort();
        sort.setBy("price");
        sort.setOrd(SortOrder.DESCENDING);
        filter.setSort(sort);

        FilterAndSortInMemoryUtil<Product> filterUtil = new FilterAndSortInMemoryUtil<>();
        int pageNumber = 0;
        int pageSize = 10;
        Page<Product> result = filterUtil.applyFiltersAndSort(products, filter, Comparator.comparingDouble(Product::price), pageNumber, pageSize);

        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getTotalElements()).isEqualTo(1);
        assertThat(result.getContent().get(0).name()).isEqualTo("Laptop");
        assertThat(result.getContent().get(0).category()).isEqualTo("Electronics");
        assertThat(result.getContent().get(0).price()).isEqualTo(999.99);
        assertThat(result.getContent().get(0).quantity()).isEqualTo(30);
    }

    @Test
    public void testAndNotEq() {
        List<Product> products =
                List.of(
                        new Product("Laptop", "Electronics", 999.99, 30, Set.of("prop1", "prop2")),
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("prop1", "prop2")),
                        new Product("Chair", "Furniture", 89.99, 15, Set.of("prop1", "prop2")),
                        new Product("Table", "Furniture", 199.99, 5, Set.of("prop1", "prop2")));

        CustomFilter filter = new CustomFilter();
        filter.setFilter(
                FilterCriteria.filter()
                        .and(FilterCriteria.filter().not(FilterCriteria.eq("name", "Laptop")))
                        .build());
        CustomFilter.CustomSort sort = new CustomFilter.CustomSort();
        sort.setBy("price");
        sort.setOrd(SortOrder.DESCENDING);
        filter.setSort(sort);

        FilterAndSortInMemoryUtil<Product> filterUtil = new FilterAndSortInMemoryUtil<>();
        int pageNumber = 0;
        int pageSize = 10;
        Page<Product> result = filterUtil.applyFiltersAndSort(products, filter, Comparator.comparingDouble(Product::price), pageNumber, pageSize);

        assertThat(result.getContent()).hasSize(3);
        assertThat(result.getTotalElements()).isEqualTo(3);
        assertThat(result.getContent())
                .contains(
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("prop1", "prop2")),
                        new Product("Chair", "Furniture", 89.99, 15, Set.of("prop1", "prop2")),
                        new Product("Table", "Furniture", 199.99, 5, Set.of("prop1", "prop2")));
    }

    @Test
    public void testOrEq() {
        List<Product> products =
                List.of(
                        new Product("Laptop", "Electronics", 999.99, 30, Set.of("prop1", "prop2")),
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("prop1", "prop2")),
                        new Product("Chair", "Furniture", 89.99, 15, Set.of("prop1", "prop2")),
                        new Product("Table", "Furniture", 199.99, 5, Set.of("prop1", "prop2")));

        CustomFilter filter = new CustomFilter();
        filter.setFilter(
                FilterCriteria.filter()
                        .or(FilterCriteria.eq("name", "Laptop"), FilterCriteria.eq("name", "Phone"))
                        .build());
        CustomFilter.CustomSort sort = new CustomFilter.CustomSort();
        sort.setBy("price");
        sort.setOrd(SortOrder.DESCENDING);
        filter.setSort(sort);

        FilterAndSortInMemoryUtil<Product> filterUtil = new FilterAndSortInMemoryUtil<>();
        int pageNumber = 0;
        int pageSize = 10;
        Page<Product> result = filterUtil.applyFiltersAndSort(products, filter, Comparator.comparingDouble(Product::price), pageNumber, pageSize);

        assertThat(result.getContent()).hasSize(2);
        assertThat(result.getTotalElements()).isEqualTo(2);
        assertThat(result.getContent())
                .contains(
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("prop1", "prop2")),
                        new Product("Laptop", "Electronics", 999.99, 30, Set.of("prop1", "prop2")));
    }

    @Test
    public void testAndNot() {
        List<Product> products =
                List.of(
                        new Product("Laptop", "Electronics", 999.99, 30, Set.of("prop1", "prop2")),
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("prop1", "prop2")),
                        new Product("Chair", "Furniture", 89.99, 15, Set.of("prop1", "prop2")),
                        new Product("Table", "Furniture", 199.99, 5, Set.of("prop1", "prop2")));

        CustomFilter filter = new CustomFilter();
        filter.setFilter(
                FilterCriteria.filter()
                        .and(FilterCriteria.filter().not(FilterCriteria.eq("name", "Laptop")))
                        .build());
        CustomFilter.CustomSort sort = new CustomFilter.CustomSort();
        sort.setBy("price");
        sort.setOrd(SortOrder.ASCENDING);
        filter.setSort(sort);

        FilterAndSortInMemoryUtil<Product> filterUtil = new FilterAndSortInMemoryUtil<>();
        int pageNumber = 0;
        int pageSize = 10;
        Page<Product> result = filterUtil.applyFiltersAndSort(products, filter, Comparator.comparingDouble(Product::price), pageNumber, pageSize);

        assertThat(result.getContent()).hasSize(3);
        assertThat(result.getTotalElements()).isEqualTo(3);
        assertThat(result.getContent())
                .contains(
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("prop1", "prop2")),
                        new Product("Chair", "Furniture", 89.99, 15, Set.of("prop1", "prop2")),
                        new Product("Table", "Furniture", 199.99, 5, Set.of("prop1", "prop2")));
    }

    @Test
    void andStatementsShouldNotOverrideEachOther() {
        List<Product> products =
                List.of(
                        new Product("Laptop", "Electronics", 999.99, 30, Set.of("prop1", "prop2")),
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("prop1", "prop2")),
                        new Product("Chair", "Furniture", 89.99, 15, Set.of("prop1", "prop2")),
                        new Product("Table", "Furniture", 199.99, 5, Set.of("prop1", "prop2")));

        CustomFilter filter = new CustomFilter();
        filter.setFilter(
                FilterCriteria.filter()
                        .and(FilterCriteria.filter().not(FilterCriteria.eq("name", "Laptop")))
                        .and()
                        .build());

        FilterAndSortInMemoryUtil<Product> filterUtil = new FilterAndSortInMemoryUtil<>();
        int pageNumber = 0;
        int pageSize = 10;
        Page<Product> result = filterUtil.applyFiltersAndSort(products, filter, Comparator.comparingDouble(Product::price), pageNumber, pageSize);

        assertThat(result.getContent()).hasSize(3);
        assertThat(result.getTotalElements()).isEqualTo(3);
        assertThat(result.getContent())
                .contains(
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("prop1", "prop2")),
                        new Product("Chair", "Furniture", 89.99, 15, Set.of("prop1", "prop2")),
                        new Product("Table", "Furniture", 199.99, 5, Set.of("prop1", "prop2")));
    }

    @Test
    void whenContainsHasAListAsParameterItReturnsCorrectResults() {
        List<Product> products =
                List.of(
                        new Product("Laptop", "Electronics", 999.99, 30, Set.of("A", "E")),
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("B", "F")),
                        new Product("Chair", "Furniture", 89.99, 15, Set.of("C", "A")),
                        new Product("Table", "Furniture", 199.99, 5, Set.of("D", "B")));

        CustomFilter filter = new CustomFilter();
        filter.setFilter(
                FilterCriteria.filter()
                        .and(
                                // use case: when we deserialize @contains in json it becomes a list
                                FilterCriteria.contains("props", List.of("A")))
                        .build());

        FilterAndSortInMemoryUtil<Product> filterUtil = new FilterAndSortInMemoryUtil<>();
        int pageNumber = 0;
        int pageSize = 10;
        Page<Product> result = filterUtil.applyFiltersAndSort(products, filter, Comparator.comparingDouble(Product::price), pageNumber, pageSize);

        assertThat(result.getContent()).hasSize(2);
        assertThat(result.getTotalElements()).isEqualTo(2);
        assertThat(result.getContent()).map(Product::name).containsExactlyInAnyOrder("Laptop", "Chair");
    }

    @Test
    void whenContainsHasValueAsParameterItContainsCorrectValue() {
        List<Product> products =
                List.of(
                        new Product("Laptop", "Electronics", 999.99, 30, Set.of("A", "E")),
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("B", "F")),
                        new Product("Chair", "Furniture", 89.99, 15, Set.of("C", "A")),
                        new Product("Table", "Furniture", 199.99, 5, Set.of("D", "B")));

        CustomFilter filter = new CustomFilter();
        filter.setFilter(FilterCriteria.filter().and(FilterCriteria.contains("props", "B")).build());

        FilterAndSortInMemoryUtil<Product> filterUtil = new FilterAndSortInMemoryUtil<>();
        int pageNumber = 0;
        int pageSize = 10;
        Page<Product> result = filterUtil.applyFiltersAndSort(products, filter, Comparator.comparingDouble(Product::price), pageNumber, pageSize);

        assertThat(result.getContent()).hasSize(2);
        assertThat(result.getTotalElements()).isEqualTo(2);
        assertThat(result.getContent()).map(Product::name).containsExactlyInAnyOrder("Phone", "Table");
    }

    @Test
    void sortingDoesntThrowExceptionWhenValuesAreNull() {
        List<Product> products =
                List.of(
                        new Product("Laptop", "Electronics", 999.99, 30, null),
                        new Product("Phone", "Electronics", 499.99, 20, null),
                        new Product("Chair", "Furniture", 89.99, 15, null),
                        new Product("Table", "Furniture", 199.99, 5, null));

        CustomFilter filter = new CustomFilter();
        filter.setFilter(
                FilterCriteria.filter().and(FilterCriteria.eq("category", "Electronics")).build());

        CustomFilter.CustomSort sort = new CustomFilter.CustomSort();
        sort.setOrd(SortOrder.DESCENDING);
        sort.setBy("props");
        filter.setSort(sort);

        FilterAndSortInMemoryUtil<Product> filterUtil = new FilterAndSortInMemoryUtil<>();
        int pageNumber = 0;
        int pageSize = 10;

        Comparator<Product> comp = Comparator.comparing(o -> o.props() == null ? 0 : o.props().size(), Comparator.nullsLast(Comparator.naturalOrder()));
        assertThatCode(() -> filterUtil.applyFiltersAndSort(products, filter, comp, pageNumber, pageSize))
                .doesNotThrowAnyException();

        Page<Product> result = filterUtil.applyFiltersAndSort(products, filter, comp, pageNumber, pageSize);

        assertThat(result.getContent()).hasSize(2);
        assertThat(result.getTotalElements()).isEqualTo(2);
        assertThat(result.getContent()).map(Product::name).containsExactlyInAnyOrder("Phone", "Laptop");
    }

    @Test
    void sortingWhenOnlyOneFiledIsNullShouldBeDeterministic_NullShouldBeAlwaysLast() {
        List<Product> products =
                List.of(
                        new Product("Laptop", "Electronics", 999.99, 30, null),
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("A")),
                        new Product("Chair", "Furniture", 89.99, 15, null),
                        new Product("Table", "Furniture", 199.99, 5, null));

        CustomFilter filter = new CustomFilter();
        filter.setFilter(
                FilterCriteria.filter().and(FilterCriteria.eq("category", "Electronics")).build());

        CustomFilter.CustomSort sort = new CustomFilter.CustomSort();
        sort.setOrd(SortOrder.DESCENDING);
        sort.setBy("props");
        filter.setSort(sort);

        FilterAndSortInMemoryUtil<Product> filterUtil = new FilterAndSortInMemoryUtil<>();
        int pageNumber = 0;
        int pageSize = 10;

        Comparator<Product> comp = Comparator.comparing(o -> o.props() == null ? 0 : o.props().size(), Comparator.nullsLast(Comparator.naturalOrder()));
        assertThatCode(() -> filterUtil.applyFiltersAndSort(products, filter, comp, pageNumber, pageSize))
                .doesNotThrowAnyException();

        Page<Product> result = filterUtil.applyFiltersAndSort(products, filter, comp, pageNumber, pageSize);

        assertThat(result.getContent()).hasSize(2);
        assertThat(result.getTotalElements()).isEqualTo(2);
        assertThat(result.getContent()).map(Product::name).containsExactly("Phone", "Laptop");
    }

    @Test
    void usingFiltersShouldNotReturnDuplicates() {
        List<Product> products =
                List.of(
                        new Product("Laptop", "Electronics", 999.99, 30, null),
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("A")),
                        new Product("Chair", "Furniture", 89.99, 15, null),
                        new Product("Table", "Furniture", 199.99, 5, null));

        CustomFilter filter = new CustomFilter();
        var criteria =
                FilterCriteria.filter()
                        .or(FilterCriteria.eq("category", "Electronics"), FilterCriteria.eq("price", 999.99))
                        .or(FilterCriteria.notEq("props", ""))
                        .build();

        filter.setFilter(criteria);

        FilterAndSortInMemoryUtil<Product> filterUtil = new FilterAndSortInMemoryUtil<>();
        int pageNumber = 0;
        int pageSize = 10;
        Page<Product> result = filterUtil.applyFiltersAndSort(products, filter, Comparator.comparingDouble(Product::price), pageNumber, pageSize);

        assertThat(result.getContent()).hasSize(4);
        assertThat(result.getTotalElements()).isEqualTo(4);
        assertThat(result.getContent())
                .map(Product::name)
                .containsExactly("Laptop", "Phone", "Chair", "Table");
    }

    @Test
    void usingContainsIgnoreCaseFindsCorrectResults() {
        List<Product> products =
                List.of(
                        new Product("Laptop", "Electronics", 999.99, 30, Set.of("A")),
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("A")),
                        new Product("Chair", "Furniture", 89.99, 15, Set.of("B")),
                        new Product("Table", "Furniture", 199.99, 5, Set.of("C"))
                );

        CustomFilter filter = new CustomFilter();
        var criteria =
                FilterCriteria.filter()
                        .and(FilterCriteria.containsIgnoreCase("props", "a"))
                        .build();

        filter.setFilter(criteria);

        FilterAndSortInMemoryUtil<Product> filterUtil = new FilterAndSortInMemoryUtil<>();
        int pageNumber = 0;
        int pageSize = 10;
        Page<Product> result = filterUtil.applyFiltersAndSort(products, filter, Comparator.comparingDouble(Product::price), pageNumber, pageSize);

        assertThat(result.getContent()).hasSize(2);
        assertThat(result.getTotalElements()).isEqualTo(2);
        assertThat(result.getContent())
                .map(Product::name)
                .containsExactly("Laptop", "Phone");
    }

    @Test
    void usingEmptyAndIsTreatedAsNoFilterApplied() {
        List<Product> products =
                List.of(
                        new Product("Laptop", "Electronics", 999.99, 30, Set.of("A")),
                        new Product("Phone", "Electronics", 499.99, 20, Set.of("A")),
                        new Product("Chair", "Furniture", 89.99, 15, Set.of("B")),
                        new Product("Table", "Furniture", 199.99, 5, Set.of("C"))
                );

        CustomFilter filter = new CustomFilter();
        var criteria =
                FilterCriteria.filter()
                        .and()
                        .build();

        filter.setFilter(criteria);

        FilterAndSortInMemoryUtil<Product> filterUtil = new FilterAndSortInMemoryUtil<>();
        int pageNumber = 0;
        int pageSize = 10;
        Page<Product> result = filterUtil.applyFiltersAndSort(products, filter, Comparator.comparingDouble(Product::price), pageNumber, pageSize);

        assertThat(result.getContent()).hasSize(4);
        assertThat(result.getTotalElements()).isEqualTo(4);
        assertThat(result.getContent())
                .map(Product::name)
                .containsExactly("Laptop", "Phone", "Chair", "Table");
    }
}

record Product(String name, String category, double price, int quantity, Set<Object> props) {
}
