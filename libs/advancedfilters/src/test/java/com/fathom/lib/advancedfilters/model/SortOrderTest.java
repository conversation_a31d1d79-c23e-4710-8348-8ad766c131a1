package com.fathom.lib.advancedfilters.model;

import static com.fathom.lib.advancedfilters.model.SortOrder.ASCENDING;
import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

class SortOrderTest {

  @ParameterizedTest
  @ValueSource(strings = {"ASC", "asc", "aSc", "ASc", "Asc", "asC"})
  void shouldBeAbleToDeserializeCaseInsensitive(String val) throws JsonProcessingException {
    SortOrder so = new ObjectMapper().readValue("\"" + val + "\"", SortOrder.class);
    assertThat(so).isEqualTo(ASCENDING);
  }
}
