package com.fathom.lib.common.model.frame;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fathom.lib.common.frn.aware.FrnAware;
import com.fathom.lib.common.jackson.KeyToLowercaseDeserializer;
import com.fathom.lib.common.model.frame.data.PropertyData;
import com.fathom.lib.common.model.frame.meta.PropertyMeta;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Setter;
import org.apache.commons.lang3.SerializationUtils;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class Frame implements FrnAware, Cloneable, Serializable {

  @EqualsAndHashCode.Include @Serial
  private static final long serialVersionUID = 7388041263166476874L;

  @NotNull @EqualsAndHashCode.Include private UUID frameId;

  @NotBlank private String assetName;

  @NotNull private UUID assetId;

  @NotNull private Long assetRevisionNumber;

  @NotNull private UUID orgId;

  @NotNull private Long seq;

  @NotNull private UUID prevFrameId;

  @NotNull private LocalDateTime timestamp;

  @NotNull private LocalDateTime assetCreatedDate;

  @NotNull private LocalDateTime assetEffectiveDate;

  private LocalDateTime modifiedDate;

  @NotNull private UUID tempId;

  @NotNull private Integer tempVer;

  @NotBlank private String ver;

  @NotBlank private String dataSourceName;

  private String frn;

  @Setter(AccessLevel.NONE)
  @JsonDeserialize(keyUsing = KeyToLowercaseDeserializer.class)
  private Map<String, @Valid PropertyMeta> meta;

  @NotEmpty
  @Setter(AccessLevel.NONE)
  @JsonDeserialize(keyUsing = KeyToLowercaseDeserializer.class)
  private Map<String, @Valid PropertyData> data;

  public Frame clone() {
    return SerializationUtils.clone(this);
  }

  public PropertyMeta getPropertyMeta(String propertyName) {
    return meta.get(propertyName.toLowerCase());
  }

  public void setPropertyMeta(String key, PropertyMeta value) {
    meta.put(key.toLowerCase(), value);
  }

  public PropertyData getPropertyData(String propertyName) {
    return data.get(propertyName.toLowerCase());
  }

  public void setPropertyData(String key, PropertyData value) {
    data.put(key.toLowerCase(), value);
  }

  public void setMeta(Map<String, PropertyMeta> meta) {
    this.meta = meta;
  }

  public void setData(Map<String, PropertyData> data) {
    this.data = data;
  }

  @Override
  public String getResourceId() {
    return this.assetName;
  }
}
