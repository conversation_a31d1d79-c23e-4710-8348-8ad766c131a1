package com.fathom.lib.common.model.page;

import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Schema(description = MODEL_COMMON_PAGE_DTO_CLASS)
public class CommonPageDTO<T> extends BasePageDTO<T> implements Serializable {

  @Serial private static final long serialVersionUID = 8057993976236513413L;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_COMMON_PAGE_PAGE_NUMBER_FIELD)
  private Integer pageNumber;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_COMMON_PAGE_PAGES_QUANTITY_FIELD)
  private Integer pagesQuantity;

  public CommonPageDTO(
      List<T> content,
      Integer pageNumber,
      Integer pageSize,
      Integer pagesQuantity,
      Long totalCount) {
    super(content, pageSize, totalCount);
    this.pageNumber = pageNumber;
    this.pagesQuantity = pagesQuantity;
  }
}
