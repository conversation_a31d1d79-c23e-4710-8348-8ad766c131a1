package com.fathom.lib.common.model.asset;

import static com.fathom.lib.common.exception.ExMessage.*;
import static com.fathom.lib.common.model.Constants.REG_EXP_FOR_ASSET_STATUS_CONFIG_NAME_VALIDATION;
import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import com.fathom.lib.common.model.validation.group.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
public class AssetStatusConfigDTO extends BaseDTO implements Serializable {

  @Serial private static final long serialVersionUID = -8090991157050087028L;

  @Schema(description = MODEL_ASSET_STATUS_CONFIG_NAME_FIELD)
  @NotBlank(
      groups = {Create.class},
      message = BLANK_NAME_FIELD)
  @Pattern(
      groups = {Create.class, Update.class},
      regexp = REG_EXP_FOR_ASSET_STATUS_CONFIG_NAME_VALIDATION,
      message = NAME_CONSTRAINTS_VIOLATION)
  private String name;

  @Schema(description = MODEL_ASSET_STATUS_CONFIG_COLOR_FIELD)
  @NotBlank(
      groups = {Create.class},
      message = BLANK_COLOUR_FIELD)
  private String color;

  @Schema(description = MODEL_ASSET_STATUS_CONFIG_ICON_ID_FIELD)
  private String iconImageId;

  @Schema(description = MODEL_ASSET_STATUS_CONFIG_MAP_MARKER_IMAGE_ID_FIELD)
  private String mapMarkerImageId;

  @Schema(description = MODEL_ASSET_STATUS_CONFIG_ASSET_TYPE_NAMES_FIELD)
  private List<String> assetTypeNames;

  @Schema(description = MODEL_ASSET_STATUS_CONFIG_ASSET_TYPE_FIELD)
  private List<AssetTypeDTO> assetTypes;

  @Override
  public String getResourceId() {
    return this.name;
  }
}
