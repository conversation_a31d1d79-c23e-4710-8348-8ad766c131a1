package com.fathom.lib.common.jackson;

import static com.fathom.lib.common.jackson.FrameSerializationUtil.*;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fathom.lib.common.model.frame.data.PropertyData;
import com.fathom.lib.common.model.frame.data.Quality;
import com.fathom.lib.common.model.frame.data.type.Value;
import java.io.IOException;
import java.io.Serial;
import java.time.LocalDateTime;
import java.util.Objects;

public class PropertyDataDeserializer extends StdDeserializer<PropertyData> {

  @Serial private static final long serialVersionUID = -8928702508514116770L;

  public static final String PROPERTY_DATA_TIMESTAMP_FIELD = "timestamp";
  public static final String PROPERTY_DATA_QUALITY_FIELD = "quality";
  public static final String PROPERTY_DATA_VALUE_FIELD = "value";
  public static final String PROPERTY_DATA_UNIT_FIELD = "unit";
  public static final String PROPERTY_DATA_META_ALIAS_FIELD = "metaAlias";
  public static final String PROPERTY_DATA_STR_ERROR_FIELD = "strError";
  public static final String PROPERTY_DATA_FRN_FIELD = "frn";

  public PropertyDataDeserializer() {
    this(null);
  }

  public PropertyDataDeserializer(Class<PropertyData> t) {
    super(t);
  }

  @Override
  public PropertyData deserialize(
      JsonParser jsonParser, DeserializationContext deserializationContext)
      throws IOException, JsonProcessingException {
    PropertyData propertyData = new PropertyData();
    JsonNode rootNode = jsonParser.getCodec().readTree(jsonParser);

    JsonNode timestampNode = rootNode.get(PROPERTY_DATA_TIMESTAMP_FIELD);
    JsonNode qualityNode = rootNode.get(PROPERTY_DATA_QUALITY_FIELD);
    JsonNode valueNode = rootNode.get(PROPERTY_DATA_VALUE_FIELD);
    JsonNode unitNode = rootNode.get(PROPERTY_DATA_UNIT_FIELD);
    JsonNode metaAliasNode = rootNode.get(PROPERTY_DATA_META_ALIAS_FIELD);
    JsonNode strErrorNode = rootNode.get(PROPERTY_DATA_STR_ERROR_FIELD);
    JsonNode frn = rootNode.get(PROPERTY_DATA_FRN_FIELD);

    if (Objects.nonNull(timestampNode)) {
      propertyData.setTimestamp(
          MapperSingleton.getMapper()
              .readValue(timestampNode.toString(), new TypeReference<LocalDateTime>() {}));
    }

    if (Objects.nonNull(qualityNode)) {
      propertyData.setQuality(Quality.fromString(qualityNode.textValue()));
    }

    if (Objects.nonNull(valueNode)) {
      Value value = parseMetaAlias(metaAliasNode.textValue());
      populateValues(valueNode, value);
      propertyData.setValue(value);
    }

    if (Objects.nonNull(unitNode)) {
      propertyData.setUnit(unitNode);

      Value value = propertyData.getValue();
      if (value != null) {
        populateUnits(unitNode, propertyData.getValue());
      }
    }

    if (Objects.nonNull(metaAliasNode)) {
      propertyData.setMetaAlias(metaAliasNode.textValue());
    }

    if (Objects.nonNull(strErrorNode)) {
      propertyData.setStrError(strErrorNode.textValue());
    }

    if (Objects.nonNull(frn)) {
      propertyData.setFrn(frn.textValue());
    }

    return propertyData;
  }
}
