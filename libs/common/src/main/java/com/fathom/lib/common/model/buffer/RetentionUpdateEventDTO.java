package com.fathom.lib.common.model.buffer;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RetentionUpdateEventDTO implements Serializable {

  @Serial private static final long serialVersionUID = 9104282757114340896L;

  private String assetID;

  private Integer bufferingTopicRetentionTime;

  private Integer bufferingTopicRetentionSize;
}
