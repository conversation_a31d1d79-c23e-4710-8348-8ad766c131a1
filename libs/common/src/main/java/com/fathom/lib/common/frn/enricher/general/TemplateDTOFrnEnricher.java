package com.fathom.lib.common.frn.enricher.general;

import com.fathom.lib.common.frn.ResourceType;
import com.fathom.lib.common.frn.ServiceName;
import com.fathom.lib.common.frn.enricher.NestedFrnEnricher;
import com.fathom.lib.common.model.asset.template.TemplateDTO;
import com.fathom.lib.common.model.frame.meta.PropertyMeta;
import jakarta.validation.Valid;
import java.util.Map;

public class TemplateDTOFrnEnricher extends NestedFrnEnricher<TemplateDTO> {

  private final PropertyMetaFrnEnricher propertyMetaFrnEnricher;

  public static TemplateDTOFrnEnricher build(String serviceName, String rootResourceName) {
    return new TemplateDTOFrnEnricher(serviceName, rootResourceName);
  }

  public static TemplateDTOFrnEnricher build(
      ServiceName serviceName, ResourceType rootResourceType) {
    return build(serviceName.getValue(), rootResourceType.getValue());
  }

  public static TemplateDTOFrnEnricher build(String serviceName) {
    return build(serviceName, ResourceType.TEMPLATE.getValue());
  }

  public TemplateDTOFrnEnricher(String serviceName, String rootResourceName) {
    super(serviceName, rootResourceName);
    this.propertyMetaFrnEnricher = PropertyMetaFrnEnricher.build();
  }

  @Override
  protected void setNestedFrn(TemplateDTO target, String rootNodeFrn) {
    Map<String, @Valid PropertyMeta> templateMeta = target.getMeta();
    if (templateMeta != null) {
      templateMeta.values().forEach(meta -> propertyMetaFrnEnricher.setFrn(meta, rootNodeFrn));
    }
  }
}
