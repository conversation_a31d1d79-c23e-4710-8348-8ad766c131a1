package com.fathom.lib.common.jackson;

import static com.fathom.lib.common.jackson.DatePatterns.LOCAL_DATE_FORMAT;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import java.io.IOException;
import java.io.Serial;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class LocalDateDeserializer extends StdDeserializer<LocalDate> {

  @Serial private static final long serialVersionUID = 2344543277859884057L;

  private static final List<DateTimeFormatter> formatters = new ArrayList<>();

  static {
    formatters.add(DateTimeFormatter.ofPattern(LOCAL_DATE_FORMAT));
  }

  public LocalDateDeserializer() {
    super(LocalDateTime.class);
  }

  @Override
  public LocalDate deserialize(JsonParser jsonParser, DeserializationContext deserializationContext)
      throws IOException, JsonProcessingException {
    String localDateString = jsonParser.getText();

    if (Objects.nonNull(localDateString) && !localDateString.trim().isEmpty()) {
      LocalDate parsedLocalDateTime = null;

      for (DateTimeFormatter formatter : formatters) {
        try {
          parsedLocalDateTime = LocalDate.parse(localDateString, formatter);
        } catch (DateTimeParseException ex) {
          // If can`t parse with current formatter just skip and go to the next iteration.
        }
      }

      if (Objects.isNull(parsedLocalDateTime)) {
        throw new IllegalArgumentException(
            "Can`t parse date '%s'. Unknown format".formatted(localDateString));
      }

      return parsedLocalDateTime;
    } else {
      throw new IllegalArgumentException("Date value is null, or empty string.");
    }
  }
}
