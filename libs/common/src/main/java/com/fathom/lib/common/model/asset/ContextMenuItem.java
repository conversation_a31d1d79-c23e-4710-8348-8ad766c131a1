package com.fathom.lib.common.model.asset;

import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContextMenuItem implements Serializable {

  @Serial private static final long serialVersionUID = 5752581643066790744L;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_CONTEXT_MENU_ITEM_TEXT_FIELD)
  @NotBlank(groups = {Create.class})
  private String text;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_CONTEXT_MENU_ITEM_ICON_CLASS_FIELD)
  private String iconClass;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_CONTEXT_MENU_ITEM_ICON_PATH_FIELD)
  private String iconPath;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_CONTEXT_MENU_ITEM_ROUTE_PATH_FIELD)
  private String routePath;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_CONTEXT_MENU_ITEM_HREF_FIELD)
  private String href;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_CONTEXT_MENU_ITEM_FRAGMENT_FIELD)
  private String fragment;
}
