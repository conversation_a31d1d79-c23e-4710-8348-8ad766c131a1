package com.fathom.lib.common.frn.enricher;

import com.fathom.lib.common.frn.ResourcePath;
import com.fathom.lib.common.frn.ResourceType;
import com.fathom.lib.common.frn.aware.FrnPropertyAware;
import java.util.Map;

public class NestedMapFrnEnricher<T extends FrnPropertyAware> {

  private final String resourceName;

  public static <T extends FrnPropertyAware> NestedMapFrnEnricher<T> build(String resourceName) {
    return new NestedMapFrnEnricher<>(resourceName);
  }

  public static <T extends FrnPropertyAware> NestedMapFrnEnricher<T> build(
      ResourceType resourceName) {
    return build(resourceName.getValue());
  }

  protected NestedMapFrnEnricher(String resourceName) {
    this.resourceName = resourceName;
  }

  public void setFrn(Map<String, T> targetMap, String rootFrn) {
    if (targetMap != null) {
      targetMap.forEach(
          (propertyName, data) -> {
            ResourcePath propertyPath = new ResourcePath(resourceName, propertyName);
            data.setFrn(rootFrn + propertyPath.toString());
          });
    }
  }
}
