package com.fathom.lib.common.model.formula;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OperatorNode extends BaseNode implements Serializable {

  @Serial private static final long serialVersionUID = -5868463961698425202L;

  @NotNull(groups = {Create.class})
  private OperatorTypes operatorType;

  @NotEmpty(groups = {Create.class})
  private List<Long> operandNodeIds;

  @NotNull(groups = {Create.class})
  private Integer operandsCount;

  public OperatorNode() {
    super(NodeTypes.OPERATOR);
  }

  public OperatorNode(
      Long id,
      Double xAxisPosition,
      Double yAxisPosition,
      OperatorTypes operatorType,
      List<Long> operandNodeIds,
      Integer operandsCount) {
    super(NodeTypes.OPERATOR, id, xAxisPosition, yAxisPosition);
    this.operatorType = operatorType;
    this.operandNodeIds = operandNodeIds;
    this.operandsCount = operandsCount;
  }
}
