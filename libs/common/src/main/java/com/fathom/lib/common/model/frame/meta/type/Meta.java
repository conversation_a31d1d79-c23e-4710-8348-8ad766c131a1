package com.fathom.lib.common.model.frame.meta.type;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = As.EXISTING_PROPERTY,
    property = "metaType",
    visible = true)
@JsonSubTypes({
  @JsonSubTypes.Type(name = "ArrayMeta", value = ArrayMeta.class),
  @JsonSubTypes.Type(name = "TupleMeta", value = TupleMeta.class),
  @JsonSubTypes.Type(name = "ValueMeta", value = ValueMeta.class)
})
public abstract class Meta implements Serializable {

  @Serial private static final long serialVersionUID = 8951202852677354392L;

  @NotNull private MetaType metaType;

  @NotBlank private String metaPath;

  private String description;

  private SourceType sourceType;
}
