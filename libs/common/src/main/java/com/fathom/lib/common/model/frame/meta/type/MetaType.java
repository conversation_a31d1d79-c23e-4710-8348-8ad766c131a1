package com.fathom.lib.common.model.frame.meta.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum MetaType implements Serializable {
  VALUE_META("ValueMeta"),
  ARRAY_META("ArrayMeta"),
  TUPLE_META("TupleMeta");

  private static final Map<String, MetaType> VALUES_MAP =
      Stream.of(MetaType.values())
          .collect(Collectors.toMap(s -> s.value.toLowerCase(), Function.identity()));

  private final String value;

  MetaType(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @JsonCreator
  public static MetaType fromString(String s) {
    return Optional.ofNullable(VALUES_MAP.get(s.trim().toLowerCase()))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    "%s is invalid 'MetaType' value. Valid values are : %s. (Case insensitive)"
                        .formatted(s, VALUES_MAP.values())));
  }
}
