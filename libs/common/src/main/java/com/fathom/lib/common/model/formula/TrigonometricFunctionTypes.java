package com.fathom.lib.common.model.formula;

import static com.fathom.lib.common.exception.ExMessage.NONEXISTENT_ENUM_VALUE;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum TrigonometricFunctionTypes implements Serializable {
  SIN("sin"),
  COS("cos"),
  TAN("tan"),
  ARC_SIN("asin"),
  ARC_COS("acos"),
  ARC_TAN("atan"),
  ARC_SIN_H("asinh"),
  ARC_COS_H("acosh"),
  ARC_TAN_H("atanh"),
  SIN_H("sinh"),
  COS_H("cosh"),
  TAN_H("tanh");

  private static final Map<String, TrigonometricFunctionTypes> VALUES_MAP =
      Stream.of(TrigonometricFunctionTypes.values())
          .collect(Collectors.toMap(s -> s.value, Function.identity()));

  private final String value;

  TrigonometricFunctionTypes(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @JsonCreator
  public static TrigonometricFunctionTypes fromString(String s) {
    return Optional.ofNullable(VALUES_MAP.get(s))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    NONEXISTENT_ENUM_VALUE.formatted(
                        s, "TrigonometricFunctionTypes", VALUES_MAP.values())));
  }
}
