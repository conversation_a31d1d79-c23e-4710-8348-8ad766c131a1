package com.fathom.lib.common.model.asset.template;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class CatalogSearchParameter implements Serializable {

  @NotBlank(groups = {Create.class})
  private String propertyId;

  @NotBlank(groups = {Create.class})
  private String propertyName;

  private String propertyAlias;
}
