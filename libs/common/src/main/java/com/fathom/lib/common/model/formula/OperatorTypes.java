package com.fathom.lib.common.model.formula;

import static com.fathom.lib.common.exception.ExMessage.NONEXISTENT_ENUM_VALUE;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum OperatorTypes implements Serializable {
  ADDITION("+"),
  SUBTRACTION("-"),
  MULTIPLICATION("*"),
  DIVISION("/");

  private static final Map<String, OperatorTypes> VALUES_MAP =
      Stream.of(OperatorTypes.values())
          .collect(Collectors.toMap(s -> s.value, Function.identity()));

  private final String value;

  OperatorTypes(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @JsonCreator
  public static OperatorTypes fromString(String s) {
    return Optional.ofNullable(VALUES_MAP.get(s))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    NONEXISTENT_ENUM_VALUE.formatted(s, "OperatorTypes", VALUES_MAP.values())));
  }
}
