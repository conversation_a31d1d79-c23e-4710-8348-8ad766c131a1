package com.fathom.lib.common.model.frame.data.type;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.SerializationUtils;

@Data
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class Value implements Cloneable, Serializable {

  @Serial private static final long serialVersionUID = -5221478874582901144L;

  @NotNull private ValueType valueType;

  public Value clone() {
    return SerializationUtils.clone(this);
  }
}
