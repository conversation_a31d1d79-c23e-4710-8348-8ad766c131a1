package com.fathom.lib.common.jackson;

import static com.fathom.lib.common.exception.ExMessage.*;
import static com.fathom.lib.common.jackson.PropertyDataDeserializer.PROPERTY_DATA_UNIT_FIELD;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.fathom.lib.common.exception.CommonLibException;
import com.fathom.lib.common.model.frame.data.type.*;
import com.fathom.lib.common.model.frame.meta.type.*;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public final class FrameSerializationUtil {

  private FrameSerializationUtil() {}

  /**
   * Method for generation 'metaAlias' string, based on {@link
   * com.fathom.lib.common.model.frame.meta.type.Meta} object.
   *
   * @param meta 'Meta' object for 'metaAlias' string creation
   * @return 'metaAlias' string
   */
  public static String generateMetaAlias(final Meta meta) {
    return getMetaDescriptiveValue(meta, m -> m.getContentType().getValue());
  }

  /**
   * Method for generation 'metaDescription' string, based on {@link
   * com.fathom.lib.common.model.frame.meta.type.Meta} object. Please note, method returns value in
   * double quotes
   *
   * @param meta 'Meta' object for 'description' string creation
   * @return 'metaAlias' string or null
   */
  public static String generateNestedMetaDescription(final Meta meta) {
    return getMetaDescriptiveValue(meta, m -> '"' + m.getDescription() + '"');
  }

  // Traverses meta recursively, wraps array value into [], tuple values into {}
  // takes func that defines what data from ValueMeta must be extracted
  private static String getMetaDescriptiveValue(
      final Meta meta, Function<ValueMeta, String> metaValueFunc) {
    if (MetaType.VALUE_META.equals(meta.getMetaType())) {
      ValueMeta valueMeta = (ValueMeta) meta;
      return metaValueFunc.apply(valueMeta);
    } else if (MetaType.ARRAY_META.equals(meta.getMetaType())) {
      ArrayMeta arrayMeta = (ArrayMeta) meta;
      return ArrayType.ARRAY.prefix
          + getMetaDescriptiveValue(arrayMeta.getMeta(), metaValueFunc)
          + ArrayType.ARRAY.suffix;
    } else if (MetaType.TUPLE_META.equals(meta.getMetaType())) {
      TupleMeta tupleMeta = (TupleMeta) meta;
      List<Meta> metas = tupleMeta.getMetas();

      StringBuilder sb = new StringBuilder();

      sb.append(ArrayType.TUPLE.prefix);

      for (int i = 0; i < metas.size(); i++) {
        sb.append(getMetaDescriptiveValue(metas.get(i), metaValueFunc));

        if (i < metas.size() - 1) {
          sb.append(", ");
        }
      }

      sb.append(ArrayType.TUPLE.suffix);

      return sb.toString();
    }

    return null;
  }

  /**
   * Method for creation {@link com.fasterxml.jackson.databind.JsonNode} object to fill 'unit'
   * property in {@link com.fathom.lib.common.model.frame.data.PropertyData} with converted units.
   *
   * @param meta {@link com.fathom.lib.common.model.frame.meta.type.Meta} object that will be
   *     converted to unit 'JsonNode' object.
   * @return 'JsonNode' object with units.
   */
  public static JsonNode convertToUnit(final Meta meta) {
    if (MetaType.VALUE_META.equals(meta.getMetaType())) {
      String unit = ((ValueMeta) meta).getUnit();

      return new TextNode(unit);
    } else if (MetaType.ARRAY_META.equals(meta.getMetaType())) {
      ArrayNode arrayNode = MapperSingleton.getMapper().createArrayNode();

      Meta arrayMetaValue = ((ArrayMeta) meta).getMeta();

      return arrayNode.add(convertToUnit(arrayMetaValue));
    } else if (MetaType.TUPLE_META.equals(meta.getMetaType())) {
      ArrayNode arrayNode = MapperSingleton.getMapper().createArrayNode();

      TupleMeta tupleMetaValue = (TupleMeta) meta;

      for (Meta element : tupleMetaValue.getMetas()) {
        arrayNode.add(convertToUnit(element));
      }

      return arrayNode;
    }

    return null;
  }

  /**
   * Method for creation {@link com.fasterxml.jackson.databind.JsonNode} object to fill 'unit'
   * property in {@link com.fathom.lib.common.model.frame.data.PropertyData} with converted units.
   *
   * @param value {@link com.fathom.lib.common.model.frame.data.type.Value} object that will be
   *     converted to unit 'JsonNode' object.
   * @param withType this parameter indicates whether to return the unit type and value or only
   *     value.
   * @return 'JsonNode' object with units, {@code null} if {@code value} is type of {@link
   *     com.fathom.lib.common.model.frame.data.type.ArrayValue} and it's values is empty, or if
   *     {@link com.fathom.lib.common.model.frame.data.type.Value#getValueType()} cannot be resolved
   *     properly.
   * @throws NullPointerException if {@code value} is {@code null}.
   * @deprecated use {@link #convertToUnit(com.fathom.lib.common.model.frame.data.type.Value,
   *     com.fasterxml.jackson.databind.JsonNode, boolean)} method that is safe and return default
   *     unit instead of {@code null}.
   */
  @Deprecated
  public static JsonNode convertToUnit(final Value value, final boolean withType) {
    Objects.requireNonNull(value, "Value object with is null.");

    if (ValueType.SINGLE_VALUE.equals(value.getValueType())) {
      String unit = ((SingleValue) value).getUnit();

      if (!withType) {
        return new TextNode(unit.split(":")[1]);
      }

      return new TextNode(unit);
    } else if (ValueType.ARRAY_VALUE.equals(value.getValueType())) {
      ArrayNode arrayNode = MapperSingleton.getMapper().createArrayNode();

      List<Value> arrayValue = ((ArrayValue) value).getValue();
      if (arrayValue == null || arrayValue.isEmpty()) {
        return null;
      }

      return arrayNode.add(convertToUnit(arrayValue.get(0), withType));
    } else if (ValueType.TUPLE_VALUE.equals(value.getValueType())) {
      ArrayNode arrayNode = MapperSingleton.getMapper().createArrayNode();

      TupleValue tupleValue = (TupleValue) value;

      for (Value element : tupleValue.getValue()) {
        arrayNode.add(convertToUnit(element, withType));
      }

      return arrayNode;
    }

    return null;
  }

  /**
   * Method for creation {@link com.fasterxml.jackson.databind.JsonNode} object to fill 'unit'
   * property in {@link com.fathom.lib.common.model.frame.data.PropertyData} with converted units.
   *
   * @param value {@link com.fathom.lib.common.model.frame.data.type.Value} object that will be
   *     converted to unit 'JsonNode' object.
   * @param withType this parameter indicates whether to return the unit type and value or only
   *     value.
   * @return 'JsonNode' object with units, {@code null} if {@code value} is type of {@link
   *     com.fathom.lib.common.model.frame.data.type.ArrayValue} and it's values is empty, or if
   *     {@link com.fathom.lib.common.model.frame.data.type.Value#getValueType()} cannot be resolved
   *     properly.
   * @param valueUnit original value's unit. If during the errors {@code value}'s unit cannot be
   *     converted, then {@code valueUnit} will be return.
   * @throws NullPointerException if {@code value} is {@code null}.
   */
  public static JsonNode convertToUnit(
      final Value value, final JsonNode valueUnit, final boolean withType) {
    Objects.requireNonNull(value, "Value object with is null.");

    if (ValueType.SINGLE_VALUE.equals(value.getValueType())) {
      return convertSingleValueToUnit((SingleValue) value, valueUnit, withType);
    } else if (ValueType.ARRAY_VALUE.equals(value.getValueType())) {
      return convertArrayValueToUnit((ArrayValue) value, valueUnit, withType);
    } else if (ValueType.TUPLE_VALUE.equals(value.getValueType())) {
      return convertTupleValueToUnit((TupleValue) value, valueUnit, withType);
    }
    return null;
  }

  private static JsonNode convertSingleValueToUnit(
      SingleValue value, JsonNode originalUnit, boolean withType) {
    String unit = value.getUnit();
    if (unit == null || unit.isEmpty()) {
      if (!originalUnit.isTextual()) {
        throw new CommonLibException(
            "Invalid default unit type: expected '%s' but actual is '%s'"
                .formatted(TextNode.class.getName(), originalUnit.getClass().getName()));
      }
      return originalUnit;
    }

    return !withType ? new TextNode(unit.split(":")[1]) : new TextNode(unit);
  }

  private static JsonNode convertArrayValueToUnit(
      ArrayValue value, JsonNode originalUnit, boolean withType) {
    if (!originalUnit.isArray()) {
      throw new CommonLibException(
          "Invalid default unit type: expected '%s' but actual is '%s'"
              .formatted(ArrayNode.class.getName(), originalUnit.getClass().getName()));
    }

    List<Value> arrayValue = value.getValue();
    if (arrayValue == null || arrayValue.isEmpty()) {
      return originalUnit;
    }

    ArrayNode arrayNode = MapperSingleton.getMapper().createArrayNode();
    return arrayNode.add(convertToUnit(arrayValue.get(0), originalUnit.get(0), withType));
  }

  private static JsonNode convertTupleValueToUnit(
      TupleValue value, JsonNode originalUnit, boolean withType) {
    if (!originalUnit.isArray()) {
      throw new CommonLibException(
          "Invalid default unit type: expected '%s' but actual is '%s'"
              .formatted(ArrayNode.class.getName(), originalUnit.getClass().getName()));
    }

    ArrayNode arrayNode = MapperSingleton.getMapper().createArrayNode();

    List<Value> values = value.getValue();
    if (values.size() != originalUnit.size()) {
      throw new CommonLibException(
          "Invalid unit %s, unit should match tuple size".formatted(originalUnit));
    }
    for (int i = 0; i < values.size(); i++) {
      JsonNode unit = originalUnit.get(i);
      Value el = values.get(i);
      arrayNode.add(convertToUnit(el, unit, withType));
    }

    return arrayNode;
  }

  /**
   * Method for creation {@link com.fathom.lib.common.model.frame.data.type.Value} object to fill
   * 'value' property in {@link com.fathom.lib.common.model.frame.data.PropertyData} with default
   * values from meta.
   *
   * @param meta {@link com.fathom.lib.common.model.frame.meta.type.Meta} object that will be
   *     converted to value object.
   * @return 'Value' object with default values.
   */
  public static Value convertToDefaultValue(final Meta meta) {
    if (MetaType.VALUE_META.equals(meta.getMetaType())) {
      Object defaultValue = ((ValueMeta) meta).getDefaultValue();

      return new SingleValue(defaultValue);
    } else if (MetaType.ARRAY_META.equals(meta.getMetaType())) {
      ArrayMeta arrayMeta = (ArrayMeta) meta;

      Value defaultValue = convertToDefaultValue(arrayMeta.getMeta());

      List<Value> arrayValues = new ArrayList<>();

      if (arrayMeta.getLength() == 0) {
        arrayValues.add(defaultValue);
      } else if (arrayMeta.getLength() > 0) {
        for (int i = 0; i < arrayMeta.getLength(); i++) {
          arrayValues.add(defaultValue);
        }
      }

      return new ArrayValue(arrayValues);
    } else if (MetaType.TUPLE_META.equals(meta.getMetaType())) {
      TupleMeta tupleMeta = (TupleMeta) meta;

      List<Value> tupleValues = new ArrayList<>();

      for (Meta element : tupleMeta.getMetas()) {
        tupleValues.add(convertToDefaultValue(element));
      }

      return new TupleValue(tupleValues);
    }

    return null;
  }

  /**
   * This method reading 'value' field from json, and with its help fills {@link
   * com.fathom.lib.common.model.frame.data.type.Value} model created by {@link
   * FrameSerializationUtil#parseMetaAlias} method.
   *
   * @param jsonNode {@link com.fasterxml.jackson.databind.JsonNode} representation of 'value' field
   *     from json.
   * @param valueTemplate 'Value' model created by 'parseMetaAlias' method.
   */
  public static void populateValues(final JsonNode jsonNode, final Value valueTemplate) {
    // Checks if provided more than one value type for single array value
    populateInitialValues(jsonNode, valueTemplate, arrayValue -> arrayValue.getValue().size() > 1);
  }

  /**
   * The same as {@link #populateValues(JsonNode, Value)} populateValues} method, but allows to
   * populate array with multiple initial values, does not require array to be initialized only with
   * one initial value.
   */
  public static void populateValuesNonInitArray(
      final JsonNode jsonNode, final Value valueTemplate) {
    populateInitialValues(jsonNode, valueTemplate, a -> false);
  }

  private static void populateInitialValues(
      JsonNode jsonNode, Value valueTemplate, Predicate<ArrayValue> arrayValidationPredicate) {
    Objects.requireNonNull(jsonNode, "JsonNode object with values is null.");
    Objects.requireNonNull(valueTemplate, "Value object with value template is null.");

    if (ValueType.SINGLE_VALUE.equals(valueTemplate.getValueType())) {
      SingleValue value = (SingleValue) valueTemplate;

      if (ContentType.BOOLEAN.equals(value.getType())) {
        value.setValue(jsonNode.asBoolean());
      } else if (ContentType.NUMBER.equals(value.getType())) {
        value.setValue(jsonNode.asDouble());
      } else if (ContentType.STRING.equals(value.getType())) {
        value.setValue(jsonNode.asText());
      } else if (ContentType.DATE_TIME.equals(value.getType())) {
        value.setValue(jsonNode.asText());
      } else {
        throw new CommonLibException(
            DATA_AND_VALUE_TEMPLATE_MISMATCH,
            ValueType.SINGLE_VALUE.getValue(),
            jsonNode.textValue(),
            valueTemplate.toString());
      }
    } else if (ValueType.ARRAY_VALUE.equals(valueTemplate.getValueType())) {
      ArrayValue arrayValue = ((ArrayValue) valueTemplate);
      List<Value> values = arrayValue.getValue();

      // If value template have same value type but different size we copying this model for each
      // value
      if (jsonNode.size() > 1 && values.size() == 1) {
        for (int i = 0; i < jsonNode.size() - 1; i++) {
          values.add(values.get(0).clone());
        }
      } else if (arrayValidationPredicate.test(arrayValue)) {
        // If provided more than one value type for single array value
        throw new CommonLibException(INVALID_ARRAY_DEFINITION);
      }

      // If size is the same we simply populating values
      if (jsonNode.size() == values.size()) {
        for (int i = 0; i < jsonNode.size(); i++) {
          populateInitialValues(jsonNode.get(i), values.get(i), arrayValidationPredicate);
        }
      } else if (jsonNode.size() == 0) {
        arrayValue.setValue(new ArrayList<>());
      } else {
        throw new CommonLibException(
            DATA_AND_VALUE_TEMPLATE_MISMATCH,
            ValueType.ARRAY_VALUE.getValue(),
            jsonNode.textValue(),
            valueTemplate.toString());
      }
    } else if (ValueType.TUPLE_VALUE.equals(valueTemplate.getValueType())) {
      List<Value> values = ((TupleValue) valueTemplate).getValue();

      // If value template have same value type but different size we copying this model for each
      // value
      if (jsonNode.size() > 1 && values.size() == 1) {
        for (int i = 0; i < jsonNode.size() - 1; i++) {
          values.add(values.get(0).clone());
        }
      }

      // If size is the same we simply populating values
      if (jsonNode.size() == values.size()) {
        for (int i = 0; i < jsonNode.size(); i++) {
          populateInitialValues(jsonNode.get(i), values.get(i), arrayValidationPredicate);
        }
      } else {
        throw new CommonLibException(
            DATA_AND_VALUE_TEMPLATE_MISMATCH,
            ValueType.TUPLE_VALUE.getValue(),
            jsonNode.textValue(),
            valueTemplate.toString());
      }
    } else {
      throw new CommonLibException(NOT_EXISTENT_VALUE_TYPE, valueTemplate.toString());
    }
  }

  /**
   * This method reading 'unit' field from json, and with its help fills {@link
   * com.fathom.lib.common.model.frame.data.type.Value} model created by {@link
   * FrameSerializationUtil#parseMetaAlias} and {@link FrameSerializationUtil#populateValues}
   * methods.
   *
   * @param jsonNode {@link com.fasterxml.jackson.databind.JsonNode} representation of 'unit' field
   *     from json.
   * @param valueTemplate 'Value' model.
   * @return 'Value' model.
   */
  public static void populateUnits(final JsonNode jsonNode, final Value valueTemplate) {
    Objects.requireNonNull(jsonNode, "JsonNode object with units is null.");
    Objects.requireNonNull(valueTemplate, "Value object with values structure is null.");

    if (ValueType.SINGLE_VALUE.equals(valueTemplate.getValueType()) && !jsonNode.isArray()) {
      SingleValue value = (SingleValue) valueTemplate;
      value.setUnit(jsonNode.textValue());
    } else if (ValueType.ARRAY_VALUE.equals(valueTemplate.getValueType()) && jsonNode.isArray()) {
      List<Value> values = ((ArrayValue) valueTemplate).getValue();

      if (jsonNode.size() == 1 && values.size() >= 1) {
        for (Value value : values) {
          populateUnits(jsonNode.get(0), value);
        }
      } else if (jsonNode.size() > 1) {
        // If provided more than one unit type for single array value
        throw new CommonLibException(INVALID_ARRAY_DEFINITION);
      }
    } else if (ValueType.TUPLE_VALUE.equals(valueTemplate.getValueType()) && jsonNode.isArray()) {
      List<Value> values = ((TupleValue) valueTemplate).getValue();

      if (jsonNode.size() == 1 && values.size() > 1) {
        for (Value value : values) {
          populateUnits(jsonNode.get(0), value);
        }
      } else if (jsonNode.size() == values.size()) {
        for (int i = 0; i < jsonNode.size(); i++) {
          populateUnits(jsonNode.get(i), values.get(i));
        }
      } else {
        throw new CommonLibException(
            DATA_AND_VALUE_TEMPLATE_MISMATCH,
            PROPERTY_DATA_UNIT_FIELD,
            jsonNode.textValue(),
            valueTemplate.toString());
      }
    }
  }

  /**
   * This method creating {@link com.fathom.lib.common.model.frame.data.type.Value} object with
   * structure specified in {@link com.fathom.lib.common.model.frame.meta.type.Meta}, and empty
   * value and unit properties.
   *
   * @param meta 'Meta' object for value template creation
   * @return created 'Value' object
   */
  static Value createValueFromMeta(final Meta meta) {
    if (MetaType.VALUE_META.equals(meta.getMetaType())) {
      ValueMeta valueMeta = (ValueMeta) meta;

      SingleValue singleValue = new SingleValue();
      singleValue.setType(valueMeta.getContentType());

      return singleValue;
    } else if (MetaType.ARRAY_META.equals(meta.getMetaType())) {
      ArrayMeta arrayMeta = (ArrayMeta) meta;
      Value value = createValueFromMeta(arrayMeta.getMeta());
      List<Value> values = new ArrayList<>();
      values.add(value);
      return new ArrayValue(values);
    } else if (MetaType.TUPLE_META.equals(meta.getMetaType())) {
      TupleMeta tupleMeta = (TupleMeta) meta;

      List<Value> values = new ArrayList<>();

      for (Meta element : tupleMeta.getMetas()) {
        values.add(createValueFromMeta(element));
      }

      return new TupleValue(values);
    }

    return null;
  }

  /**
   * Method for parsing 'metaAlias' field and creation value structure based on that. Working
   * recursively.
   *
   * @param value metaAlias value.
   * @return {@link com.fathom.lib.common.model.frame.data.type.Value} representation of 'metaAlias'
   *     string.
   */
  public static Value parseMetaAlias(final String value) {
    Objects.requireNonNull(value, "Value can`t be null");

    if (isSingleValue(value)) {
      return parseSingle(value);
    } else if (checkArrayType(value, ArrayType.ARRAY)) {
      List<Value> values = new ArrayList<>();

      for (String entry : parseMetaAliasValues(value, ',')) {
        values.add(parseMetaAlias(entry));
      }

      return new ArrayValue(values);
    } else if (checkArrayType(value, ArrayType.TUPLE)) {
      List<Value> values = new ArrayList<>();

      for (String entry : parseMetaAliasValues(value, ',')) {
        values.add(parseMetaAlias(entry));
      }

      return new TupleValue(values);
    } else {
      throw new CommonLibException(NOT_VALID_META_ALIAS_STRUCTURE);
    }
  }

  /**
   * This method contains logic for checking is specified string {@link
   * com.fathom.lib.common.model.frame.data.type.SingleValue} or not. Make decision based on {@link
   * com.fathom.lib.common.model.frame.meta.type.ContentType} enum.
   *
   * @param value string for check.
   * @return result of check.
   */
  private static boolean isSingleValue(final String value) {
    String trimmedString = value.trim();

    for (ContentType entry : ContentType.values()) {
      if (entry.getValue().equals(trimmedString)) {
        return true;
      }
    }

    return false;
  }

  /**
   * This method creating {@link com.fathom.lib.common.model.frame.data.type.SingleValue} object
   * based on specified string value. Also setting type of value. All valid 'SingleValue' types
   * specified in 'SingleType'.
   *
   * @param singleString String representing 'SingleValue'.
   * @return 'SingleValue' object representation of string.
   */
  private static Value parseSingle(final String singleString) {
    SingleValue singleValue = new SingleValue();
    singleValue.setType(ContentType.fromString(singleString));
    return singleValue;
  }

  /**
   * This method defines type of array based on brackets type.
   *
   * @param value String representing array of values with brackets.
   * @param arrayType Type of array we checking now.
   * @return Boolean whether the array belongs to this type or not.
   */
  private static boolean checkArrayType(final String value, final ArrayType arrayType) {
    return value.trim().matches(arrayType.regex);
  }

  /**
   * This method parsing metaAlias elements, on the same level of nesting tree. For example method
   * call with arguments {{int, double, int},{double, int, double}} and separator ',' will return
   * list with two string elements {int, double, int} and {double, int, double}. Next call on {int,
   * double, int} will return list with three string elements int, double and int.
   *
   * <p>This method alternately called for each of the nesting levels.
   *
   * @param values Input string with values.
   * @param separator Character used as separator between values.
   * @return List of elements from same level of nesting tree.
   */
  private static List<String> parseMetaAliasValues(final String values, final Character separator) {
    String trimmedString = values.trim();
    String valuesArray = trimmedString.substring(1, trimmedString.length() - 1);

    Map<Character, ArrayType> prefixMap =
        Arrays.stream(ArrayType.values())
            .collect(Collectors.toMap(e -> e.prefix, Function.identity()));

    List<String> resultList = new ArrayList<>();

    ArrayType arrayType = null;
    int level = 0;
    int offset = 0;

    for (int i = 0; i < valuesArray.length(); i++) {
      Character currentChar = valuesArray.charAt(i);

      // Controlling the level of nesting.
      if (Objects.isNull(arrayType) && prefixMap.containsKey(currentChar)) {
        arrayType = prefixMap.get(currentChar);
        level++;
      } else if (Objects.nonNull(arrayType) && currentChar.equals(arrayType.prefix)) {
        level++;
      } else if (Objects.nonNull(arrayType) && currentChar.equals(arrayType.suffix)) {
        level--;
      }

      /*We must collect items from the same level of nesting therefore we can collect items only on level 0.
      Signal for element collection is separator or end of the string*/
      if (level == 0 && (currentChar.equals(separator) || i == valuesArray.length() - 1)) {
        if (currentChar.equals(separator)) {
          resultList.add(valuesArray.substring(offset, i).trim());
        } else {
          resultList.add(valuesArray.substring(offset, i + 1).trim());
        }

        arrayType = null;
        offset = i + 1;
      }
    }

    return resultList;
  }

  private enum ArrayType {
    ARRAY('[', ']', "^\\[.*\\]$"),
    TUPLE('{', '}', "^\\{.*\\}$");

    private final Character prefix;
    private final Character suffix;
    private final String regex;

    ArrayType(Character startChar, Character endChar, String regex) {
      this.prefix = startChar;
      this.suffix = endChar;
      this.regex = regex;
    }
  }
}
