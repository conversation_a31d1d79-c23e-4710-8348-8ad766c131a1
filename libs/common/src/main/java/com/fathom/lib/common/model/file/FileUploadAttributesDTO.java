package com.fathom.lib.common.model.file;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FileUploadAttributesDTO implements Serializable {

  @Serial private static final long serialVersionUID = 7053799671854876169L;

  private String url;

  private String token;

  private String details;
}
