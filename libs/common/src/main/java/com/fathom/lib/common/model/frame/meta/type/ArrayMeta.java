package com.fathom.lib.common.model.frame.meta.type;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ArrayMeta extends Meta implements Serializable {

  @Serial private static final long serialVersionUID = 4288191145404779002L;

  @Valid @NotNull private Meta meta;

  @NotNull private Integer length;
}
