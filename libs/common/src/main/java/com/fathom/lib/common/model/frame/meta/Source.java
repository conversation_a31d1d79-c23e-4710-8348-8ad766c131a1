package com.fathom.lib.common.model.frame.meta;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum Source implements Serializable {
  FIELD("field"),
  STATIC("static"),
  USER_INPUT("userinput"),
  MAESTRO("maestro"),
  CALCULATION("calculation"),
  WELL_TEST("well_test"),
  CUSTOM_CALCULATION("custom_calculation"),
  CATALOG("catalog"),
  CATALOG_BINDING("catalog_binding"),
  LOCATION("location"),
  TABLE("table"),
  MAP("map"),
  FORM("form"),
  DATAWORKSPACE("data_workspace"),
  UNKNOWN("unknown");

  private static final Map<String, Source> VALUES_MAP =
      Stream.of(Source.values()).collect(Collectors.toMap(s -> s.value, Function.identity()));

  private final String value;

  Source(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @JsonCreator
  public static Source fromString(String s) {
    return Optional.ofNullable(VALUES_MAP.get(s.trim().toLowerCase())).orElse(UNKNOWN);
  }
}
