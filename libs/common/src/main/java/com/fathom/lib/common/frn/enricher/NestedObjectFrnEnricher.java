package com.fathom.lib.common.frn.enricher;

import com.fathom.lib.common.frn.ResourcePath;
import com.fathom.lib.common.frn.ResourceType;
import com.fathom.lib.common.frn.aware.FrnPropertyAware;
import com.fathom.lib.common.frn.aware.FrnResourceIdAware;

public class NestedObjectFrnEnricher<T extends FrnPropertyAware & FrnResourceIdAware> {

  private final String resourceName;

  public static <T extends FrnPropertyAware & FrnResourceIdAware> NestedObjectFrnEnricher<T> build(
      String resourceName) {
    return new NestedObjectFrnEnricher<>(resourceName);
  }

  public static <T extends FrnPropertyAware & FrnResourceIdAware> NestedObjectFrnEnricher<T> build(
      ResourceType resourceName) {
    return build(resourceName.getValue());
  }

  protected NestedObjectFrnEnricher(String resourceName) {
    this.resourceName = resourceName;
  }

  public void setFrn(T target, String rootFrn) {
    if (target != null && target.getResourceId() != null) {
      ResourcePath resourcePath = new ResourcePath(resourceName, target.getResourceId());
      target.setFrn(rootFrn + resourcePath.toString());
    }
  }
}
