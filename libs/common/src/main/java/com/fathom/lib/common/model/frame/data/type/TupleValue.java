package com.fathom.lib.common.model.frame.data.type;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class TupleValue extends Value implements Serializable {

  @Serial private static final long serialVersionUID = 4149938385905206292L;

  private List<Value> value;

  public TupleValue() {
    super(ValueType.TUPLE_VALUE);
  }

  public TupleValue(List<Value> value) {
    this();
    this.value = value;
  }

  @Override
  public String toString() {
    return value.toString();
  }
}
