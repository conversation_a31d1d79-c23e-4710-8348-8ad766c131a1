package com.fathom.lib.common.model.frame.data;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum Quality implements Serializable {
  HEALTHY("healthy"),
  GOOD("good"),
  STALE("stale"),
  BAD("bad"),
  UNHEALTHY("unhealthy");

  private static final Map<String, Quality> VALUES_MAP =
      Stream.of(Quality.values()).collect(Collectors.toMap(s -> s.value, Function.identity()));

  private final String value;

  Quality(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @JsonCreator
  public static Quality fromString(String s) {
    if (Objects.isNull(s)
        || HEALTHY.getValue().equals(s.toLowerCase())
        || GOOD.getValue().equals(s.toLowerCase())) {
      return null;
    }

    return Optional.ofNullable(VALUES_MAP.get(s.trim().toLowerCase()))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    "%s is invalid 'Quality' value. Valid values are : %s. (Case insensitive)"
                        .formatted(s, VALUES_MAP.values())));
  }
}
