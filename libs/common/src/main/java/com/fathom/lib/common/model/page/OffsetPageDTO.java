package com.fathom.lib.common.model.page;

import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.MODEL_OFFSET_PAGE_DTO_CLASS;
import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.MODEL_OFFSET_PAGE_PAGE_OFFSET_FIELD;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Schema(description = MODEL_OFFSET_PAGE_DTO_CLASS)
public class OffsetPageDTO<T> extends BasePageDTO<T> implements Serializable {

  @Serial private static final long serialVersionUID = 1257075136646196835L;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_OFFSET_PAGE_PAGE_OFFSET_FIELD)
  private Integer offset;

  public OffsetPageDTO(List<T> content, Integer offset, Integer pageSize, Long totalCount) {
    super(content, pageSize, totalCount);
    this.offset = offset;
  }
}
