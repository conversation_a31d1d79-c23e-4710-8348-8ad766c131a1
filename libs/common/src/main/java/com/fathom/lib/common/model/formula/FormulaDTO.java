package com.fathom.lib.common.model.formula;

import static com.fathom.lib.common.exception.ExMessage.BLANK_NAME_FIELD;
import static com.fathom.lib.common.exception.ExMessage.NAME_CONSTRAINTS_VIOLATION;
import static com.fathom.lib.common.model.Constants.REG_EXP_FOR_NAME_VALIDATION;
import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.asset.BaseDTO;
import com.fathom.lib.common.model.validation.group.Create;
import com.fathom.lib.common.model.validation.group.CreateSystem;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Schema(description = MODEL_FORMULA_DTO_CLASS)
public class FormulaDTO extends BaseDTO implements Serializable {

  @Serial private static final long serialVersionUID = -933027347670311888L;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_BASIC_NAME_FIELD)
  @NotBlank(
      groups = {Create.class},
      message = BLANK_NAME_FIELD)
  @Pattern(
      groups = {Create.class, CreateSystem.class},
      regexp = REG_EXP_FOR_NAME_VALIDATION,
      message = NAME_CONSTRAINTS_VIOLATION)
  private String name;

  @Schema(description = MODEL_SYSTEM_FIELD)
  private boolean system;

  @Schema(description = MODEL_BODY_FIELD)
  private List<@Valid BaseNode> nodes;

  @Override
  public @NotNull String getResourceId() {
    return this.name;
  }
}
