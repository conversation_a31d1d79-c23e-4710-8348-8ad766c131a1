package com.fathom.lib.common.model.asset.template.group;

import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.MODEL_TOGGLE_GROUP_SELECTED_GROUP_ID_FIELD;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ToggleGroup extends Group implements Serializable {

  @Serial private static final long serialVersionUID = -3675122962781042608L;

  @Schema(description = MODEL_TOGGLE_GROUP_SELECTED_GROUP_ID_FIELD)
  private String selectedGroupId;

  public ToggleGroup() {
    super(GroupType.TOGGLE_GROUP);
  }

  public ToggleGroup(
      String selectedGroupId,
      String name,
      String title,
      String description,
      String parentGroupName,
      Integer order) {
    super(GroupType.TOGGLE_GROUP, name, title, description, parentGroupName, order);
    this.selectedGroupId = selectedGroupId;
  }
}
