package com.fathom.lib.common.model.asset.template.binding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class CatalogBinding implements Serializable {

  @EqualsAndHashCode.Include private CatalogBindingTypes bindingType;

  @EqualsAndHashCode.Include
  @NotBlank(groups = {Create.class})
  private String boundId;

  @EqualsAndHashCode.Include
  @NotBlank(groups = {Create.class})
  private String catalogId;

  private List<@Valid CatalogBindingParameter> parameters;

  @EqualsAndHashCode.Include private String catalogName;

  @EqualsAndHashCode.Include private String catalogSelectionPropertyName;
}
