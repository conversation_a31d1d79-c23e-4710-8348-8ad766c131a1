package com.fathom.lib.common.jackson;

import static com.fathom.lib.common.jackson.DatePatterns.LOCAL_DATE_FORMAT;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import java.io.IOException;
import java.io.Serial;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class LocalDateSerializer extends StdSerializer<LocalDate> {

  @Serial private static final long serialVersionUID = -1171334073345685304L;

  public LocalDateSerializer() {
    super(LocalDate.class);
  }

  @Override
  public void serialize(
      LocalDate localDate, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
      throws IOException {
    jsonGenerator.writeString(localDate.format(DateTimeFormatter.ofPattern(LOCAL_DATE_FORMAT)));
  }
}
