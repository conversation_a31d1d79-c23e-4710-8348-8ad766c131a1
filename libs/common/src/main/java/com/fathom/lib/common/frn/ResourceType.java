package com.fathom.lib.common.frn;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ResourceType implements Serializable {
  TEMPLATE("template"),
  ASSET("asset"),
  ASSET_TYPE("asset_type"),
  ASSET_STATUS_INFO("asset_status_info"),
  FORMULA("formula"),
  ALARM("alarm"),
  ALARM_WATCHER("alarm_watcher"),
  DIAGNOSTIC("diagnostic"),
  CALCULATION("calculation"),
  FRAME("frame"),
  MAP_MARKER("marker"),
  TREE("tree"),
  HEATMAP("heatmap"),
  WELL_TEST("wellname"),
  HISTORICAL_RESERVOIR_PRESSURE("historicalreservoirpressure");

  private static final Map<String, ResourceType> VALUES_MAP =
      Stream.of(ResourceType.values()).collect(Collectors.toMap(s -> s.value, Function.identity()));

  private String value;

  ResourceType(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return value;
  }

  @JsonCreator
  public static ResourceType fromString(String s) {
    return Optional.ofNullable(VALUES_MAP.get(s.trim().toLowerCase()))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    ("%s is invalid 'ResourceType' value. "
                            + "Valid values are : %s. (Case insensitive)")
                        .formatted(s, VALUES_MAP.values())));
  }
}
