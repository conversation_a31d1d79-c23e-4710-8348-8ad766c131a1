package com.fathom.lib.common.model.productionTarget;

import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.asset.AssetTypeDTO;
import com.fathom.lib.common.model.asset.BaseDTO;
import com.fathom.lib.common.model.frame.data.PropertyData;
import com.fathom.lib.common.model.validation.group.Create;
import com.fathom.lib.common.model.validation.group.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Schema(description = MODEL_PRODUCTION_TARGET_CLASS)
public class ProductionTargetDTO extends BaseDTO implements Serializable {

  @Serial private static final long serialVersionUID = -4311604006107270570L;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_PRODUCTION_TARGET_ASSET_ID_FIELD)
  @NotNull(groups = {Create.class})
  private UUID assetId;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_PRODUCTION_TARGET_ASSET_NAME_FIELD)
  @NotNull(groups = {Create.class})
  private String assetName;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_PRODUCTION_TARGET_ASSET_TYPE_ID_FIELD)
  private UUID assetTypeId;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_PRODUCTION_TARGET_ASSET_TYPE_NAME_FIELD)
  private String assetTypeName;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_PRODUCTION_TARGET_EFFECTIVE_DATE_FIELD)
  @NotNull(groups = {Create.class, Update.class})
  private LocalDate effectiveDate;

  @Schema(description = MODEL_PRODUCTION_TARGET_DATA_FIELD)
  @NotNull(groups = {Create.class, Update.class})
  private PropertyData data;

  @Schema(description = MODEL_PRODUCTION_TARGET_LO_VALUE_FIELD)
  @NotNull(groups = {Create.class, Update.class})
  private PropertyData loValue;

  @Schema(description = MODEL_PRODUCTION_TARGET_HI_VALUE_FIELD)
  @NotNull(groups = {Create.class, Update.class})
  private PropertyData hiValue;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_PRODUCTION_TARGET_EMAIL_FIELD)
  @NotNull(groups = {Create.class})
  private String email;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_PRODUCTION_TARGET_IS_DEFAULT_FIELD)
  private boolean isDefault;

  @Override
  public @NotNull String getResourceId() {
    return assetName;
  }

  @JsonIgnore
  public void setAssetTypeDetails(AssetTypeDTO assetTypeDTO) {
    if (assetTypeDTO != null) {
      this.assetTypeId = assetTypeDTO.getId();
      this.assetTypeName = assetTypeDTO.getName();
    }
  }

  public LocalDate getEffectiveDate() {
    if (effectiveDate == null) {
      return null;
    }
    return effectiveDate.atStartOfDay().toLocalDate();
  }

  public void setEffectiveDate(LocalDate effectiveDate) {
    if (effectiveDate != null) this.effectiveDate = effectiveDate.atStartOfDay().toLocalDate();
  }

  @JsonIgnore
  public String getAssetIdAsString() {
    return this.assetId != null ? this.assetId.toString() : null;
  }

  @JsonIgnore
  public void setAssetIdAsString(String assetId) {
    if (assetId != null) this.assetId = UUID.fromString(assetId);
  }

  @JsonIgnore
  public void setAssetTypeIdAsString(String assetTypeId) {
    if (assetTypeId != null) this.assetTypeId = UUID.fromString(assetTypeId);
  }
}
