package com.fathom.lib.common.model.asset.template.group;

import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fathom.lib.common.model.validation.group.Create;
import com.fathom.lib.common.model.validation.group.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.EXISTING_PROPERTY,
    property = "type",
    visible = true)
@JsonSubTypes({
  @JsonSubTypes.Type(name = "group", value = CommonGroup.class),
  @JsonSubTypes.Type(name = "toggle", value = ToggleGroup.class)
})
@Schema(description = MODEL_GROUP_CLASS)
public abstract class Group implements Serializable {

  @Serial private static final long serialVersionUID = 1707667412383752201L;

  @Schema(description = MODEL_GROUP_ID_FIELD)
  @NotBlank(groups = {Create.class, Update.class})
  private String id;

  @NotNull(groups = {Create.class, Update.class})
  @Schema(description = MODEL_GROUP_TYPE_FIELD)
  private GroupType type;

  @NotBlank(groups = {Create.class, Update.class})
  @Schema(description = MODEL_GROUP_NAME_FIELD)
  private String name;

  @NotBlank(groups = {Create.class, Update.class})
  @Schema(description = MODEL_GROUP_TITLE_FIELD)
  private String title;

  @Schema(description = MODEL_GROUP_DESCRIPTION_FIELD)
  private String description;

  @Schema(description = MODEL_GROUP_PARENT_GROUP_ID_FIELD)
  private String parentGroupId;

  @Schema(description = MODEL_GROUP_ORDER_FIELD)
  private Integer order;

  @Schema(description = MODEL_BOUND_ASSET_TYPE_NAME_FIELD)
  private String boundAssetTypeName;

  public Group(GroupType type) {
    this.type = type;
  }

  public Group(
      GroupType type,
      String name,
      String title,
      String description,
      String parentGroupId,
      Integer order) {
    this.type = type;
    this.name = name;
    this.title = title;
    this.description = description;
    this.parentGroupId = parentGroupId;
    this.order = order;
  }
}
