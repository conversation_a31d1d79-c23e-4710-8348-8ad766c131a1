package com.fathom.lib.common.model.doc;

public final class ModelSwaggerConstants {

  // Class Descriptions
  public static final String MODEL_ASSET_DTO_CLASS = "Data transfer object for asset entity.";
  public static final String MODEL_TEMPLATE_DTO_CLASS = "Data transfer object for template entity.";
  public static final String MODEL_GROUP_CLASS = "Model class for group object.";
  public static final String MODEL_RESTRICTION_CLASS = "Model class for restriction object.";
  public static final String MODEL_CALCULATION_DTO_CLASS =
      "Data transfer object for calculation entity.";
  public static final String MODEL_VALIDATOR_CLASS = "Model class for validator object.";
  public static final String MODEL_BASE_PAGE_DTO_CLASS =
      "Base data transfer object for pagination.";
  public static final String MODEL_COMMON_PAGE_DTO_CLASS =
      "Common data transfer object for pagination.";
  public static final String MODEL_OFFSET_PAGE_DTO_CLASS =
      "Offset data transfer object for pagination.";
  public static final String MODEL_PRODUCTION_TARGET_CLASS =
      "Data transfer object for production target entity.";
  public static final String MODEL_FORMULA_DTO_CLASS =
      "General data transfer object for formula entity.";

  // Field descriptions
  public static final String MODEL_SYSTEM_FIELD = "Defines the system resource";

  // (BasicDTO)
  public static final String MODEL_BASIC_ID_FIELD =
      "Unique resource identifier. Generated on creation, not updatable.";
  public static final String MODEL_BASIC_NAME_FIELD =
      "Name of the resource. Must be assigned on creation and be unique within organization, not updatable.";
  public static final String MODEL_BASIC_VERSION_FIELD =
      "Version attribute of template to detect concurrent modifications on update. The value of this attribute is required on update, and completely controlled by the server. When updating a template, you should simply specify the same value of this attribute that he received in GET request.";
  public static final String MODEL_BASIC_DESCRIPTION_FIELD =
      "Resource description. Optional and updatable";
  public static final String MODEL_BASIC_ORGANIZATION_ID_FIELD =
      "Id of the organization that owns the resource. Must be assigned on creation, not updatable.";
  public static final String MODEL_BASIC_CREATED_DATE_FIELD =
      "Timestamp of moment when the resource was created. Assigned automatically when creating a resource, not updatable.";
  public static final String MODEL_BASIC_UPDATED_DATE_FIELD =
      "Timestamp of moment when the resource was updated. Assigned automatically when updating, not updatable manually.";
  public static final String MODEL_BASIC_FRN_FIELD =
      "Fathom resource name. Generated automatically, not updatable.";
  public static final String MODEL_BASIC_OUTPUT_FIELD =
      "Calculation results will be provided only by a certain output fields.";

  // (BasePageDTO)
  public static final String MODEL_BASE_PAGE_CONTENT_FIELD = "Contains data of selected page.";
  public static final String MODEL_BASE_PAGE_PAGE_SIZE_FIELD = "Displays current page size.";
  public static final String MODEL_BASE_PAGE_TOTAL_COUNT_FIELD =
      "Displays total count of objects from all pages.";

  // (CommonPageDTO)
  public static final String MODEL_COMMON_PAGE_PAGE_NUMBER_FIELD =
      "Displays number of current page.";
  public static final String MODEL_COMMON_PAGE_PAGES_QUANTITY_FIELD =
      "Displays overall pages quantity.";

  // (OffsetPageDTO)
  public static final String MODEL_OFFSET_PAGE_PAGE_OFFSET_FIELD =
      "Displays number of skipped entities.";

  // (AssetDTO)
  public static final String MODEL_ASSET_DISPLAY_NAME_FIELD =
      "Asset display name. Must be assigned on creation, updatable.";
  public static final String MODEL_ASSET_LONGITUDE_FIELD =
      "Asset longitude. Must be assigned on creation, updatable.";
  public static final String MODEL_ASSET_LATITUDE_FIELD =
      "Asset latitude. Must be assigned on creation, updatable.";
  public static final String MODEL_ASSET_COORDINATES_PROPERTY_NAME_FIELD =
      "Asset coordinates property name. Must be assigned on creation, updatable.";
  public static final String MODEL_ASSET_EFFECTIVE_DATE_FIELD =
      "Asset effective date. Optional. If not assigned on create, will be generated automatically.";
  public static final String MODEL_ASSET_BUFFERING_TOPIC_RETENTION_TIME_FIELD =
      "Asset frame buffering topic retention time. Optional. If null, frame buffer service will use default value instead.";
  public static final String MODEL_ASSET_BUFFERING_TOPIC_RETENTION_SIZE_FIELD =
      "Asset frame buffering topic retention size. Optional. If null, frame buffer service will use default value instead.";
  public static final String MODEL_ASSET_TEMPLATE_ID_FIELD =
      "Id of the template that is associated with current asset. Must be assigned on creation, not updatable. This field is used only when you creating an asset. When you get an asset this field will not be displayed.";
  public static final String MODEL_ASSET_REVISION_NUMBER_FIELD =
      "Field that describes asset revision number. On creation is equal to 1, completely controlled by the server, incremented when creating a new revision of an asset, not updatable manually.";
  public static final String MODEL_ASSET_CREATE_REVISION_FIELD =
      "Field created to indicate whether you want to create a new revision when updating an asset. Used on update, optional, default value is equal to false.";
  public static final String MODEL_ASSET_TEMPLATE_FIELD = "Template assigned to current asset.";
  public static final String MODEL_ASSET_INITIAL_DATA_FIELD =
      "Asset initial data. Must be assigned on creation, updatable.";
  public static final String MODEL_ASSET_ASSET_TYPE_FIELD =
      "Asset type. Optional. If not specified, value will be taken from template.";
  public static final String MODEL_ASSET_AREA_COORDINATES_NAME_FIELD =
      "Asset's area coordinates property name. Optional.";
  public static final String MODEL_ASSET_AREA_COORDINATES_FIELD =
      "Two dimensional array of area coordinates. Optional.";
  public static final String MODEL_ASSET_DATA_SOURCE_FIELD =
      "Actual data source of asset's properties.";

  // (AssetTypeDTO)
  public static final String MODEL_ASSET_TYPE_DISPLAY_NAME_FIELD = "Asset type display name.";
  public static final String MODEL_ASSET_TYPE_ICON_CLASS_FIELD = "Asset type icon class.";
  public static final String MODEL_ASSET_TYPE_ICON_PATH_FIELD = "Asset type icon path.";
  public static final String MODEL_ASSET_TYPE_CONTEXT_MENU_ITEMS_FIELD =
      "Asset type context menu items.";
  public static final String MODEL_ASSET_TYPE_MAX = "Asset type max zoom value.";
  public static final String MODEL_ASSET_TYPE_MIN = "Asset type min zoom value.";

  // ContextMenuItem
  public static final String MODEL_CONTEXT_MENU_ITEM_TEXT_FIELD = "Context menu item text field.";
  public static final String MODEL_CONTEXT_MENU_ITEM_ICON_CLASS_FIELD =
      "Context menu item icon class.";
  public static final String MODEL_CONTEXT_MENU_ITEM_ICON_PATH_FIELD =
      "Context menu item icon path.";
  public static final String MODEL_CONTEXT_MENU_ITEM_ROUTE_PATH_FIELD =
      "Context menu item route path field.";
  public static final String MODEL_CONTEXT_MENU_ITEM_HREF_FIELD = "Context menu item href field.";
  public static final String MODEL_CONTEXT_MENU_ITEM_FRAGMENT_FIELD =
      "Context menu item fragment field.";

  // (TemplateDTO)
  public static final String MODEL_TEMPLATE_TYPE_FIELD =
      "Template templateType field. Required on create.";
  public static final String MODEL_TEMPLATE_GROUPS_FIELD = "Template groups field. Optional.";
  public static final String MODEL_TEMPLATE_META_FIELD = "Template meta field. Required on create.";
  public static final String MODEL_TEMPLATE_FORMULA_BINDINGS_FIELD =
      "Template formula bindings field. Optional.";
  public static final String MODEL_TEMPLATE_CATALOG_BINDINGS_FIELD =
      "Template catalog bindings field. Optional.";
  public static final String MODEL_TEMPLATE_CATALOG_SETTINGS_FIELD =
      "Template catalog settings field. Optional.";
  public static final String MODEL_TEMPLATE_DEFAULT_ASSET_TYPE_FIELD =
      "Default asset type. Must be assigned on creation, updatable.";
  public static final String MODEL_TEMPLATE_DEFAULT_ASSET_TYPE_DESC_FIELD =
      "Default asset type description. Read only.";
  public static final String MODEL_TEMPLATE_DEFAULT_ASSET_TYPE_DISPLAY_NAME_FIELD =
      "Default asset type display name. Rad only.";
  public static final String MODEL_TEMPLATE_SUPPORTED_TYPES_FIELD =
      "Supported asset types. Must be assigned on creation, updatable.";
  public static final String MODEL_TEMPLATE_DEFAULT_COORDINATES_PROPERTY_NAME_FIELD =
      "Default asset coordinates property name. Must be assigned on creation, updatable.";

  // (Group)
  public static final String MODEL_GROUP_ID_FIELD = "Displays group id";
  public static final String MODEL_GROUP_TYPE_FIELD =
      "Field that describes templateType of group object. Exists two types of group: group, toggle";
  public static final String MODEL_GROUP_NAME_FIELD = "Displays group name.";
  public static final String MODEL_GROUP_TITLE_FIELD = "Displays group title.";
  public static final String MODEL_GROUP_DESCRIPTION_FIELD = "Displays group description.";
  public static final String MODEL_GROUP_PARENT_GROUP_ID_FIELD = "Displays parent group id.";
  public static final String MODEL_GROUP_ORDER_FIELD = "Displays groups order number.";
  public static final String MODEL_BOUND_ASSET_TYPE_NAME_FIELD = "Displays bound asset type name.";

  // (Toggle group)
  public static final String MODEL_TOGGLE_GROUP_SELECTED_GROUP_ID_FIELD =
      "Displays selected group id. ";

  // (Restriction)
  public static final String MODEL_RESTRICTION_VALUE_FIELD = "Displays restriction value.";
  public static final String MODEL_RESTRICTION_ALIAS_FIELD = "Displays alias name of restriction.";

  // (Validator)
  public static final String MODEL_VALIDATOR_VALIDATOR_TYPE_FIELD =
      "Displays validator templateType.";
  public static final String MODEL_VALIDATOR_PARAMETER_FIELD = "Displays validator parameter.";

  // (CalculationDTO)
  public static final String MODEL_CALCULATION_TEMPLATE_ID_FIELD =
      "Id of the template that is associated with current calculation. Must be assigned on creation, not updatable. This field is used only when you creating an calculation.";
  public static final String MODEL_CALCULATION_TEMPLATE_NAME_FIELD =
      "Name of the template that is associated with current calculation. Must be assigned on creation, not updatable. This field is used only when you creating an calculation.";
  public static final String MODEL_CALCULATION_CODE_FIELD =
      "Field that contains code which will be executed on frame. Optional. Updatable";
  public static final String MODEL_CALCULATION_PREFERRED_UNITS_FIELD =
      "Preferred units map. Contains a list of values to which the original values in frame will be converted. Optional. Updatable.";
  public static final String MODEL_CALCULATION_EXCLUDED_ASSET_IDS_FIELD =
      "List of asset ids for which this calculation will not be applied. All assets must be related with same template. Optional. Updatable.";
  public static final String MODEL_CALCULATION_PRIORITY_FIELD =
      "Priority of the calculation. Higher number calculations will be executed last.";

  // (ProductionTargetDTO)
  public static final String MODEL_PRODUCTION_TARGET_ASSET_ID_FIELD =
      "Id of the asset that is associated with current production target. Must be assigned on creation, updatable.";
  public static final String MODEL_PRODUCTION_TARGET_ASSET_NAME_FIELD =
      "Name of the asset that is associated with current production target. Must be assigned on creation, updatable.";
  public static final String MODEL_PRODUCTION_TARGET_ASSET_TYPE_ID_FIELD =
      "Asset type id that is associated with current production target. Must be assigned on creation";
  public static final String MODEL_PRODUCTION_TARGET_ASSET_TYPE_NAME_FIELD =
      "Asset type name that is associated with current production target. Must be assigned on creation";
  public static final String MODEL_PRODUCTION_TARGET_EFFECTIVE_DATE_FIELD =
      "Production target effective date property. Must be assigned on creation, updatable.";
  public static final String MODEL_PRODUCTION_TARGET_DATA_FIELD =
      "Production target data property. Must be assigned on creation, updatable.";
  public static final String MODEL_PRODUCTION_TARGET_LO_VALUE_FIELD =
      "Production target lo value property. Must be assigned on creation, updatable.";
  public static final String MODEL_PRODUCTION_TARGET_HI_VALUE_FIELD =
      "Production target hi value property. Must be assigned on creation, updatable.";
  public static final String MODEL_PRODUCTION_TARGET_EMAIL_FIELD =
      "Production target creator email property. Must be assigned on creation, not updatable.";
  public static final String MODEL_PRODUCTION_TARGET_IS_DEFAULT_FIELD =
      "Production target isDefault property. False by default.";

  // (FormulaDTO)
  public static final String MODEL_BODY_FIELD = "Contains formula body string.";

  // (AssetStatusConfigDTO)
  public static final String MODEL_ASSET_STATUS_CONFIG_NAME_FIELD =
      "Asset status info name property. Must be assigned on creation, updatable.";
  public static final String MODEL_ASSET_STATUS_CONFIG_COLOR_FIELD =
      "Asset status info color property. Must be assigned on creation, updatable.";
  public static final String MODEL_ASSET_STATUS_CONFIG_ICON_ID_FIELD =
      "Asset status info icon id property. Must be assigned on creation, updatable.";
  public static final String MODEL_ASSET_STATUS_CONFIG_MAP_MARKER_IMAGE_ID_FIELD =
      "Asset status info icon id property. Must be assigned on creation, updatable.";
  public static final String MODEL_ASSET_STATUS_CONFIG_ASSET_TYPE_NAMES_FIELD =
      "Asset status info asset type names property. Updatable.";
  public static final String MODEL_ASSET_STATUS_CONFIG_ASSET_TYPE_FIELD =
      "Asset status info asset type property. Updatable.";
}
