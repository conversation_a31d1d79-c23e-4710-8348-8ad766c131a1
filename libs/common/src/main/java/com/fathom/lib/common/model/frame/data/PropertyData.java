package com.fathom.lib.common.model.frame.data;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fathom.lib.common.frn.aware.FrnPropertyAware;
import com.fathom.lib.common.jackson.PropertyDataDeserializer;
import com.fathom.lib.common.jackson.PropertyDataSerializer;
import com.fathom.lib.common.model.frame.data.type.Value;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.SerializationUtils;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonDeserialize(using = PropertyDataDeserializer.class)
@JsonSerialize(using = PropertyDataSerializer.class)
public class PropertyData implements FrnPropertyAware, Cloneable, Serializable {

  @Serial private static final long serialVersionUID = -2974081132782101791L;

  @NotNull private LocalDateTime timestamp;

  private Quality quality;

  @NotNull private Value value;

  @NotNull private JsonNode unit;

  @NotBlank private String metaAlias;

  private String strError;

  @EqualsAndHashCode.Exclude private String frn;

  public PropertyData clone() {
    return SerializationUtils.clone(this);
  }
}
