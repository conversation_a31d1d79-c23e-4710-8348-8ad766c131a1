package com.fathom.lib.common.model.file;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import jakarta.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FathomFileDTO implements Serializable {

  @Serial private static final long serialVersionUID = 7138904576642775852L;

  private String id;
  private String fileId;
  private LocalDateTime date;
  private String fileName;
  private String displayName;
  private String description;

  @NotBlank(groups = {Create.class})
  private String objectUUID;

  @NotBlank(groups = {Create.class})
  private String classification;

  private String content;
  private String contentType;
  private Boolean sendToGeneralKafkaTopic = false;
  private Boolean sendToKafkaTopic = false;
  private String topicName;
  private Long fileSize;
}
