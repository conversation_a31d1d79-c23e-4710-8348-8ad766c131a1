package com.fathom.lib.common.model.frame.meta.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ContentType implements Serializable {
  STRING("string"),
  NUMBER("number"),
  BOOLEAN("bool"),
  DATE_TIME("datetime");

  private static final Map<String, ContentType> VALUES_MAP =
      Stream.of(ContentType.values()).collect(Collectors.toMap(s -> s.value, Function.identity()));

  static {
    VALUES_MAP.put("boolean", BOOLEAN);
  }

  private final String value;

  ContentType(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @<PERSON>son<PERSON>reator
  public static ContentType fromString(String s) {
    return Optional.ofNullable(VALUES_MAP.get(s.trim().toLowerCase()))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    "%s is invalid 'ContentType' value. Valid values are : %s. (Case insensitive)"
                        .formatted(s, VALUES_MAP.values())));
  }
}
