package com.fathom.lib.common.model.page;

import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@Schema(description = MODEL_BASE_PAGE_DTO_CLASS)
public abstract class BasePageDTO<T> implements Serializable {

  @Serial private static final long serialVersionUID = 5034548573432983239L;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_BASE_PAGE_CONTENT_FIELD)
  private List<T> content;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_BASE_PAGE_PAGE_SIZE_FIELD)
  private Integer pageSize;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_BASE_PAGE_TOTAL_COUNT_FIELD)
  private Long totalCount;
}
