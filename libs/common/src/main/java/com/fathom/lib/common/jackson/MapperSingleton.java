package com.fathom.lib.common.jackson;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.TimeZone;

public class MapperSingleton {

  private static volatile ObjectMapper mapper;

  private MapperSingleton() {}

  public static ObjectMapper getMapper() {
    if (Objects.isNull(mapper)) {
      synchronized (ObjectMapper.class) {
        if (Objects.isNull(mapper)) {
          mapper = new ObjectMapper();
          mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
          // enable to make keys case insensitive
          mapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, false);
          mapper.setTimeZone(TimeZone.getTimeZone("UTC"));
          JavaTimeModule javaTimeModule = new JavaTimeModule();
          javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer());
          javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer());
          javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer());
          javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer());

          mapper.registerModule(javaTimeModule);
        }
      }
    }
    return mapper;
  }
}
