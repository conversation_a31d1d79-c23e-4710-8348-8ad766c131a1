package com.fathom.lib.common.model.formula;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResultNode extends BaseNode implements Serializable {

  @Serial private static final long serialVersionUID = 4389365408283884666L;

  @NotNull(groups = {Create.class})
  private Long sourceNodeId;

  public ResultNode() {
    super(NodeTypes.RESULT);
  }

  public ResultNode(Long id, Double xAxisPosition, Double yAxisPosition, Long sourceNodeId) {
    super(NodeTypes.RESULT, id, xAxisPosition, yAxisPosition);
    this.sourceNodeId = sourceNodeId;
  }
}
