package com.fathom.lib.common.model.frame.data.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ValueType implements Serializable {
  SINGLE_VALUE("SingleValue"),
  ARRAY_VALUE("ArrayValue"),
  TUPLE_VALUE("TupleValue");

  private static final Map<String, ValueType> VALUES_MAP =
      Stream.of(ValueType.values())
          .collect(Collectors.toMap(s -> s.value.toLowerCase(), Function.identity()));

  private final String value;

  ValueType(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @JsonCreator
  public static ValueType fromString(String s) {
    return Optional.ofNullable(VALUES_MAP.get(s.trim().toLowerCase()))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    "%s is invalid 'ValueType' value. Valid values are : %s. (Case insensitive)"
                        .formatted(s, VALUES_MAP.values())));
  }
}
