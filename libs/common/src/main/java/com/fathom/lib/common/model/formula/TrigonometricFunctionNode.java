package com.fathom.lib.common.model.formula;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TrigonometricFunctionNode extends BaseNode implements Serializable {

  @Serial private static final long serialVersionUID = -1613732869432336270L;

  @NotNull(groups = {Create.class})
  private TrigonometricFunctionTypes functionType;

  @NotNull(groups = {Create.class})
  private Long sourceNodeId;

  public TrigonometricFunctionNode() {
    super(NodeTypes.TRIGONOMETRIC_FUNCTION);
  }

  public TrigonometricFunctionNode(
      Long id,
      Double xAxisPosition,
      Double yAxisPosition,
      TrigonometricFunctionTypes functionType,
      Long sourceNodeId) {
    super(NodeTypes.TRIGONOMETRIC_FUNCTION, id, xAxisPosition, yAxisPosition);
    this.functionType = functionType;
    this.sourceNodeId = sourceNodeId;
  }
}
