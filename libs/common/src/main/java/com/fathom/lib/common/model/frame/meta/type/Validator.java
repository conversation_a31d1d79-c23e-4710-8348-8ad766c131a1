package com.fathom.lib.common.model.frame.meta.type;

import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import com.fathom.lib.common.model.validation.group.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = MODEL_VALIDATOR_CLASS)
public class Validator implements Serializable {

  @Serial private static final long serialVersionUID = 8426228922640300650L;

  @NotBlank(groups = {Create.class, Update.class})
  @Schema(description = MODEL_VALIDATOR_VALIDATOR_TYPE_FIELD)
  private String validatorType;

  @Schema(description = MODEL_VALIDATOR_PARAMETER_FIELD)
  private String parameter;
}
