package com.fathom.lib.common.model.asset;

import static com.fathom.lib.common.exception.ExMessage.ASSET_TYPE_NAME_CONSTRAINTS_VIOLATION;
import static com.fathom.lib.common.exception.ExMessage.BLANK_NAME_FIELD;
import static com.fathom.lib.common.model.Constants.REG_EXP_FOR_ASSET_TYPE_NAME_VALIDATION;
import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import com.fathom.lib.common.model.validation.group.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
public class AssetTypeDTO extends BaseDTO implements Serializable {

  @Serial private static final long serialVersionUID = -9078839746875280025L;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_BASIC_NAME_FIELD)
  @NotBlank(
      groups = {Create.class},
      message = BLANK_NAME_FIELD)
  @Pattern(
      groups = {Create.class},
      regexp = REG_EXP_FOR_ASSET_TYPE_NAME_VALIDATION,
      message = ASSET_TYPE_NAME_CONSTRAINTS_VIOLATION)
  private String name;

  @Schema(description = MODEL_ASSET_TYPE_DISPLAY_NAME_FIELD)
  @NotBlank(groups = {Create.class})
  private String displayName;

  @Schema(description = MODEL_ASSET_TYPE_ICON_CLASS_FIELD)
  private String iconClass;

  @Schema(description = MODEL_ASSET_TYPE_ICON_PATH_FIELD)
  private String iconPath;

  @DecimalMin(
      value = "0.0",
      groups = {Create.class, Update.class})
  @DecimalMax(
      value = "22.0",
      groups = {Create.class, Update.class})
  @Schema(description = MODEL_ASSET_TYPE_MAX)
  private Float mapMaxVisibilityZoom;

  @DecimalMin(
      value = "0.0",
      groups = {Create.class, Update.class})
  @DecimalMax(
      value = "22.0",
      groups = {Create.class, Update.class})
  @Schema(description = MODEL_ASSET_TYPE_MIN)
  private Float mapMinVisibilityZoom;

  @Schema(description = MODEL_ASSET_TYPE_CONTEXT_MENU_ITEMS_FIELD)
  private List<@Valid ContextMenuItem> contextMenuItems;

  @Override
  public String getResourceId() {
    return this.name;
  }
}
