package com.fathom.lib.common.frn.enricher.general;

import com.fathom.lib.common.frn.ResourceType;
import com.fathom.lib.common.frn.ServiceName;
import com.fathom.lib.common.frn.enricher.FrnEnricher;
import com.fathom.lib.common.frn.enricher.NestedFrnEnricher;
import com.fathom.lib.common.model.asset.AssetDTO;
import com.fathom.lib.common.model.asset.AssetTypeDTO;

public final class AssetDTOFrnEnricher extends NestedFrnEnricher<AssetDTO> {

  private final FrnEnricher<AssetTypeDTO> assetTypeDTOFrnEnricher;
  private final PropertyDataFrnEnricher propertyDataFrnEnricher;
  private final TemplateDTOFrnEnricher templateDTOFrnEnricher;

  public static AssetDTOFrnEnricher build(String serviceName, String rootResourceName) {
    return new AssetDTOFrnEnricher(serviceName, rootResourceName);
  }

  public static AssetDTOFrnEnricher build(ServiceName serviceName, ResourceType rootResourceType) {
    return build(serviceName.getValue(), rootResourceType.getValue());
  }

  private AssetDTOFrnEnricher(String serviceName, String rootResourceName) {
    super(serviceName, rootResourceName);
    this.assetTypeDTOFrnEnricher =
        FrnEnricher.build(serviceName, ResourceType.ASSET_TYPE.getValue());
    this.propertyDataFrnEnricher = PropertyDataFrnEnricher.build();
    this.templateDTOFrnEnricher = TemplateDTOFrnEnricher.build(serviceName);
  }

  @Override
  protected void setNestedFrn(AssetDTO target, String rootNodeFrn) {
    assetTypeDTOFrnEnricher.setFrn(target.getType());
    templateDTOFrnEnricher.setFrn(target.getTemplate());
    propertyDataFrnEnricher.setFrn(target.getInitialData(), rootNodeFrn);
  }
}
