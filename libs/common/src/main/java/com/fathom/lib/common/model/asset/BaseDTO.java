package com.fathom.lib.common.model.asset;

import static com.fathom.lib.common.exception.ExMessage.MISSED_VERSION_NUMBER_ON_UPDATE;
import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import com.fathom.lib.common.frn.aware.FrnAware;
import com.fathom.lib.common.model.validation.group.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public abstract class BaseDTO implements FrnAware {

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_BASIC_ID_FIELD)
  private UUID id;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_BASIC_ORGANIZATION_ID_FIELD)
  private UUID orgId;

  @EqualsAndHashCode.Include
  @NotNull(
      groups = {Update.class},
      message = MISSED_VERSION_NUMBER_ON_UPDATE)
  @Schema(description = MODEL_BASIC_VERSION_FIELD)
  private Integer version;

  @Schema(description = MODEL_BASIC_DESCRIPTION_FIELD)
  private String description;

  @Schema(description = MODEL_BASIC_CREATED_DATE_FIELD)
  private LocalDateTime createdDate;

  @Schema(description = MODEL_BASIC_UPDATED_DATE_FIELD)
  private LocalDateTime updatedDate;

  @Schema(description = MODEL_BASIC_FRN_FIELD)
  private String frn;
}
