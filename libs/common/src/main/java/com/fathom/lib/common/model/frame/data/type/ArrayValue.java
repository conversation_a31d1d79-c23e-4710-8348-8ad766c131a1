package com.fathom.lib.common.model.frame.data.type;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class ArrayValue extends Value implements Serializable {

  @Serial private static final long serialVersionUID = -4691224688442162953L;

  private List<Value> value;

  public ArrayValue() {
    super(ValueType.ARRAY_VALUE);
  }

  public ArrayValue(List<Value> value) {
    this();
    this.value = value;
  }

  @Override
  public String toString() {
    return value.toString();
  }
}
