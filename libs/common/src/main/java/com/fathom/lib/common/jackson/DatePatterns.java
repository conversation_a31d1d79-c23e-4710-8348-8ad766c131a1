package com.fathom.lib.common.jackson;

public final class DatePatterns {
  public static final String OUTPUT_DATE_PATTERN_ISO = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
  public static final String INPUT_DATE_PATTERN_ISO_WITH_Z =
      "yyyy-MM-dd'T'HH:mm:ss[.SSS][.SS][.S][]'Z'";
  public static final String INPUT_DATE_PATTERN_ISO_WITHOUT_Z =
      "yyyy-MM-dd'T'HH:mm:ss[.SSS][.SS][.S][]";
  public static final String LOCAL_DATE_FORMAT = "yyyy-MM-dd";

  private DatePatterns() {}
}
