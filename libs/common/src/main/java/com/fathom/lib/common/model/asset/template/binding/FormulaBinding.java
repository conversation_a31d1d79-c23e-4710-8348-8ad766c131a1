package com.fathom.lib.common.model.asset.template.binding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class FormulaBinding implements Serializable {

  @EqualsAndHashCode.Include
  @NotBlank(groups = {Create.class})
  private String formulaId;

  @EqualsAndHashCode.Include
  @NotBlank(groups = {Create.class})
  private String propertyId;

  @EqualsAndHashCode.Include
  @NotBlank(groups = {Create.class})
  private String metaPath;

  private List<@Valid PropertySelection> formulaParameters;
}
