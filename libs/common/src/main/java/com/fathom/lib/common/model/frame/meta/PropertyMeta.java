package com.fathom.lib.common.model.frame.meta;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.frn.aware.FrnPropertyAware;
import com.fathom.lib.common.frn.aware.FrnResourceIdAware;
import com.fathom.lib.common.model.frame.meta.type.Meta;
import com.fathom.lib.common.model.frame.meta.type.ValueMeta;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.SerializationUtils;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PropertyMeta implements FrnPropertyAware, FrnResourceIdAware, Cloneable, Serializable {

  @Serial private static final long serialVersionUID = -1748653942813037583L;

  @Valid @NotNull private Meta meta;

  private String description;

  @NotBlank private String name;

  private String displayName;

  @NotNull private Source source;

  private String maestroTag;

  private String wellTestColumn;

  private String groupId;

  private Integer order;

  @Valid private ValueMeta lolo;

  @Valid private ValueMeta lo;

  @Valid private ValueMeta hi;

  @Valid private ValueMeta hihi;

  @EqualsAndHashCode.Exclude private String frn;

  public PropertyMeta clone() {
    return SerializationUtils.clone(this);
  }

  @Override
  public String getResourceId() {
    return name;
  }
}
