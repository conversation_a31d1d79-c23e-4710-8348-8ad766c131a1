package com.fathom.lib.common.model.frame.meta.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum SelectionType implements Serializable {
  RADIOBUTTON("radiobutton"),
  DROPDOWN("dropdown");

  private static final Map<String, SelectionType> VALUES_MAP =
      Stream.of(SelectionType.values())
          .collect(Collectors.toMap(s -> s.value, Function.identity()));

  private final String value;

  SelectionType(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @JsonCreator
  public static SelectionType fromString(String s) {
    return Optional.ofNullable(VALUES_MAP.get(s.trim().toLowerCase()))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    "%s is invalid 'SelectionType' value. Valid values are : %s. (Case insensitive)"
                        .formatted(s, VALUES_MAP.values())));
  }
}
