package com.fathom.lib.common.frn.enricher;

import com.fathom.lib.common.frn.Frn;
import com.fathom.lib.common.frn.ResourcePath;
import com.fathom.lib.common.frn.ResourceType;
import com.fathom.lib.common.frn.ServiceName;
import com.fathom.lib.common.frn.aware.FrnAware;

/**
 * Class that sets the frn to DTO
 *
 * <p>General structure of frn `frn:appName:serviceName:orgId:/rootResourceName/resourceId` E.g.
 * `frn:wells:well_test:org_123:/well_tests/well123`
 */
public abstract class FrnEnricher<T extends FrnAware> {

  private final String appName;
  private final String serviceName;
  private final String rootResourceName;

  /**
   * @param serviceName - name of the service that owns resources
   * @param rootResourceName - name of the root -resource
   * @return - enricher for setting frn property to the model
   */
  public static <T extends FrnAware> FrnEnricher<T> build(
      String serviceName, String rootResourceName) {
    return build("", serviceName, rootResourceName);
  }

  /**
   * @param appName - name of the specific application, e.g. wells, wells_koc
   * @param serviceName - name of the service that owns resources
   * @param rootResourceName - name of the root -resource
   * @return - enricher for setting frn property to the model
   */
  public static <T extends FrnAware> FrnEnricher<T> build(
      String appName, String serviceName, String rootResourceName) {
    return new FrnEnricher<T>(appName, serviceName, rootResourceName) {};
  }

  public static <T extends FrnAware> FrnEnricher<T> build(
      ServiceName serviceName, ResourceType rootResourceName) {
    return build("", serviceName, rootResourceName);
  }

  public static <T extends FrnAware> FrnEnricher<T> build(
      String appName, ServiceName serviceName, ResourceType rootResourceName) {
    return build(appName, serviceName.getValue(), rootResourceName.getValue());
  }

  protected FrnEnricher(String appName, String serviceName, String rootResourceName) {
    this.appName = appName;
    this.serviceName = serviceName;
    this.rootResourceName = rootResourceName;
  }

  public void setFrn(T target) {
    if (target != null) {
      Frn frn = buildFrn(target);
      target.setFrn(frn.toString());
    }
  }

  protected Frn buildFrn(T target) {
    ResourcePath rootResourcePath = new ResourcePath(rootResourceName, target.getResourceId());
    return new Frn(appName, target.getOrgId(), serviceName, rootResourcePath);
  }
}
