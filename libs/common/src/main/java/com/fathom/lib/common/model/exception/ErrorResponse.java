package com.fathom.lib.common.model.exception;

import java.time.LocalDateTime;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ErrorResponse {

  @EqualsAndHashCode.Include private LocalDateTime timestamp;

  @EqualsAndHashCode.Include private Integer status;

  @EqualsAndHashCode.Include private String error;

  @EqualsAndHashCode.Include private String message;

  private ErrorResponse() {}

  public static Builder newBuilder() {
    return new ErrorResponse().new Builder();
  }

  public class Builder {

    private Builder() {}

    public Builder setTimestamp(LocalDateTime timestamp) {
      ErrorResponse.this.timestamp = timestamp;
      return this;
    }

    public Builder setStatus(Integer status) {
      ErrorResponse.this.status = status;
      return this;
    }

    public Builder setError(String error) {
      ErrorResponse.this.error = error;
      return this;
    }

    public Builder setMessage(String message) {
      ErrorResponse.this.message = message;
      return this;
    }

    public ErrorResponse build() {
      return ErrorResponse.this;
    }
  }
}
