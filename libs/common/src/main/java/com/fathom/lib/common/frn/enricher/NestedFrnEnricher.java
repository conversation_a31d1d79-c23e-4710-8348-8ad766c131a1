package com.fathom.lib.common.frn.enricher;

import com.fathom.lib.common.frn.aware.FrnAware;

/** Class that sets the frn to DTO and to the nested fields of the DTO */
public abstract class NestedFrnEnricher<T extends FrnAware> extends FrnEnricher<T> {

  public NestedFrnEnricher(String serviceName, String rootResourceName) {
    this("", serviceName, rootResourceName);
  }

  public NestedFrnEnricher(String appName, String serviceName, String rootResourceName) {
    super(appName, serviceName, rootResourceName);
  }

  @Override
  public void setFrn(T target) {
    if (target != null) {
      String rootFrn = buildFrn(target).toString();
      target.setFrn(rootFrn);
      setNestedFrn(target, rootFrn);
    }
  }

  protected abstract void setNestedFrn(T target, String rootNodeFrn);
}
