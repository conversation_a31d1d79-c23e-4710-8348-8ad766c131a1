package com.fathom.lib.common.exception;

import com.fathom.lib.common.model.exception.ErrorResponse;
import java.io.Serial;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.context.request.WebRequest;

@Getter
@Setter
public class RestException extends RuntimeException {

  @Serial private static final long serialVersionUID = 4768403617392160086L;

  private HttpStatus httpStatusCode;

  public RestException(String message, HttpStatus httpStatusCode) {
    super(message);
    this.httpStatusCode = httpStatusCode;
  }

  public RestException(HttpStatus httpStatusCode, String message, Object... args) {
    super(message.formatted(args));
    this.httpStatusCode = httpStatusCode;
  }

  public static ResponseEntity<ErrorResponse> handleRestException(
      RestException ex, WebRequest request) {
    ErrorResponse errorResponse = restExceptionErrorResponse(ex);
    return new ResponseEntity<>(errorResponse, ex.getHttpStatusCode());
  }

  private static ErrorResponse restExceptionErrorResponse(RestException restException) {
    HttpStatus httpStatus = restException.getHttpStatusCode();

    return ErrorResponse.newBuilder()
        .setTimestamp(LocalDateTime.now())
        .setStatus(httpStatus.value())
        .setError(httpStatus.getReasonPhrase())
        .setMessage(restException.getMessage())
        .build();
  }
}
