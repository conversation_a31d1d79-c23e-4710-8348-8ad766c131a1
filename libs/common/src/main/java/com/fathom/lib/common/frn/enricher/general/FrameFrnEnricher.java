package com.fathom.lib.common.frn.enricher.general;

import com.fathom.lib.common.frn.ResourceType;
import com.fathom.lib.common.frn.ServiceName;
import com.fathom.lib.common.frn.enricher.NestedFrnEnricher;
import com.fathom.lib.common.model.frame.Frame;
import com.fathom.lib.common.model.frame.meta.PropertyMeta;
import jakarta.validation.Valid;
import java.util.Map;

public final class FrameFrnEnricher extends NestedFrnEnricher<Frame> {

  private final PropertyDataFrnEnricher propertyDataEnricher;
  private final PropertyMetaFrnEnricher propertyMetaEnricher;

  public static FrameFrnEnricher build(String serviceName, String rootPropertyName) {
    return new FrameFrnEnricher(serviceName, rootPropertyName);
  }

  public static FrameFrnEnricher build(ServiceName serviceName, ResourceType rootResourceType) {
    return build(serviceName.getValue(), rootResourceType.getValue());
  }

  private FrameFrnEnricher(String serviceName, String rootPropertyName) {
    super(serviceName, rootPropertyName);
    this.propertyDataEnricher = PropertyDataFrnEnricher.build();
    this.propertyMetaEnricher = PropertyMetaFrnEnricher.build();
  }

  @Override
  protected void setNestedFrn(Frame target, String rootNodeFrn) {
    propertyDataEnricher.setFrn(target.getData(), rootNodeFrn);
    Map<String, @Valid PropertyMeta> targetMeta = target.getMeta();
    if (targetMeta != null) {
      targetMeta.values().forEach(meta -> propertyMetaEnricher.setFrn(meta, rootNodeFrn));
    }
  }
}
