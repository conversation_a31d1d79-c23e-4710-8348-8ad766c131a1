package com.fathom.lib.common.model.asset.template.binding;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PropertySelection implements Serializable {

  @NotNull(groups = {Create.class})
  private Long nodeId;

  @NotBlank(groups = {Create.class})
  private String propertyId;

  @NotBlank(groups = {Create.class})
  private String metaPath;

  @NotNull(groups = {Create.class})
  private Integer indexShift;
}
