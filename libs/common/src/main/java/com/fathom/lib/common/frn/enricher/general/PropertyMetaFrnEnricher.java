package com.fathom.lib.common.frn.enricher.general;

import com.fathom.lib.common.frn.enricher.NestedObjectFrnEnricher;
import com.fathom.lib.common.model.frame.meta.PropertyMeta;
import java.util.Map;

public final class PropertyMetaFrnEnricher extends NestedObjectFrnEnricher<PropertyMeta> {

  private static final String PROPERTY_META_RESOURCE_NAME = "property";

  public static PropertyMetaFrnEnricher build(String resourceName) {
    return new PropertyMetaFrnEnricher(resourceName);
  }

  public static PropertyMetaFrnEnricher build() {
    return build(PROPERTY_META_RESOURCE_NAME);
  }

  private PropertyMetaFrnEnricher(String resourceName) {
    super(resourceName);
  }

  public void setFrn(Map<String, PropertyMeta> target, String rootFrn) {
    if (target != null) {
      target.values().forEach(meta -> setFrn(meta, rootFrn));
    }
  }
}
