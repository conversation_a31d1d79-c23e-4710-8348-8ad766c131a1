package com.fathom.lib.common.frn.enricher.general;

import com.fathom.lib.common.frn.enricher.NestedMapFrnEnricher;
import com.fathom.lib.common.model.frame.data.PropertyData;

public final class PropertyDataFrnEnricher extends NestedMapFrnEnricher<PropertyData> {

  private static final String PROPERTY_DATA_RESOURCE_NAME = "property";

  public static PropertyDataFrnEnricher build(String resourceName) {
    return new PropertyDataFrnEnricher(resourceName);
  }

  public static PropertyDataFrnEnricher build() {
    return build(PROPERTY_DATA_RESOURCE_NAME);
  }

  private PropertyDataFrnEnricher(String resourceName) {
    super(resourceName);
  }
}
