package com.fathom.lib.common.model.asset.template;

import static com.fathom.lib.common.exception.ExMessage.*;
import static com.fathom.lib.common.model.Constants.REG_EXP_FOR_ASSET_TYPE_NAME_VALIDATION;
import static com.fathom.lib.common.model.Constants.REG_EXP_FOR_NAME_VALIDATION;
import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import com.fathom.lib.common.model.asset.BaseDTO;
import com.fathom.lib.common.model.asset.template.binding.CatalogBinding;
import com.fathom.lib.common.model.asset.template.binding.FormulaBinding;
import com.fathom.lib.common.model.asset.template.group.Group;
import com.fathom.lib.common.model.frame.meta.PropertyMeta;
import com.fathom.lib.common.model.validation.group.Create;
import com.fathom.lib.common.model.validation.group.CreateSystem;
import com.fathom.lib.common.model.validation.group.Update;
import com.fathom.lib.common.model.validation.group.UpdateSystem;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Schema(description = MODEL_TEMPLATE_DTO_CLASS)
public class TemplateDTO extends BaseDTO implements Serializable {

  @Serial private static final long serialVersionUID = 6306795691173273100L;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_BASIC_NAME_FIELD)
  @NotBlank(
      groups = {Create.class, CreateSystem.class, Update.class, UpdateSystem.class},
      message = BLANK_NAME_FIELD)
  @Pattern(
      groups = {Create.class, CreateSystem.class, Update.class, UpdateSystem.class},
      regexp = REG_EXP_FOR_NAME_VALIDATION,
      message = NAME_CONSTRAINTS_VIOLATION)
  private String name;

  @Schema(description = MODEL_SYSTEM_FIELD)
  private boolean system;

  @Schema(description = MODEL_TEMPLATE_DEFAULT_ASSET_TYPE_FIELD)
  @NotNull(groups = {Create.class})
  @Pattern(
      groups = {Create.class},
      regexp = REG_EXP_FOR_ASSET_TYPE_NAME_VALIDATION,
      message = ASSET_TYPE_NAME_CONSTRAINTS_VIOLATION)
  private String defaultAssetType;

  @Schema(description = MODEL_TEMPLATE_DEFAULT_ASSET_TYPE_DESC_FIELD)
  private String defaultAssetTypeDescription;

  @Schema(description = MODEL_TEMPLATE_DEFAULT_ASSET_TYPE_DISPLAY_NAME_FIELD)
  private String defaultAssetTypeDisplayName;

  @Schema(description = MODEL_TEMPLATE_SUPPORTED_TYPES_FIELD)
  @NotNull(groups = {Create.class})
  @NotEmpty(groups = {Create.class})
  @Null(groups = {CreateSystem.class, UpdateSystem.class})
  private List<String> supportedAssetTypes;

  @Schema(description = MODEL_TEMPLATE_DEFAULT_COORDINATES_PROPERTY_NAME_FIELD)
  private String defaultCoordinatesPropertyName;

  @Schema(description = MODEL_TEMPLATE_TYPE_FIELD)
  @NotNull(groups = {Create.class, CreateSystem.class})
  private TemplateType type;

  @Schema(description = MODEL_TEMPLATE_GROUPS_FIELD)
  private List<@Valid Group> groups;

  @Schema(description = MODEL_TEMPLATE_META_FIELD)
  @NotNull(groups = {Create.class, CreateSystem.class, UpdateSystem.class})
  private Map<String, @Valid PropertyMeta> meta;

  @Schema(description = MODEL_TEMPLATE_FORMULA_BINDINGS_FIELD)
  private List<@Valid FormulaBinding> formulaBindings;

  @Schema(description = MODEL_TEMPLATE_CATALOG_BINDINGS_FIELD)
  private List<@Valid CatalogBinding> catalogBindings;

  @Schema(description = MODEL_TEMPLATE_CATALOG_SETTINGS_FIELD)
  private CatalogSettings catalogSettings;

  @Override
  public String getResourceId() {
    return this.getId().toString();
  }
}
