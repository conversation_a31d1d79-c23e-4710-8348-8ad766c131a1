package com.fathom.lib.common.jackson;

import static com.fathom.lib.common.jackson.PropertyDataDeserializer.*;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.fathom.lib.common.model.frame.data.PropertyData;
import com.fathom.lib.common.model.frame.data.Quality;
import com.fathom.lib.common.model.frame.data.type.*;
import java.io.IOException;
import java.io.Serial;
import java.time.LocalDateTime;
import java.util.Objects;

public class PropertyDataSerializer extends StdSerializer<PropertyData> {

  @Serial private static final long serialVersionUID = 4828719755415249527L;

  public PropertyDataSerializer() {
    this(null);
  }

  public PropertyDataSerializer(Class<PropertyData> t) {
    super(t);
  }

  @Override
  public void serialize(
      PropertyData propertyData, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
      throws IOException {
    LocalDateTime timestamp = propertyData.getTimestamp();
    Quality quality = propertyData.getQuality();
    Value value = propertyData.getValue();
    JsonNode unit = propertyData.getUnit();
    String metaAlias = propertyData.getMetaAlias();
    String strError = propertyData.getStrError();
    String frn = propertyData.getFrn();

    jsonGenerator.writeStartObject();

    if (Objects.nonNull(timestamp)) {
      jsonGenerator.writeObjectField(PROPERTY_DATA_TIMESTAMP_FIELD, timestamp);
    }

    if (Objects.nonNull(quality)) {
      jsonGenerator.writeObjectField(PROPERTY_DATA_QUALITY_FIELD, quality);
    }

    if (Objects.nonNull(value)) {
      jsonGenerator.writeFieldName(PROPERTY_DATA_VALUE_FIELD);
      writeValueField(jsonGenerator, value);
    }

    if (Objects.nonNull(unit)) {
      jsonGenerator.writeObjectField(PROPERTY_DATA_UNIT_FIELD, unit);
    }

    if (Objects.nonNull(metaAlias)) {
      jsonGenerator.writeObjectField(PROPERTY_DATA_META_ALIAS_FIELD, metaAlias);
    }

    if (Objects.nonNull(strError)) {
      jsonGenerator.writeObjectField(PROPERTY_DATA_STR_ERROR_FIELD, strError);
    }

    if (Objects.nonNull(frn)) {
      jsonGenerator.writeObjectField(PROPERTY_DATA_FRN_FIELD, frn);
    }

    jsonGenerator.writeEndObject();
  }

  private void writeValueField(JsonGenerator jsonGenerator, Value value) throws IOException {
    if (ValueType.SINGLE_VALUE.equals(value.getValueType())) {
      SingleValue singleValue = (SingleValue) value;
      jsonGenerator.writeObject(singleValue.getValue());
    } else if (ValueType.ARRAY_VALUE.equals(value.getValueType())) {
      ArrayValue arrayValue = (ArrayValue) value;
      jsonGenerator.writeStartArray();

      for (Value v : arrayValue.getValue()) {
        writeValueField(jsonGenerator, v);
      }

      jsonGenerator.writeEndArray();
    } else if (ValueType.TUPLE_VALUE.equals(value.getValueType())) {
      TupleValue tupleValue = (TupleValue) value;

      jsonGenerator.writeStartArray();

      for (Value v : tupleValue.getValue()) {
        writeValueField(jsonGenerator, v);
      }

      jsonGenerator.writeEndArray();
    }
  }
}
