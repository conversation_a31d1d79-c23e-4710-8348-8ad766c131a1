package com.fathom.lib.common.model.asset.template.group;

import java.io.Serial;
import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CommonGroup extends Group implements Serializable {

  @Serial private static final long serialVersionUID = 8487636096595587187L;

  public CommonGroup() {
    super(GroupType.COMMON_GROUP);
  }

  public CommonGroup(
      String name, String title, String description, String parentGroupName, Integer order) {
    super(GroupType.COMMON_GROUP, name, title, description, parentGroupName, order);
  }
}
