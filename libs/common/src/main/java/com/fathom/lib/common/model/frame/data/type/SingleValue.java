package com.fathom.lib.common.model.frame.data.type;

import com.fathom.lib.common.model.frame.meta.type.ContentType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class SingleValue extends Value implements Serializable {

  @Serial private static final long serialVersionUID = 150100990934433599L;

  private Object value;

  @NotNull private ContentType type;

  @NotBlank private String unit;

  public SingleValue() {
    super(ValueType.SINGLE_VALUE);
  }

  public SingleValue(Object value) {
    this();
    this.value = value;
  }

  public SingleValue(Object value, ContentType type, String unit) {
    this();
    this.value = value;
    this.type = type;
    this.unit = unit;
  }

  @Override
  public String toString() {
    return value.toString();
  }
}
