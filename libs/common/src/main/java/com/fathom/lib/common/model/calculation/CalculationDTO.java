package com.fathom.lib.common.model.calculation;

import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.frn.ResourceType;
import com.fathom.lib.common.frn.aware.FrnAware;
import com.fathom.lib.common.model.validation.group.Create;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@Schema(description = MODEL_CALCULATION_DTO_CLASS)
public class CalculationDTO implements Serializable, FrnAware {

  @Serial private static final long serialVersionUID = -860797704477248322L;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_BASIC_ID_FIELD)
  private UUID id;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_BASIC_ORGANIZATION_ID_FIELD)
  private UUID orgId;

  @EqualsAndHashCode.Include
  @NotNull(groups = {Create.class})
  @Schema(description = MODEL_CALCULATION_TEMPLATE_ID_FIELD)
  private UUID templateId;

  @EqualsAndHashCode.Include
  @NotNull(groups = {Create.class})
  @Schema(description = MODEL_CALCULATION_TEMPLATE_NAME_FIELD)
  private String templateName;

  @EqualsAndHashCode.Include
  @NotBlank(groups = {Create.class})
  @Schema(description = MODEL_BASIC_NAME_FIELD)
  private String calculationName;

  @Schema(description = MODEL_BASIC_DESCRIPTION_FIELD)
  private String description;

  @Schema(description = MODEL_CALCULATION_CODE_FIELD)
  private String code;

  @Schema(description = MODEL_CALCULATION_PREFERRED_UNITS_FIELD)
  private Map<String, String> preferredUnits;

  @Schema(description = MODEL_CALCULATION_EXCLUDED_ASSET_IDS_FIELD)
  private List<UUID> excludedAssetsIds;

  @Schema(description = MODEL_BASIC_FRN_FIELD)
  private String frn;

  @Schema(description = MODEL_BASIC_OUTPUT_FIELD)
  private Set<OutputProperty> outputProperties;

  @Schema(description = MODEL_BASIC_FRN_FIELD)
  private int priority;

  @Override
  public @NotNull String getResourceId() {
    return "%s/%s/%s".formatted(templateName, ResourceType.CALCULATION.getValue(), id.toString());
  }
}
