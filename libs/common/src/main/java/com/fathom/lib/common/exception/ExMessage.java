package com.fathom.lib.common.exception;

public final class ExMessage {
  public static final String BLANK_NAME_FIELD =
      "Field 'name' cannot be blank, contain only spaces or be equal to null.";
  public static final String BLANK_COLOUR_FIELD =
      "Field 'colour' cannot be blank, contain only spaces or be equal to null.";
  public static final String NAME_CONSTRAINTS_VIOLATION =
      "The name can only contain lowercase letters, numbers, symbols: ['_', '-', '/'], and contain no more than 128 characters.";
  public static final String ASSET_TYPE_NAME_CONSTRAINTS_VIOLATION =
      "The name can only contain lowercase letters, numbers, symbols: ['_', '-', '.'], and contain no more than 128 characters.";
  public static final String MISSED_VERSION_NUMBER_ON_UPDATE =
      "Version number is required for update operation. Please specify 'version' field with current version number of entity.";
  public static final String NOT_VALID_META_ALIAS_STRUCTURE = "Not valid 'metaAlias' structure";
  public static final String DATA_AND_VALUE_TEMPLATE_MISMATCH =
      "'%s' data does not meet the template requirements. Data: '%s'. Template: '%s'";
  public static final String NOT_EXISTENT_VALUE_TYPE =
      "Can`t parse data. Provided 'ValueType' does not exists. Value: '%s'";
  public static final String INVALID_ARRAY_DEFINITION =
      "Array could be defined only with single value type and unit. Example: correct: [<value>], incorrect: [<value>, <value>] or [<value1>, <value2>, ...]";
  public static final String NONEXISTENT_ENUM_VALUE =
      "%s is invalid %s value. Valid values are : %s";

  private ExMessage() {}
}
