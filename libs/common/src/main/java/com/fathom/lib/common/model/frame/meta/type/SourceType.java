package com.fathom.lib.common.model.frame.meta.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum SourceType implements Serializable {
  REGULAR("regular"),
  CALCULATION("calculation"),
  CATALOG("catalog");

  private static final Map<String, SourceType> VALUES_MAP =
      Stream.of(SourceType.values()).collect(Collectors.toMap(s -> s.value, Function.identity()));

  private final String value;

  SourceType(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @JsonCreator
  public static SourceType fromString(String s) {
    return Optional.ofNullable(VALUES_MAP.get(s.trim().toLowerCase()))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    ("%s is invalid 'SourceType' value. "
                            + "Valid values are : %s. (Case insensitive)")
                        .formatted(s, VALUES_MAP.values())));
  }
}
