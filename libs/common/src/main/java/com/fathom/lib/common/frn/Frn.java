package com.fathom.lib.common.frn;

import java.util.Optional;
import java.util.UUID;
import lombok.Value;

@Value
public class Frn {
  String appName;
  String organizationId;
  String serviceName;
  ResourcePath currentResource;

  public Frn(String appName, UUID organizationId, String serviceName, ResourcePath resourcePath) {
    this.appName = Optional.ofNullable(appName).orElse("");
    if (serviceName == null) {
      throw new IllegalStateException("Service name cannot be null");
    }
    if (organizationId == null) {
      throw new IllegalStateException("[%s] Organization id cannot be null".formatted(serviceName));
    }
    if (resourcePath == null) {
      throw new IllegalStateException("[%s] Resource path cannot be null".formatted(serviceName));
    }
    this.organizationId = organizationId.toString();
    this.serviceName = serviceName;
    this.currentResource = resourcePath;
  }

  @Override
  public String toString() {
    return "frn:%s:%s:%s:%s"
        .formatted(appName, serviceName, organizationId, currentResource.toString());
  }
}
