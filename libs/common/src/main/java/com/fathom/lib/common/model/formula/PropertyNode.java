package com.fathom.lib.common.model.formula;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PropertyNode extends BaseNode implements Serializable {

  @Serial private static final long serialVersionUID = -1380229901286947973L;

  @NotNull(groups = {Create.class})
  private Integer defaultValue;

  public PropertyNode() {
    super(NodeTypes.PROPERTY);
  }

  public PropertyNode(Long id, Double xAxisPosition, Double yAxisPosition, Integer defaultValue) {
    super(NodeTypes.PROPERTY, id, xAxisPosition, yAxisPosition);
    this.defaultValue = defaultValue;
  }
}
