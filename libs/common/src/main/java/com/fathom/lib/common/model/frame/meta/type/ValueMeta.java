package com.fathom.lib.common.model.frame.meta.type;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ValueMeta extends Meta implements Serializable {

  @Serial private static final long serialVersionUID = 2604207461653528542L;

  @NotNull private ContentType contentType;

  @NotBlank private String unit;

  @NotNull private Object defaultValue;

  private List<@Valid Restriction> restrictions;

  @NotNull private SelectionType selectionType;

  private List<@Valid Validator> validators;
}
