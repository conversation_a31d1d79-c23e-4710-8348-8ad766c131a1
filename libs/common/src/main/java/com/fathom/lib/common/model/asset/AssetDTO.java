package com.fathom.lib.common.model.asset;

import static com.fathom.lib.common.exception.ExMessage.BLANK_NAME_FIELD;
import static com.fathom.lib.common.exception.ExMessage.NAME_CONSTRAINTS_VIOLATION;
import static com.fathom.lib.common.model.Constants.REG_EXP_FOR_NAME_VALIDATION;
import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fathom.lib.common.jackson.KeyToLowercaseDeserializer;
import com.fathom.lib.common.model.asset.template.TemplateDTO;
import com.fathom.lib.common.model.frame.data.PropertyData;
import com.fathom.lib.common.model.validation.group.Create;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@Schema(description = MODEL_ASSET_DTO_CLASS)
public class AssetDTO extends BaseDTO implements Serializable {

  @Serial private static final long serialVersionUID = 8938893327168465271L;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_BASIC_NAME_FIELD)
  @NotBlank(
      groups = {Create.class},
      message = BLANK_NAME_FIELD)
  @Pattern(
      groups = {Create.class},
      regexp = REG_EXP_FOR_NAME_VALIDATION,
      message = NAME_CONSTRAINTS_VIOLATION)
  private String name;

  @Schema(description = MODEL_ASSET_ASSET_TYPE_FIELD)
  private AssetTypeDTO type;

  @Schema(description = MODEL_ASSET_EFFECTIVE_DATE_FIELD)
  private LocalDateTime effectiveDate;

  @Schema(description = MODEL_ASSET_DISPLAY_NAME_FIELD)
  @NotBlank(groups = {Create.class})
  private String displayName;

  @Schema(description = MODEL_ASSET_LONGITUDE_FIELD)
  private Double longitude;

  @Schema(description = MODEL_ASSET_LATITUDE_FIELD)
  private Double latitude;

  @Schema(description = MODEL_ASSET_COORDINATES_PROPERTY_NAME_FIELD)
  private String coordinatesPropertyName;

  @Schema(description = MODEL_ASSET_AREA_COORDINATES_FIELD)
  private List<Double[]> areaCoordinates;

  @Schema(description = MODEL_ASSET_AREA_COORDINATES_NAME_FIELD)
  private String areaCoordinatesPropertyName;

  @Schema(description = MODEL_ASSET_DATA_SOURCE_FIELD)
  private String dataSourceName;

  @EqualsAndHashCode.Include
  @Schema(description = MODEL_ASSET_REVISION_NUMBER_FIELD)
  private Long revisionNumber;

  @Schema(description = MODEL_ASSET_TEMPLATE_ID_FIELD)
  @NotNull(groups = {Create.class})
  private UUID templateId;

  @Schema(description = MODEL_ASSET_CREATE_REVISION_FIELD)
  private Boolean createNewRevision;

  @Schema(description = MODEL_ASSET_BUFFERING_TOPIC_RETENTION_TIME_FIELD)
  private Integer bufferingTopicRetentionTime;

  @Schema(description = MODEL_ASSET_BUFFERING_TOPIC_RETENTION_SIZE_FIELD)
  private Integer bufferingTopicRetentionSize;

  @Schema(description = MODEL_ASSET_INITIAL_DATA_FIELD)
  @JsonDeserialize(keyUsing = KeyToLowercaseDeserializer.class)
  private Map<String, @Valid PropertyData> initialData;

  @Schema(description = MODEL_ASSET_TEMPLATE_FIELD)
  private TemplateDTO template;

  @Override
  public String getResourceId() {
    return this.name;
  }
}
