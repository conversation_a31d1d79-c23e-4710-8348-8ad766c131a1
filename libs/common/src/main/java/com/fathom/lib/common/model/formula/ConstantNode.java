package com.fathom.lib.common.model.formula;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConstantNode extends BaseNode implements Serializable {

  @Serial private static final long serialVersionUID = -2420534224950687696L;

  @NotNull(groups = {Create.class})
  private Integer value;

  public ConstantNode() {
    super(NodeTypes.CONSTANT);
  }

  public ConstantNode(Long id, Double xAxisPosition, Double yAxisPosition, Integer value) {
    super(NodeTypes.CONSTANT, id, xAxisPosition, yAxisPosition);
    this.value = value;
  }
}
