package com.fathom.lib.common.model.frame.meta.type;

import static com.fathom.lib.common.model.doc.ModelSwaggerConstants.*;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fathom.lib.common.model.validation.group.Create;
import com.fathom.lib.common.model.validation.group.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = MODEL_RESTRICTION_CLASS)
public class Restriction implements Serializable {

  @Serial private static final long serialVersionUID = 3094544914477505799L;

  @NotBlank(groups = {Create.class, Update.class})
  @Schema(description = MODEL_RESTRICTION_VALUE_FIELD)
  private String value;

  @NotBlank(groups = {Create.class, Update.class})
  @Schema(description = MODEL_RESTRICTION_ALIAS_FIELD)
  private String alias;
}
