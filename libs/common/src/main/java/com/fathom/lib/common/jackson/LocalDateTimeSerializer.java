package com.fathom.lib.common.jackson;

import static com.fathom.lib.common.jackson.DatePatterns.OUTPUT_DATE_PATTERN_ISO;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import java.io.IOException;
import java.io.Serial;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class LocalDateTimeSerializer extends StdSerializer<LocalDateTime> {

  @Serial private static final long serialVersionUID = -1171334073345685304L;

  public LocalDateTimeSerializer() {
    super(LocalDateTime.class);
  }

  @Override
  public void serialize(
      LocalDateTime localDateTime,
      JsonGenerator jsonGenerator,
      SerializerProvider serializerProvider)
      throws IOException {
    jsonGenerator.writeString(
        localDateTime.format(DateTimeFormatter.ofPattern(OUTPUT_DATE_PATTERN_ISO)));
  }
}
