package com.fathom.lib.common.jackson;

import static com.fathom.lib.common.jackson.DatePatterns.INPUT_DATE_PATTERN_ISO_WITHOUT_Z;
import static com.fathom.lib.common.jackson.DatePatterns.INPUT_DATE_PATTERN_ISO_WITH_Z;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import java.io.IOException;
import java.io.Serial;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class LocalDateTimeDeserializer extends StdDeserializer<LocalDateTime> {

  @Serial private static final long serialVersionUID = 2344543277859884057L;

  private static final List<DateTimeFormatter> formatters = new ArrayList<>();

  static {
    formatters.add(DateTimeFormatter.ofPattern(INPUT_DATE_PATTERN_ISO_WITH_Z));
    formatters.add(DateTimeFormatter.ofPattern(INPUT_DATE_PATTERN_ISO_WITHOUT_Z));
  }

  public LocalDateTimeDeserializer() {
    super(LocalDateTime.class);
  }

  @Override
  public LocalDateTime deserialize(
      JsonParser jsonParser, DeserializationContext deserializationContext)
      throws IOException, JsonProcessingException {
    String localDateString = jsonParser.getText();

    if (Objects.nonNull(localDateString) && !localDateString.trim().isEmpty()) {
      LocalDateTime parsedLocalDateTime = null;

      for (DateTimeFormatter formatter : formatters) {
        try {
          parsedLocalDateTime = LocalDateTime.parse(localDateString, formatter);
        } catch (DateTimeParseException ex) {
          // If can`t parse with current formatter just skip and go to the next iteration.
        }
      }

      if (Objects.isNull(parsedLocalDateTime)) {
        throw new IllegalArgumentException(
            "Can`t parse date '%s'. Unknown format".formatted(localDateString));
      }

      return parsedLocalDateTime;
    } else {
      throw new IllegalArgumentException("Date value is null, or empty string.");
    }
  }
}
