package com.fathom.lib.common.frn;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.Serializable;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum ServiceName implements Serializable {
  ASSET_MANAGER("asset_manager"),
  ALARM("alarm"),
  DIAGNOSTIC("diagnostic"),
  CALCULATION("calculation"),
  MAP("map"),
  <PERSON><PERSON><PERSON>RIAN("historian"),
  LOGICAL_TREE("logical-tree"),
  HEATMAP_SERVICE("heatmap"),
  WELL_TESTS("welltests"),
  HISTORICAL_RESERVOIR_PRESSURE("historicalreservoirpressure");

  private static final Map<String, ServiceName> VALUES_MAP =
      Stream.of(ServiceName.values()).collect(Collectors.toMap(s -> s.value, Function.identity()));

  private String value;

  ServiceName(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return value;
  }

  @JsonCreator
  public static ServiceName fromString(String s) {
    return Optional.ofNullable(VALUES_MAP.get(s.trim().toLowerCase()))
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    ("%s is invalid 'ServiceName' value. "
                            + "Valid values are : %s. (Case insensitive)")
                        .formatted(s, VALUES_MAP.values())));
  }
}
