{"meta": {"metaType": "ArrayMeta", "metaPath": "0", "description": "CASING PROFILE", "meta": {"metaType": "TupleMeta", "metaPath": "0-0", "description": "<PERSON><PERSON>", "metas": [{"metaType": "ValueMeta", "metaPath": "0-0-0", "description": "Casing Selection", "sourceType": "regular", "contentType": "number", "unit": "None:None", "defaultValue": "", "restrictions": [], "selectionType": "dropdown", "validators": []}, {"metaType": "ValueMeta", "metaPath": "0-0-1", "description": "Start Point Measured Depth", "sourceType": "regular", "contentType": "number", "unit": "Length:ft", "defaultValue": "", "restrictions": [], "selectionType": "dropdown", "validators": []}, {"metaType": "ValueMeta", "metaPath": "0-0-2", "description": "End Point Measured Depth", "sourceType": "regular", "contentType": "number", "unit": "Length:ft", "defaultValue": "", "restrictions": [], "selectionType": "dropdown", "validators": []}]}, "length": 0}, "description": "", "name": "casing_profile", "displayName": "CASING PROFILE", "source": "userinput", "groupId": "2054", "order": 0, "frn": "frn::asset_manager:422244f4-9ecd-48cc-aca4-f4bb6d900472:/template/2fbe68eb-922b-45bc-a25f-3f776597209f/property/casing_profile"}