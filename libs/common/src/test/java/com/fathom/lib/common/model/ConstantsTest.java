package com.fathom.lib.common.model;

import static com.fathom.lib.common.model.Constants.REG_EXP_FOR_ASSET_STATUS_CONFIG_NAME_VALIDATION;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

class ConstantsTest {

  @ParameterizedTest
  @ValueSource(strings = {"n/a", "status_1", "status.1"})
  void givenValidStatusName_whenMatch_thenMatches(String statusName) {
    assertTrue(statusName.matches(REG_EXP_FOR_ASSET_STATUS_CONFIG_NAME_VALIDATION));
  }

  @ParameterizedTest
  @ValueSource(strings = {"n!@$a", "\\test", "status&1"})
  void givenNameWithForwardSlash_whenValidateAssetStatusName_thenValid(String statusName) {
    assertFalse(statusName.matches(REG_EXP_FOR_ASSET_STATUS_CONFIG_NAME_VALIDATION));
  }
}
