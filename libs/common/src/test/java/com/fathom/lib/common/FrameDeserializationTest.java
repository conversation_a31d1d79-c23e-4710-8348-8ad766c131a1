package com.fathom.lib.common;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fathom.lib.common.jackson.MapperSingleton;
import com.fathom.lib.common.model.frame.Frame;
import com.fathom.lib.common.model.frame.data.PropertyData;
import com.fathom.lib.common.model.frame.data.type.ValueType;
import java.io.IOException;
import java.nio.file.Path;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

public class FrameDeserializationTest {
  Frame frame;

  @BeforeEach
  public void getFrame() throws IOException {
    Path path = Path.of("src", "test", "resources", "frame_example.json");
    ObjectMapper mapper = MapperSingleton.getMapper();
    frame = mapper.readValue(path.toFile(), Frame.class);
  }

  @Test
  public void testSinglePropertyDeserialization() {
    PropertyData propertyData = frame.getPropertyData("vibration");
    assertNotNull(propertyData);
    assertEquals(ValueType.SINGLE_VALUE, propertyData.getValue().getValueType());
    assertEquals("0.09", propertyData.getValue().toString());
  }

  @Test
  public void testArrayPropertyDeserialization() {
    PropertyData propertyData = frame.getPropertyData("motorspeedcoefficient");
    assertNotNull(propertyData);
    assertEquals(ValueType.ARRAY_VALUE, propertyData.getValue().getValueType());
    assertEquals("[0.0, 0.4, 1.0, 2.0, 3.5, 0.7]", propertyData.getValue().toString());
  }

  @Test
  public void testArrayTuplePropertyDeserialization() {
    PropertyData propertyData = frame.getPropertyData("wellboredeviationmdtvdangles");
    assertNotNull(propertyData);
    assertEquals("[{number, number, number}]", propertyData.getMetaAlias());
    assertEquals(ValueType.ARRAY_VALUE, propertyData.getValue().getValueType());
    assertEquals(
        "[[1050.0, 90.0, 1050.0], [1150.0, 85.0, 1149.8], [1250.0, 80.0, 1248.8], [1350.0, 75.0, 1346.3], [1500.0, 70.0, 1489.2], [2000.0, 65.0, 1950.7], [2500.0, 62.0, 2398.0], [3000.0, 63.0, 2841.4], [6000.0, 68.0, 5568.5], [7201.0, 69.0, 6685.8], [8043.0, 75.0, 7485.5], [8500.0, 74.0, 7925.8]]",
        propertyData.getValue().toString());
  }
}
