package com.fathom.lib.common.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fathom.lib.common.frn.FrnPropertyEnricherTest;
import com.fathom.lib.common.jackson.MapperSingleton;
import java.io.IOException;
import java.net.URL;

public final class JsonUtil {
  public static final ObjectMapper MAPPER = MapperSingleton.getMapper();

  private JsonUtil() {}

  public static <T> T load(String jsonFileName, Class<T> tClass) {
    try {
      URL json = FrnPropertyEnricherTest.class.getResource(jsonFileName);
      return MAPPER.readValue(json, tClass);
    } catch (IOException e) {
      throw new RuntimeException("Fail load file=" + jsonFileName, e);
    }
  }
}
