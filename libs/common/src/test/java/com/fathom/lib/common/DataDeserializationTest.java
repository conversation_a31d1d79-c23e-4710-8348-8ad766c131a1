package com.fathom.lib.common;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.core.*;
import com.fathom.lib.common.jackson.LocalDateTimeDeserializer;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;
import org.junit.jupiter.api.Test;

public class DataDeserializationTest {

  public static final String INPUT_DATE_1 = "2014-05-31T23:12:32Z";
  public static final String INPUT_DATE_2 = "2014-05-31T23:12:32";

  @Test
  public void testDateDeserialization() throws IOException {
    LocalDateTimeDeserializer deserializer = new LocalDateTimeDeserializer();

    LocalDateTime localDateTime1 = deserializer.deserialize(parser_with_z, null);
    assertEquals(31, localDateTime1.getDayOfMonth());
    assertEquals(5, localDateTime1.getMonthValue());
    assertEquals(2014, localDateTime1.getYear());
    assertEquals(23, localDateTime1.getHour());
    assertEquals(12, localDateTime1.getMinute());
    assertEquals(32, localDateTime1.getSecond());
  }

  @Test
  public void testDateDeserializationWithoutZ() throws IOException {
    LocalDateTimeDeserializer deserializer = new LocalDateTimeDeserializer();
    LocalDateTime localDateTime1 = deserializer.deserialize(parser_without_z, null);

    LocalDateTime localDateTime2 = deserializer.deserialize(parser_without_z, null);
    assertEquals(localDateTime1.getDayOfMonth(), localDateTime2.getDayOfMonth());
    assertEquals(localDateTime1.getMonthValue(), localDateTime2.getMonthValue());
    assertEquals(localDateTime1.getYear(), localDateTime2.getYear());
    assertEquals(localDateTime1.getHour(), localDateTime2.getHour());
    assertEquals(localDateTime1.getMinute(), localDateTime2.getMinute());
    assertEquals(localDateTime1.getSecond(), localDateTime2.getSecond());
  }

  private static final JsonParser parser_with_z =
      new JsonParser() {
        @Override
        public ObjectCodec getCodec() {
          return null;
        }

        @Override
        public void setCodec(ObjectCodec objectCodec) {}

        @Override
        public Version version() {
          return null;
        }

        @Override
        public void close() throws IOException {}

        @Override
        public boolean isClosed() {
          return false;
        }

        @Override
        public JsonStreamContext getParsingContext() {
          return null;
        }

        @Override
        public JsonLocation getTokenLocation() {
          return null;
        }

        @Override
        public JsonLocation getCurrentLocation() {
          return null;
        }

        @Override
        public JsonToken nextToken() throws IOException {
          return null;
        }

        @Override
        public JsonToken nextValue() throws IOException {
          return null;
        }

        @Override
        public JsonParser skipChildren() throws IOException {
          return null;
        }

        @Override
        public JsonToken getCurrentToken() {
          return null;
        }

        @Override
        public int getCurrentTokenId() {
          return 0;
        }

        @Override
        public boolean hasCurrentToken() {
          return false;
        }

        @Override
        public boolean hasTokenId(int i) {
          return false;
        }

        @Override
        public boolean hasToken(JsonToken jsonToken) {
          return false;
        }

        @Override
        public void clearCurrentToken() {}

        @Override
        public JsonToken getLastClearedToken() {
          return null;
        }

        @Override
        public void overrideCurrentName(String s) {}

        @Override
        public String getCurrentName() throws IOException {
          return null;
        }

        @Override
        public String getText() throws IOException {
          return INPUT_DATE_1;
        }

        @Override
        public char[] getTextCharacters() throws IOException {
          return new char[0];
        }

        @Override
        public int getTextLength() throws IOException {
          return 0;
        }

        @Override
        public int getTextOffset() throws IOException {
          return 0;
        }

        @Override
        public boolean hasTextCharacters() {
          return false;
        }

        @Override
        public Number getNumberValue() throws IOException {
          return null;
        }

        @Override
        public NumberType getNumberType() throws IOException {
          return null;
        }

        @Override
        public int getIntValue() throws IOException {
          return 0;
        }

        @Override
        public long getLongValue() throws IOException {
          return 0;
        }

        @Override
        public BigInteger getBigIntegerValue() throws IOException {
          return null;
        }

        @Override
        public float getFloatValue() throws IOException {
          return 0;
        }

        @Override
        public double getDoubleValue() throws IOException {
          return 0;
        }

        @Override
        public BigDecimal getDecimalValue() throws IOException {
          return null;
        }

        @Override
        public byte[] getBinaryValue(Base64Variant base64Variant) throws IOException {
          return new byte[0];
        }

        @Override
        public String getValueAsString(String s) throws IOException {
          return INPUT_DATE_1;
        }
      };
  private static final JsonParser parser_without_z =
      new JsonParser() {
        @Override
        public ObjectCodec getCodec() {
          return null;
        }

        @Override
        public void setCodec(ObjectCodec objectCodec) {}

        @Override
        public Version version() {
          return null;
        }

        @Override
        public void close() throws IOException {}

        @Override
        public boolean isClosed() {
          return false;
        }

        @Override
        public JsonStreamContext getParsingContext() {
          return null;
        }

        @Override
        public JsonLocation getTokenLocation() {
          return null;
        }

        @Override
        public JsonLocation getCurrentLocation() {
          return null;
        }

        @Override
        public JsonToken nextToken() throws IOException {
          return null;
        }

        @Override
        public JsonToken nextValue() throws IOException {
          return null;
        }

        @Override
        public JsonParser skipChildren() throws IOException {
          return null;
        }

        @Override
        public JsonToken getCurrentToken() {
          return null;
        }

        @Override
        public int getCurrentTokenId() {
          return 0;
        }

        @Override
        public boolean hasCurrentToken() {
          return false;
        }

        @Override
        public boolean hasTokenId(int i) {
          return false;
        }

        @Override
        public boolean hasToken(JsonToken jsonToken) {
          return false;
        }

        @Override
        public void clearCurrentToken() {}

        @Override
        public JsonToken getLastClearedToken() {
          return null;
        }

        @Override
        public void overrideCurrentName(String s) {}

        @Override
        public String getCurrentName() throws IOException {
          return null;
        }

        @Override
        public String getText() throws IOException {
          return INPUT_DATE_2;
        }

        @Override
        public char[] getTextCharacters() throws IOException {
          return new char[0];
        }

        @Override
        public int getTextLength() throws IOException {
          return 0;
        }

        @Override
        public int getTextOffset() throws IOException {
          return 0;
        }

        @Override
        public boolean hasTextCharacters() {
          return false;
        }

        @Override
        public Number getNumberValue() throws IOException {
          return null;
        }

        @Override
        public NumberType getNumberType() throws IOException {
          return null;
        }

        @Override
        public int getIntValue() throws IOException {
          return 0;
        }

        @Override
        public long getLongValue() throws IOException {
          return 0;
        }

        @Override
        public BigInteger getBigIntegerValue() throws IOException {
          return null;
        }

        @Override
        public float getFloatValue() throws IOException {
          return 0;
        }

        @Override
        public double getDoubleValue() throws IOException {
          return 0;
        }

        @Override
        public BigDecimal getDecimalValue() throws IOException {
          return null;
        }

        @Override
        public byte[] getBinaryValue(Base64Variant base64Variant) throws IOException {
          return new byte[0];
        }

        @Override
        public String getValueAsString(String s) throws IOException {
          return INPUT_DATE_2;
        }
      };
}
