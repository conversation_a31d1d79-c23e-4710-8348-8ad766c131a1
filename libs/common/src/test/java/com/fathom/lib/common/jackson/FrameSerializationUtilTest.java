package com.fathom.lib.common.jackson;

import static com.fathom.lib.common.exception.ExMessage.INVALID_ARRAY_DEFINITION;
import static com.fathom.lib.common.jackson.FrameSerializationUtil.populateValues;
import static com.fathom.lib.common.jackson.FrameSerializationUtil.populateValuesNonInitArray;
import static com.fathom.lib.common.util.JsonUtil.MAPPER;
import static com.fathom.lib.common.util.JsonUtil.load;
import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.DoubleNode;
import com.fathom.lib.common.exception.CommonLibException;
import com.fathom.lib.common.model.frame.data.type.ArrayValue;
import com.fathom.lib.common.model.frame.data.type.SingleValue;
import com.fathom.lib.common.model.frame.data.type.TupleValue;
import com.fathom.lib.common.model.frame.meta.PropertyMeta;
import com.fathom.lib.common.model.frame.meta.type.ContentType;
import java.util.Arrays;
import java.util.stream.Stream;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class FrameSerializationUtilTest {

  private static Stream<Arguments> aliasData() {
    return Stream.of(
        Arguments.of("/meta/casing_profile_meta.json", "[{number, number, number}]"),
        Arguments.of("/meta/duration_meta.json", "string"));
  }

  private static Stream<Arguments> descriptionData() {
    return Stream.of(
        Arguments.of(
            "/meta/casing_profile_meta.json",
            "[{\"Casing Selection\", \"Start Point Measured Depth\", \"End Point Measured Depth\"}]"),
        Arguments.of("/meta/duration_meta.json", "\"Duration\""));
  }

  @ParameterizedTest
  @MethodSource("aliasData")
  void testGetMetaAlias(String jsonMetaFileName, String expectedResult) {
    PropertyMeta casingProfileMeta = load(jsonMetaFileName, PropertyMeta.class);

    String alias = FrameSerializationUtil.generateMetaAlias(casingProfileMeta.getMeta());
    assertEquals(expectedResult, alias);
  }

  @ParameterizedTest
  @MethodSource("descriptionData")
  void testGetNestedMetaDescription(String jsonMetaFileName, String expectedResult) {
    PropertyMeta casingProfileMeta = load(jsonMetaFileName, PropertyMeta.class);

    String actual =
        FrameSerializationUtil.generateNestedMetaDescription(casingProfileMeta.getMeta());
    assertEquals(expectedResult, actual);
  }

  @Test
  void testPopulateValues_whenArrayValueHasMultipleValues() {
    ArrayNode arrayNode = getTwoElementArray();
    ArrayValue arrayValue = getArrayWithTwoTuples();

    CommonLibException ex =
        assertThrows(CommonLibException.class, () -> populateValues(arrayNode, arrayValue));
    assertEquals(INVALID_ARRAY_DEFINITION, ex.getMessage());
  }

  @Test
  void testPopulateValuesNonInitArray_whenArrayValueHasMultipleValues() {
    ArrayNode arrayNode = getTwoElementArray();
    ArrayValue arrayValue = getArrayWithTwoTuples();

    populateValuesNonInitArray(arrayNode, arrayValue);

    TupleValue firstTuple = (TupleValue) arrayValue.getValue().get(0);
    assertAll(
        "Populated values",
        () -> assertEquals(2, arrayValue.getValue().size()),
        () -> assertEquals(20d, ((SingleValue) firstTuple.getValue().get(0)).getValue()),
        () -> assertEquals(10d, ((SingleValue) firstTuple.getValue().get(1)).getValue()));
  }

  private ArrayNode getTwoElementArray() {
    ArrayNode firstValue = MAPPER.createArrayNode();
    firstValue.add(new DoubleNode(20d));
    firstValue.add(new DoubleNode(10d));
    ArrayNode secondValue = MAPPER.createArrayNode();
    secondValue.add(new DoubleNode(30d));
    secondValue.add(new DoubleNode(40d));

    ArrayNode arrayNode = MAPPER.createArrayNode();
    arrayNode.add(firstValue);
    arrayNode.add(secondValue);
    return arrayNode;
  }

  private ArrayValue getArrayWithTwoTuples() {
    TupleValue firstTuple = new TupleValue(Arrays.asList(single(100d), single(200d)));
    TupleValue secondTuple = new TupleValue(Arrays.asList(single(300d), single(400d)));
    return new ArrayValue(Arrays.asList(firstTuple, secondTuple));
  }

  private SingleValue single(double val) {
    SingleValue singleValue = new SingleValue(100d);
    singleValue.setType(ContentType.NUMBER);
    singleValue.setUnit("None:None");
    return singleValue;
  }
}
