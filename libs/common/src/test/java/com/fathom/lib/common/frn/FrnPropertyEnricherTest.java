package com.fathom.lib.common.frn;

import static com.fathom.lib.common.util.JsonUtil.MAPPER;
import static com.fathom.lib.common.util.JsonUtil.load;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.params.provider.Arguments.arguments;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fathom.lib.common.frn.aware.FrnAware;
import com.fathom.lib.common.frn.enricher.FrnEnricher;
import com.fathom.lib.common.frn.enricher.NestedFrnEnricher;
import com.fathom.lib.common.frn.enricher.general.AssetDTOFrnEnricher;
import com.fathom.lib.common.frn.enricher.general.FrameFrnEnricher;
import com.fathom.lib.common.frn.enricher.general.PropertyMetaFrnEnricher;
import com.fathom.lib.common.model.asset.AssetDTO;
import com.fathom.lib.common.model.frame.Frame;
import com.fathom.lib.common.model.frame.meta.PropertyMeta;
import jakarta.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Stream;
import lombok.Data;
import org.json.JSONException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.skyscreamer.jsonassert.JSONAssert;
import org.skyscreamer.jsonassert.JSONCompareMode;

public class FrnPropertyEnricherTest {

  private static final String TEST_ASSET_FILE_JSON = "/enricher/asset_example.json";
  private static final String TEST_ASSET_WITH_FRNS_FILE_JSON =
      "/enricher/asset_example_with_frn.json";
  private static final String TEST_ASSET_WITH_FRNS_AND_NULLS_FILE_JSON =
      "/enricher/asset_example_with_frn_and_null_fields.json";
  private static final String TEST_FRAME_FILE_JSON = "/frame_example.json";
  private static final String TEST_FRAME_WITH_FRNS_FILE_JSON =
      "/enricher/frame_example_with_frn.json";
  private static final String TEST_SERVICE_NAME = "asset_manager";
  private static final String TEST_TEST_DTO_WITH_FRN_INCLUDES_APP_NAME =
      "/enricher/test_dto_with_frn.json";
  private static final String APP_NAME = "wells";
  private static final String SERVICE = "wells_manager";
  private static final String ROOT_RESOURCE = "well_test";

  private static Stream<Arguments> enricherData() {
    return Stream.of(
        arguments(
            FrameFrnEnricher.build(TEST_SERVICE_NAME, "frame"),
            load(TEST_FRAME_FILE_JSON, Frame.class),
            load(TEST_FRAME_WITH_FRNS_FILE_JSON, Frame.class)),
        arguments(
            AssetDTOFrnEnricher.build(TEST_SERVICE_NAME, "asset"),
            load(TEST_ASSET_FILE_JSON, AssetDTO.class),
            load(TEST_ASSET_WITH_FRNS_FILE_JSON, AssetDTO.class)));
  }

  @ParameterizedTest
  @MethodSource("enricherData")
  void givenFrnAwareDTO_whenSetFrn_thenReturnDTOWithFrns(
      FrnEnricher<FrnAware> frnEnricher, FrnAware dtoToEnrich, FrnAware expectedDto)
      throws JsonProcessingException, JSONException {

    frnEnricher.setFrn(dtoToEnrich);

    String actualJson = MAPPER.writeValueAsString(dtoToEnrich);
    String expectedJson = MAPPER.writeValueAsString(expectedDto);
    JSONAssert.assertEquals(expectedJson, actualJson, JSONCompareMode.STRICT);
  }

  @Test
  public void
      givenAssetDTOWithTemplateDTONullAndDataNull_whenSetFrn_thenReturnAssetAndNestedFieldsWithFrns()
          throws JsonProcessingException, JSONException {
    AssetDTO assetDTO = loadAssetDTOWithNullTemplateAndInitialData();

    FrnEnricher<AssetDTO> assetDtoEnricher = AssetDTOFrnEnricher.build(TEST_SERVICE_NAME, "asset");
    assetDtoEnricher.setFrn(assetDTO);

    String actualJson = MAPPER.writeValueAsString(assetDTO);
    String expectedJson =
        MAPPER.writeValueAsString(load(TEST_ASSET_WITH_FRNS_AND_NULLS_FILE_JSON, AssetDTO.class));
    JSONAssert.assertEquals(expectedJson, actualJson, JSONCompareMode.STRICT);
  }

  @Test
  public void givenPropertyMetaMap_whenSetFrn_thenReturnMetaMapWithFrns() {
    String rootFrn = "frn::asset_manager:org123:/asset/asset123";
    Map<String, PropertyMeta> propertyMetaMap = generateMeta();

    PropertyMetaFrnEnricher metaFrnEnricher = PropertyMetaFrnEnricher.build();
    metaFrnEnricher.setFrn(propertyMetaMap, rootFrn);

    String actualMeta5Frn = propertyMetaMap.get("5").getFrn();
    String expectedMeta5Frn =
        "frn::asset_manager:org123:/asset/asset123/property/tandem_motor_shaft_bhp_limit";

    String actualMeta18Frn = propertyMetaMap.get("18").getFrn();
    String expectedMeta18Frn = "frn::asset_manager:org123:/asset/asset123/property/calculated_bhp1";

    assertEquals(expectedMeta5Frn, actualMeta5Frn);
    assertEquals(actualMeta18Frn, expectedMeta18Frn);
  }

  @Test
  public void givenTestDTO_whenSetFrn_thenReturnWellTestWithFrnIncludesWellsAsAppName()
      throws JsonProcessingException, JSONException, CloneNotSupportedException {
    TestDto dto = new TestDto();
    dto.setOrgId(UUID.fromString("1f952560-ca8b-11eb-97b0-a349d42b2355"));
    dto.setName("well_test_name_1");
    TestDto nestedEnricherDto = (TestDto) dto.clone();

    FrnEnricher<TestDto> enricher = createEnricher();
    NestedFrnEnricher<TestDto> nestedEnricher = createNestedEnricher();

    enricher.setFrn(dto);
    nestedEnricher.setFrn(nestedEnricherDto);

    String actualDto = MAPPER.writeValueAsString(dto);
    String actualNestedEnricherDto = MAPPER.writeValueAsString(nestedEnricherDto);
    String expectedJson =
        MAPPER.writeValueAsString(load(TEST_TEST_DTO_WITH_FRN_INCLUDES_APP_NAME, TestDto.class));
    JSONAssert.assertEquals(expectedJson, actualDto, JSONCompareMode.STRICT);
    JSONAssert.assertEquals(expectedJson, actualNestedEnricherDto, JSONCompareMode.STRICT);
  }

  private FrnEnricher<TestDto> createEnricher() {
    return FrnEnricher.build(APP_NAME, SERVICE, ROOT_RESOURCE);
  }

  private NestedFrnEnricher<TestDto> createNestedEnricher() {
    return new NestedFrnEnricher<TestDto>(APP_NAME, SERVICE, ROOT_RESOURCE) {
      @Override
      protected void setNestedFrn(TestDto target, String rootNodeFrn) {}
    };
  }

  private AssetDTO loadAssetDTOWithNullTemplateAndInitialData() {
    AssetDTO assetDTO = load(TEST_ASSET_FILE_JSON, AssetDTO.class);
    assetDTO.setInitialData(null);
    assetDTO.setTemplate(null);
    return assetDTO;
  }

  private Map<String, PropertyMeta> generateMeta() {
    Map<String, PropertyMeta> propertyMeta = new HashMap<>();
    PropertyMeta meta5 = new PropertyMeta();
    meta5.setName("tandem_motor_shaft_bhp_limit");

    PropertyMeta meta18 = new PropertyMeta();
    meta18.setName("calculated_bhp1");

    propertyMeta.put("5", meta5);
    propertyMeta.put("18", meta18);
    return propertyMeta;
  }

  @Data
  private static class TestDto implements FrnAware, Cloneable {
    private UUID orgId;
    private String frn;
    private String name;

    @Override
    public @NotNull String getResourceId() {
      return name;
    }

    @Override
    protected Object clone() throws CloneNotSupportedException {
      return super.clone();
    }
  }
}
