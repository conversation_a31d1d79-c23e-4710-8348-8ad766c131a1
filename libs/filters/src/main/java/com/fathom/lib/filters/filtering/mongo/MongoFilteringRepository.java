package com.fathom.lib.filters.filtering.mongo;

import com.fathom.lib.filters.model.Filter;
import com.fathom.lib.filters.model.SortOption;
import java.util.List;
import org.springframework.data.mongodb.core.query.Query;

public interface MongoFilteringRepository<T> {

  void addFiltersToQuery(Query query, List<Filter> filters);

  void addFiltersAndSortingToQuery(Query query, List<Filter> filters, List<SortOption> sortOptions);
}
