package com.fathom.lib.filters.filtering.jpa.impl;

import static com.fathom.lib.filters.filtering.FilterUtils.parseBooleanFromObject;
import static com.fathom.lib.filters.filtering.FilterUtils.parseLocalDateTimeFromObject;

import com.fathom.lib.filters.filtering.jpa.JpaFilteringRepository;
import com.fathom.lib.filters.model.*;
import jakarta.persistence.criteria.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import org.springframework.util.CollectionUtils;

public class JpaFilteringRepositoryImpl<T> implements JpaFilteringRepository<T> {

  private static final String FIELD_PATH_SEPARATOR = "\\.";

  @Override
  public void addFiltersToQuery(
      CriteriaBuilder criteriaBuilder, CriteriaQuery<T> query, List<Filter> filters) {
    Root<T> root = query.from(query.getResultType());

    // List of predicates
    List<Predicate> predicates = new ArrayList<>();

    if (!CollectionUtils.isEmpty(filters)) {
      for (Filter filter : filters) {
        predicates.add(addPredicate(criteriaBuilder, root, filter));
      }
    }

    query.select(root).where(predicates.toArray(new Predicate[] {}));
  }

  @Override
  public void addFiltersAndSortingToQuery(
      CriteriaBuilder criteriaBuilder,
      CriteriaQuery<T> query,
      List<Filter> filters,
      List<SortOption> sortOptions) {
    addFiltersToQuery(criteriaBuilder, query, filters);

    Root<T> root = query.from(query.getResultType());

    List<Order> orders = new ArrayList<>();

    for (SortOption sortOption : sortOptions) {
      Order order =
          SortOrder.ASC.equals(sortOption.getSortOrder())
              ? criteriaBuilder.asc(createPathObjectFromPathString(root, sortOption.getField()))
              : criteriaBuilder.desc(createPathObjectFromPathString(root, sortOption.getField()));

      orders.add(order);
    }

    query.orderBy(orders);
  }

  @SuppressWarnings("unchecked")
  private Predicate addPredicate(CriteriaBuilder criteriaBuilder, Root<T> root, Filter filter) {

    // Adding criteria depending on filter type
    switch (filter.getFilterType()) {
      case EQUAL:
        EqualFilter equalFilter = (EqualFilter) filter;

        switch (filter.getValueType()) {
          case STRING, NUMBER:
            return criteriaBuilder.equal(
                createPathObjectFromPathString(root, equalFilter.getPropertyName()),
                equalFilter.getValue());
          case BOOLEAN:
            Boolean booleanValue = parseBooleanFromObject(equalFilter.getValue());
            return criteriaBuilder.equal(
                createPathObjectFromPathString(root, equalFilter.getPropertyName()), booleanValue);
          case DATE:
            LocalDateTime localDateTimeValue = parseLocalDateTimeFromObject(equalFilter.getValue());
            return criteriaBuilder.equal(
                createPathObjectFromPathString(root, equalFilter.getPropertyName()),
                localDateTimeValue);
          default:
            break;
        }
        break;
      case LIKE:
        LikeFilter likeFilter = (LikeFilter) filter;
        String value = "%".concat(likeFilter.getValue()).concat("%");

        return criteriaBuilder.like(
            createPathObjectFromPathString(root, likeFilter.getPropertyName()), value);
      case IN:
        InFilter inFilter = (InFilter) filter;
        return createPathObjectFromPathString(root, inFilter.getPropertyName())
            .in(inFilter.getValues());
      case GREATER:
        GreaterFilter greaterFilter = (GreaterFilter) filter;

        switch (greaterFilter.getValueType()) {
          case NUMBER:
            Double greater = Double.valueOf(greaterFilter.getValue().toString());

            return criteriaBuilder.greaterThanOrEqualTo(
                createPathObjectFromPathString(root, greaterFilter.getPropertyName()), greater);
          case DATE:
            LocalDateTime localDateTimeValue =
                parseLocalDateTimeFromObject(greaterFilter.getValue().toString());

            return criteriaBuilder.greaterThanOrEqualTo(
                createPathObjectFromPathString(root, greaterFilter.getPropertyName()),
                localDateTimeValue);
          default:
            break;
        }
        break;
      case LESS:
        LessFilter lessFilter = (LessFilter) filter;

        switch (lessFilter.getValueType()) {
          case NUMBER:
            Double less = Double.valueOf(lessFilter.getValue().toString());

            return criteriaBuilder.lessThanOrEqualTo(
                createPathObjectFromPathString(root, lessFilter.getPropertyName()), less);
          case DATE:
            LocalDateTime localDateTimeValue = parseLocalDateTimeFromObject(lessFilter.getValue());

            return criteriaBuilder.lessThanOrEqualTo(
                createPathObjectFromPathString(root, lessFilter.getPropertyName()),
                localDateTimeValue);
          default:
            break;
        }
        break;
      case BETWEEN:
        BetweenFilter btwFilter = (BetweenFilter) filter;

        switch (btwFilter.getValueType()) {
          case NUMBER:
            Double greater = Double.valueOf(btwFilter.getGreater().toString());
            Double less = Double.valueOf(btwFilter.getLess().toString());

            return criteriaBuilder.between(
                createPathObjectFromPathString(root, btwFilter.getPropertyName()), greater, less);
          case DATE:
            LocalDateTime startDate = parseLocalDateTimeFromObject(btwFilter.getGreater());
            LocalDateTime endDate = parseLocalDateTimeFromObject(btwFilter.getLess());

            return criteriaBuilder.between(
                createPathObjectFromPathString(root, btwFilter.getPropertyName()),
                startDate,
                endDate);
          default:
            break;
        }
        break;
      default:
        break;
    }
    return null;
  }

  private Path createPathObjectFromPathString(Root<T> root, String fieldPath) {
    List<String> fieldsFromPath =
        Arrays.stream(fieldPath.split(FIELD_PATH_SEPARATOR)).map(String::trim).toList();

    Path path = null;

    for (String fieldName : fieldsFromPath) {
      if (Objects.isNull(path)) {
        path = root.get(fieldName);
      } else {
        path = path.get(fieldName);
      }
    }

    return path;
  }
}
