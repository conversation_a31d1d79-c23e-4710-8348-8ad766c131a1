package com.fathom.lib.filters.filtering.mongo.impl;

import static com.fathom.lib.filters.filtering.FilterUtils.parseBooleanFromObject;
import static com.fathom.lib.filters.filtering.FilterUtils.parseLocalDateTimeFromObject;

import com.fathom.lib.filters.filtering.mongo.MongoFilteringRepository;
import com.fathom.lib.filters.model.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;

@Slf4j
public class MongoFilteringRepositoryImpl<T> implements MongoFilteringRepository<T> {

  @Override
  public void addFiltersToQuery(Query query, List<Filter> filters) {
    if (!CollectionUtils.isEmpty(filters)) {
      for (Filter filter : filters) {
        addCriteria(query, filter);
      }
    }
  }

  @Override
  public void addFiltersAndSortingToQuery(
      Query query, List<Filter> filters, List<SortOption> sortOptions) {
    addFiltersToQuery(query, filters);

    List<Sort.Order> orders = new ArrayList<>();

    for (SortOption sortOption : sortOptions) {
      Sort.Direction direction =
          SortOrder.ASC.equals(sortOption.getSortOrder())
              ? Sort.Direction.ASC
              : Sort.Direction.DESC;

      orders.add(new Sort.Order(direction, sortOption.getField()));
    }

    query.with(Sort.by(orders));
  }

  private void addCriteria(Query query, Filter filter) {
    // Adding criteria depending on filter type
    switch (filter.getFilterType()) {
      case EQUAL:
        EqualFilter equalFilter = (EqualFilter) filter;

        switch (equalFilter.getValueType()) {
          case STRING, NUMBER:
            query.addCriteria(
                Criteria.where(equalFilter.getPropertyName()).is(equalFilter.getValue()));
            break;
          case BOOLEAN:
            Boolean booleanValue = parseBooleanFromObject(equalFilter.getValue());
            query.addCriteria(Criteria.where(equalFilter.getPropertyName()).is(booleanValue));
            break;
          case DATE:
            LocalDateTime localDateTimeValue = parseLocalDateTimeFromObject(equalFilter.getValue());
            query.addCriteria(Criteria.where(equalFilter.getPropertyName()).is(localDateTimeValue));
            break;
          default:
            break;
        }
        break;
      case LIKE:
        LikeFilter likeFilter = (LikeFilter) filter;
        query.addCriteria(
            Criteria.where(likeFilter.getPropertyName()).regex(likeFilter.getValue()));
        break;
      case IN:
        InFilter inFilter = (InFilter) filter;
        query.addCriteria(Criteria.where(inFilter.getPropertyName()).in(inFilter.getValues()));
        break;
      case GREATER:
        GreaterFilter greaterFilter = (GreaterFilter) filter;

        switch (greaterFilter.getValueType()) {
          case NUMBER:
            query.addCriteria(
                Criteria.where(greaterFilter.getPropertyName()).gte(greaterFilter.getValue()));
            break;
          case DATE:
            LocalDateTime localDateTimeValue =
                parseLocalDateTimeFromObject(greaterFilter.getValue().toString());
            query.addCriteria(
                Criteria.where(greaterFilter.getPropertyName()).gte(localDateTimeValue));
            break;
          default:
            break;
        }

        break;
      case LESS:
        LessFilter lessFilter = (LessFilter) filter;

        switch (lessFilter.getValueType()) {
          case NUMBER:
            query.addCriteria(
                Criteria.where(lessFilter.getPropertyName()).lte(lessFilter.getValue()));
            break;
          case DATE:
            LocalDateTime localDateTimeValue = parseLocalDateTimeFromObject(lessFilter.getValue());
            query.addCriteria(Criteria.where(lessFilter.getPropertyName()).lte(localDateTimeValue));
            break;
          default:
            break;
        }

        break;
      case BETWEEN:
        BetweenFilter btwFilter = (BetweenFilter) filter;

        switch (btwFilter.getValueType()) {
          case NUMBER:
            query.addCriteria(
                Criteria.where(btwFilter.getPropertyName())
                    .gte(btwFilter.getGreater())
                    .lte(btwFilter.getLess()));
            break;
          case DATE:
            LocalDateTime startDate = parseLocalDateTimeFromObject(btwFilter.getGreater());
            LocalDateTime endDate = parseLocalDateTimeFromObject(btwFilter.getLess());

            query.addCriteria(
                Criteria.where(btwFilter.getPropertyName()).gte(startDate).lte(endDate));
            break;
          default:
            break;
        }
        break;
      default:
        break;
    }
  }
}
