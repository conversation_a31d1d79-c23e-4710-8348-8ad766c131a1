package com.fathom.lib.filters.filtering;

import static com.fathom.lib.filters.model.FilterType.*;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fathom.lib.common.jackson.MapperSingleton;
import com.fathom.lib.filters.model.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;

@Slf4j
public class FilterUtils {

  private static final ObjectMapper objectMapper = MapperSingleton.getMapper();

  public static final String FILTER_MAP_ARRAY_SEPARATOR = ",";
  public static final String FILTER_MAP_BETWEEN_SEPARATOR = "_";

  public static LocalDateTime parseLocalDateTimeFromObject(Object object) {
    if (object instanceof LocalDateTime time) {
      return time;
    }

    String stringValue = object.toString();

    LocalDateTime localDateTime = null;

    StringBuilder sb = new StringBuilder("\"").append(stringValue).append("\"");

    try {
      localDateTime = objectMapper.readValue(sb.toString(), new TypeReference<LocalDateTime>() {});
    } catch (Exception e) {
      log.trace("Can`t parse LocalDateTime from String. '{}'", stringValue, e);
    }

    return localDateTime;
  }

  public static Boolean parseBooleanFromObject(Object object) {
    if (object instanceof Boolean boolean1) {
      return boolean1;
    }

    String stringValue = object.toString();

    if (stringValue.equalsIgnoreCase("true") || stringValue.equalsIgnoreCase("false")) {
      return Boolean.valueOf(stringValue);
    }

    return null;
  }

  public static Double parseNumberFromObject(Object object) {
    if (object instanceof Number number) {
      return number.doubleValue();
    }

    String stringValue = object.toString();

    if (NumberUtils.isCreatable(stringValue)) {
      return Double.parseDouble(stringValue);
    }

    return null;
  }

  public static TypeValuePair parseObject(Object object) {
    Boolean booleanValue = parseBooleanFromObject(object);

    if (Objects.nonNull(booleanValue)) {
      return new TypeValuePair(ValueType.BOOLEAN, booleanValue);
    }

    Double numberValue = parseNumberFromObject(object);

    if (Objects.nonNull(numberValue)) {
      return new TypeValuePair(ValueType.NUMBER, numberValue);
    }

    LocalDateTime localDateTimeValue = parseLocalDateTimeFromObject(object);

    if (Objects.nonNull(localDateTimeValue)) {
      return new TypeValuePair(ValueType.DATE, localDateTimeValue);
    }

    if (Objects.nonNull(object)) {
      return new TypeValuePair(ValueType.STRING, object);
    }

    return null;
  }

  public static List<Filter> filterMapToList(Map<String, String> filterMap) {
    List<Filter> filterList = new ArrayList<>();

    for (Map.Entry<String, String> entry : filterMap.entrySet()) {
      try {
        Pattern p = Pattern.compile("\\[(.*?)\\]");
        Matcher m = p.matcher(entry.getKey());

        String propertyName = null;
        FilterType filterType = null;

        for (int i = 0; i < 2; i++) {
          if (i == 0 && m.find()) {
            propertyName = m.group(1);
          } else if (i == 1) {
            if (m.find()) {
              String filterTypeString = m.group(1).toLowerCase();
              filterType = FilterType.fromString(filterTypeString);
            } else {
              filterType = EQUAL;
            }
          }
        }

        if (Objects.nonNull(propertyName) && Objects.nonNull(filterType)) {
          switch (filterType) {
            case EQUAL:
              FilterUtils.TypeValuePair equalCaseTypeValuePair = parseObject(entry.getValue());
              filterList.add(
                  new EqualFilter(
                      equalCaseTypeValuePair.getValueType(),
                      propertyName,
                      equalCaseTypeValuePair.getValue()));
              break;
            case LIKE:
              filterList.add(new LikeFilter(ValueType.STRING, propertyName, entry.getValue()));
              break;
            case IN:
              List<Object> valueList =
                  Arrays.stream(entry.getValue().split(FILTER_MAP_ARRAY_SEPARATOR))
                      .map(String::trim)
                      .collect(Collectors.toList());
              filterList.add(new InFilter(ValueType.STRING, propertyName, valueList));
              break;
            case GREATER:
              FilterUtils.TypeValuePair greaterCaseTypeValuePair = parseObject(entry.getValue());
              filterList.add(
                  new GreaterFilter(
                      greaterCaseTypeValuePair.getValueType(),
                      propertyName,
                      greaterCaseTypeValuePair.getValue()));
              break;
            case LESS:
              FilterUtils.TypeValuePair lessCaseTypeValuePair = parseObject(entry.getValue());
              filterList.add(
                  new LessFilter(
                      lessCaseTypeValuePair.getValueType(),
                      propertyName,
                      lessCaseTypeValuePair.getValue()));
              break;
            case BETWEEN:
              String[] values = entry.getValue().split(FILTER_MAP_BETWEEN_SEPARATOR);

              FilterUtils.TypeValuePair greater = parseObject(values[0]);
              FilterUtils.TypeValuePair less = parseObject(values[1]);

              filterList.add(
                  new BetweenFilter(
                      greater.getValueType(), propertyName, greater.getValue(), less.getValue()));
              break;
          }
        }
      } catch (Exception e) {
        log.warn(e.getMessage());
      }
    }

    return filterList;
  }

  @Getter
  @Setter
  @AllArgsConstructor
  public static class TypeValuePair {
    private final ValueType valueType;
    private final Object value;
  }
}
