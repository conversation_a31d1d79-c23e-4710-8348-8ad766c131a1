package com.fathom.lib.filters.pagination;

import java.util.ArrayList;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

public interface Paged<T> {

  default Page<T> getPage(List<T> entities, Pageable pageable) {
    int total = entities.size();
    int start = (int) pageable.getOffset();
    int end = Math.min((start + pageable.getPageSize()), total);

    List<T> output = new ArrayList<>();

    if (start <= end) {
      output = entities.subList(start, end);
    }

    return new PageImpl<>(output, pageable, total);
  }
}
