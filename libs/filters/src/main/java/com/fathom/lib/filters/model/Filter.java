package com.fathom.lib.filters.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.EXISTING_PROPERTY,
    property = "filterType",
    visible = true)
@JsonSubTypes({
  @JsonSubTypes.Type(name = "equal", value = EqualFilter.class),
  @JsonSubTypes.Type(name = "like", value = LikeFilter.class),
  @JsonSubTypes.Type(name = "in", value = InFilter.class),
  @JsonSubTypes.Type(name = "greater", value = GreaterFilter.class),
  @JsonSubTypes.Type(name = "less", value = LessFilter.class),
  @JsonSubTypes.Type(name = "between", value = BetweenFilter.class)
})
public abstract class Filter implements Serializable {

  @Serial private static final long serialVersionUID = 1559331020433350373L;

  @NotNull private FilterType filterType;

  @NotNull private ValueType valueType;

  @NotBlank private String propertyName;
}
