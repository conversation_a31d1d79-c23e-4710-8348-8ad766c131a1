package com.fathom.lib.filters.filtering.jpa;

import com.fathom.lib.filters.model.Filter;
import com.fathom.lib.filters.model.SortOption;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import java.util.List;

public interface JpaFilteringRepository<T> {

  void addFiltersToQuery(
      CriteriaBuilder criteriaBuilder, CriteriaQuery<T> query, List<Filter> filters);

  public void addFiltersAndSortingToQuery(
      CriteriaBuilder criteriaBuilder,
      CriteriaQuery<T> query,
      List<Filter> filters,
      List<SortOption> sortOptions);
}
