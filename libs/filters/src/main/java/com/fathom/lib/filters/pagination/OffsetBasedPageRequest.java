package com.fathom.lib.filters.pagination;

import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

public class OffsetBasedPageRequest implements Pageable {
  private final int pageSize;
  private final int offset;

  // Constructor could be expanded if sorting is needed
  private Sort sort;

  public OffsetBasedPageRequest(int pageSize, int offset) {
    if (pageSize < 1) {
      throw new IllegalArgumentException("Page size must not be less than one!");
    }
    if (offset < 0) {
      throw new IllegalArgumentException("Offset index must not be less than zero!");
    }
    this.pageSize = pageSize;
    this.offset = offset;
  }

  public OffsetBasedPageRequest(int pageSize, int offset, Sort sort) {
    this(pageSize, offset);
    this.sort = sort;
  }

  @Override
  public int getPageNumber() {
    return offset / pageSize;
  }

  @Override
  public int getPageSize() {
    return pageSize;
  }

  @Override
  public long getOffset() {
    return offset;
  }

  @Override
  public Sort getSort() {
    return sort;
  }

  @Override
  public Pageable next() {
    // Typecast possible because number of entries cannot be bigger than integer (primary key is
    // integer)
    return new OffsetBasedPageRequest(getPageSize(), (int) (getOffset() + getPageSize()));
  }

  public Pageable previous() {
    // The integers are positive. Subtracting does not let them become bigger than integer.
    return hasPrevious()
        ? new OffsetBasedPageRequest(getPageSize(), (int) (getOffset() - getPageSize()))
        : this;
  }

  @Override
  public Pageable previousOrFirst() {
    return hasPrevious() ? previous() : first();
  }

  @Override
  public Pageable first() {
    return new OffsetBasedPageRequest(getPageSize(), 0);
  }

  @Override
  public Pageable withPage(int pageNumber) {
    return null;
  }

  @Override
  public boolean hasPrevious() {
    return offset > pageSize;
  }
}
