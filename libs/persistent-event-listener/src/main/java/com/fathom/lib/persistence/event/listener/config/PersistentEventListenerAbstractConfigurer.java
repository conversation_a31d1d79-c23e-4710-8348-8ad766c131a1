/*
 * Copyright 2021-2022 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.fathom.lib.persistence.event.listener.config;

import com.fathom.lib.common.jackson.MapperSingleton;
import com.fathom.lib.persistence.event.listener.PersistentEventListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.CommonLoggingErrorHandler;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.MessageListener;
import org.springframework.kafka.support.serializer.JsonDeserializer;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(KafkaProperties.class)
public abstract class PersistentEventListenerAbstractConfigurer
    implements ApplicationListener<ApplicationReadyEvent> {

  @Override
  public void onApplicationEvent(ApplicationReadyEvent event) {
    KafkaProperties kafkaProperties = event.getApplicationContext().getBean(KafkaProperties.class);

    Map<String, PersistentEventListener<?>> listeners = new HashMap<>();
    this.registerResourceEventListener(listeners);

    log.debug("Register '{}' persistent event listeners", listeners.size());
    for (Map.Entry<String, PersistentEventListener<?>> e : listeners.entrySet()) {
      String topicName = e.getKey();
      PersistentEventListener<?> housekeeper = e.getValue();

      log.trace(
          "Create kafka message listener: [topic={}, eventListener={}] ",
          topicName,
          housekeeper.getClass().getName());

      ConcurrentMessageListenerContainer<String, ?> container =
          getContainer(kafkaProperties, topicName, housekeeper);

      log.info("Starting container, [props={}]", container.getContainerProperties());
      container.start();
    }
  }

  private <T> ConcurrentMessageListenerContainer<String, T> getContainer(
      KafkaProperties props, String topicName, PersistentEventListener<T> housekeeper) {
    ContainerProperties properties = new ContainerProperties(topicName);
    properties.setAckMode(ContainerProperties.AckMode.RECORD);
    properties.setMessageListener(createListener(housekeeper));

    properties.setGroupId("persistent-event-listener-%s-group".formatted(UUID.randomUUID()));
    properties.setClientId("persistent-event-listener-%s-client".formatted(UUID.randomUUID()));

    ConsumerFactory<String, T> consumerFactory = getConsumerFactory(props, housekeeper.getType());

    ConcurrentMessageListenerContainer<String, T> container =
        new ConcurrentMessageListenerContainer<>(consumerFactory, properties);
    container.setCommonErrorHandler(new CommonLoggingErrorHandler());
    return container;
  }

  private <T> ConsumerFactory<String, T> getConsumerFactory(KafkaProperties props, Class<T> type) {
    DefaultKafkaConsumerFactory<String, T> factory =
        new DefaultKafkaConsumerFactory<>(props.buildConsumerProperties(null));

    factory.setKeyDeserializer(new StringDeserializer());

    JsonDeserializer<T> deserializer = new JsonDeserializer<>(type, MapperSingleton.getMapper());
    deserializer.trustedPackages("com.fathom.*");
    deserializer.setUseTypeHeaders(false);

    factory.setValueDeserializer(deserializer);
    return factory;
  }

  private <T> MessageListener<String, T> createListener(PersistentEventListener<T> eventListener) {
    return record -> {
      String key = record.key();
      log.info(
          "Consume record: [topic={}, partition={}, offset={}, key={}]",
          record.topic(),
          record.partition(),
          record.offset(),
          key);

      T obj = record.value();

      switch (key) {
        case "ENTITY_PERSISTED_EVENT":
          log.trace("On created: {}", obj);
          eventListener.onCreated(obj);
          break;
        case "ENTITY_UPDATED_EVENT":
          log.trace("On updated: {}", obj);
          eventListener.onUpdated(obj);
          break;
        case "ENTITY_DELETED_EVENT":
          log.trace("On deleted: {}", obj);
          eventListener.onDeleted(obj);
          break;
        default:
          log.info("Unknown event: {}", key);
      }
    };
  }

  protected void registerResourceEventListener(Map<String, PersistentEventListener<?>> registry) {}
}
