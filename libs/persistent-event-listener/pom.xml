<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <repositories>
        <repository>
            <id>nexus</id>
            <name>Nexus repository</name>
            <url>https://nexus.fathom-solutions.com/repository/maven-public/</url>
        </repository>
    </repositories>

    <modelVersion>4.0.0</modelVersion>

    <groupId>com.fathom.lib</groupId>
    <artifactId>persistent-event-listener</artifactId>
    <version>0.0.0-SNAPSHOT</version>

    <name>Library used for provide functionality to listen resource's persistent event and handle some actions</name>

    <url>https://github.com/fathom-io/com.fathom.lib.persistent-event-listener</url>
    <scm>
        <url>https://github.com/fathom-io/com.fathom.lib.persistent-event-listener</url>
    </scm>

    <parent>
        <groupId>com.fathom</groupId>
        <artifactId>libs</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <properties>
        <!-- Maven properties -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.version>3.13.0</maven.compiler.version>
        <!-- Third party properties -->
        <org.projectlombok.version>1.18.36</org.projectlombok.version>
    </properties>

    <dependencies>
        <!-- Fathom dependencies -->
        <dependency>
            <groupId>com.fathom.lib</groupId>
            <artifactId>common</artifactId>
            <version>5.2.2</version>
        </dependency>
        <!-- Kafka -->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <!-- Spring Framework -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <!-- Third party dependencies -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- Test dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.openrewrite.maven</groupId>
                <artifactId>rewrite-maven-plugin</artifactId>
                <version>5.46.1</version>
                <configuration>
                    <activeRecipes>
                        <recipe>org.openrewrite.java.spring.boot3.UpgradeSpringBoot_3_3</recipe>
                    </activeRecipes>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.openrewrite.recipe</groupId>
                        <artifactId>rewrite-spring</artifactId>
                        <version>5.24.1</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>com.spotify.fmt</groupId>
                <artifactId>fmt-maven-plugin</artifactId>
                <version>2.25</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${org.projectlombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.18.0</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>5.11.3</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <distributionManagement>
        <repository>
            <id>nexus</id>
            <url>https://nexus.fathom-solutions.com/repository/maven-mixed</url>
        </repository>
    </distributionManagement>
</project>
