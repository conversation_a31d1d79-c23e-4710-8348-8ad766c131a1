<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>platform-backend-java</artifactId>
        <groupId>com.fathom</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>libs</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>advancedfilters</module>
        <module>composition</module>
        <module>common</module>
        <module>common-utils</module>
        <module>director.util</module>
        <module>filters</module>
        <module>persistent-event-listener</module>
        <module>persistent-event-publisher</module>
        <module>diagnostics</module>
    </modules>
</project>