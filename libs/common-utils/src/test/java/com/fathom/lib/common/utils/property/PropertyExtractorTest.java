package com.fathom.lib.common.utils.property;

import static com.fathom.lib.common.utils.property.PropertyExtractor.getFramesPropertyValue;
import static com.fathom.lib.common.utils.property.PropertyExtractor.getRawValue;
import static com.fathom.lib.common.utils.property.PropertyExtractor.getRawValues;
import static com.fathom.lib.common.utils.property.PropertyExtractor.getValuesOfType;
import static org.junit.jupiter.api.Assertions.assertAll;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.fathom.lib.common.model.frame.Frame;
import com.fathom.lib.common.model.frame.data.PropertyData;
import com.fathom.lib.common.model.frame.data.type.ArrayValue;
import com.fathom.lib.common.model.frame.data.type.SingleValue;
import com.fathom.lib.common.model.frame.data.type.TupleValue;
import com.fathom.lib.common.model.frame.data.type.ValueType;
import com.fathom.lib.common.model.frame.meta.type.ContentType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;

class PropertyExtractorTest {

  /*
   * getFramesPropertyValue method tests
   */

  @Test
  void givenNullArguments_ShouldThrownExceptionOnGetFramesPropertyValue() {
    assertAll(
        () -> assertThrows(NullPointerException.class,
            () -> getFramesPropertyValue(null, "property", SingleValue.class)),
        () -> assertThrows(NullPointerException.class,
            () -> getFramesPropertyValue(new Frame(), null, SingleValue.class)),
        () -> assertThrows(NullPointerException.class,
            () -> getFramesPropertyValue(new Frame(), "property", null))
    );
  }

  @Test
  void givenUnexistedPropertyName_ShouldReturnEmptyOptionalOnGetFramesPropertyValue() {
    Frame frame = new Frame();
    frame.setData(new HashMap<>());

    Optional<SingleValue> actual = getFramesPropertyValue(frame,
        "unexisted_property", SingleValue.class);

    assertFalse(actual.isPresent());
  }

  @Test
  void givenExistedProperty_ShouldReturnPropertyValueOnGetFramesPropertyValue() {
    Frame frame = new Frame();
    frame.setData(new HashMap<>());

    PropertyData data = new PropertyData();

    SingleValue singleValue = new SingleValue();
    singleValue.setValue(10.5);
    singleValue.setValueType(ValueType.SINGLE_VALUE);
    singleValue.setType(ContentType.NUMBER);

    data.setValue(singleValue);

    String propertyName = "property";

    frame.setPropertyData(propertyName, data);

    Optional<SingleValue> actual =
        getFramesPropertyValue(frame, propertyName, SingleValue.class);
    assertTrue(actual.isPresent());
  }

  /*
   * getValuesOfType(TupleValue) method tests
   */

  @Test
  void givenNullArguments_ShouldThrownExceptionOnGetValuesOfTypeFromTupleValue() {
    assertAll(
        () -> assertThrows(NullPointerException.class,
            () -> getValuesOfType((TupleValue) null, SingleValue.class)),
        () -> assertThrows(NullPointerException.class,
            () -> getValuesOfType(new TupleValue(), null))
    );
  }

  @Test
  void givenTupleValueWithEmptyValues_ShouldReturnEmptyCollectionOnGetValuesOfType() {
    TupleValue value = new TupleValue();
    value.setValue(Collections.emptyList());
    value.setValueType(ValueType.TUPLE_VALUE);

    Collection<SingleValue> actual = getValuesOfType(value, SingleValue.class);
    assertNotNull(actual);
    assertTrue(actual.isEmpty());
  }

  @Test
  void givenTupleValueWithArrayValueValues_ShouldReturnEmtpyCollectionWhenRequestSingleValue() {
    TupleValue value = new TupleValue();

    ArrayValue arrayValue = new ArrayValue();
    arrayValue.setValue(Collections.emptyList());
    value.setValue(Collections.singletonList(arrayValue));

    value.setValueType(ValueType.TUPLE_VALUE);

    Collection<SingleValue> actual = getValuesOfType(value, SingleValue.class);
    assertNotNull(actual);
    assertTrue(actual.isEmpty());
  }

  @Test
  void givenTupleValue_ShouldReturnCollectionOfSingleValues() {
    TupleValue value = new TupleValue();

    SingleValue v1 = new SingleValue();
    v1.setType(ContentType.NUMBER);
    v1.setValueType(ValueType.SINGLE_VALUE);
    v1.setValue(1.0);
    v1.setUnit("Temperature:C");

    SingleValue v2 = new SingleValue();
    v2.setType(ContentType.NUMBER);
    v2.setValueType(ValueType.SINGLE_VALUE);
    v2.setValue(2.0);
    v2.setUnit("Temperature:F");

    value.setValue(Arrays.asList(v1, v2));
    value.setValueType(ValueType.TUPLE_VALUE);

    Collection<SingleValue> actual = getValuesOfType(value, SingleValue.class);
    assertNotNull(actual);
    assertEquals(2, actual.size());

    assertCollectionContains(actual, v1, v2);
  }

  /*
   * getValuesOfType(ArrayValue) method tests
   */

  @Test
  void givenNullArguments_ShouldThrownExceptionOnGetValuesOfTypeFromArrayValue() {
    assertAll(
        () -> assertThrows(NullPointerException.class,
            () -> getValuesOfType((ArrayValue) null, SingleValue.class)),
        () -> assertThrows(NullPointerException.class,
            () -> getValuesOfType(new ArrayValue(), null))
    );
  }

  @Test
  void givenArrayValueWithEmptyValues_ShouldReturnEmptyCollectionOnGetValuesOfType() {
    ArrayValue value = new ArrayValue();
    value.setValue(Collections.emptyList());
    value.setValueType(ValueType.ARRAY_VALUE);

    Collection<SingleValue> actual = getValuesOfType(value, SingleValue.class);
    assertNotNull(actual);
    assertTrue(actual.isEmpty());
  }

  @Test
  void givenArrayValueWithArrayValueValues_ShouldReturnEmtpyCollectionWhenRequestSingleValue() {
    ArrayValue value = new ArrayValue();

    ArrayValue arrayValue = new ArrayValue();
    arrayValue.setValue(Collections.emptyList());

    value.setValue(Collections.singletonList(arrayValue));
    value.setValueType(ValueType.ARRAY_VALUE);

    Collection<SingleValue> actual = getValuesOfType(value, SingleValue.class);
    assertNotNull(actual);
    assertTrue(actual.isEmpty());
  }

  @Test
  void givenArrayValue_ShouldReturnCollectionOfSingleValues() {
    ArrayValue value = new ArrayValue();

    SingleValue v1 = new SingleValue();
    v1.setType(ContentType.NUMBER);
    v1.setValueType(ValueType.SINGLE_VALUE);
    v1.setValue(1.0);
    v1.setUnit("Temperature:C");

    SingleValue v2 = new SingleValue();
    v2.setType(ContentType.NUMBER);
    v2.setValueType(ValueType.SINGLE_VALUE);
    v2.setValue(2.0);
    v2.setUnit("Temperature:F");

    value.setValue(Arrays.asList(v1, v2));
    value.setValueType(ValueType.ARRAY_VALUE);

    Collection<SingleValue> actual = getValuesOfType(value, SingleValue.class);
    assertNotNull(actual);
    assertEquals(2, actual.size());

    assertCollectionContains(actual, v1, v2);
  }

  /*
   * getRawValue method tests
   */

  @Test
  void givenNullArguments_ShouldThrowExceptionOfGetRawValue() {
    assertAll(
        () -> assertThrows(NullPointerException.class,
            () -> getRawValue(null, Number.class)),
        () -> assertThrows(NullPointerException.class,
            () -> getRawValue(new SingleValue(), null))
    );
  }

  @Test
  void givenSingleValue_ShouldReturnEmptyOptionalWhenForInvalidValueType() {
    SingleValue value = new SingleValue();
    value.setType(ContentType.STRING);
    value.setValue("value");

    Optional<Number> actual = getRawValue(value, Number.class);
    assertNotNull(actual);
    assertFalse(actual.isPresent());
  }

  @Test
  void givenSingleValue_ShouldReturnOptionalOfRawValue() {
    SingleValue value = new SingleValue();
    value.setType(ContentType.STRING);
    value.setValue("value");

    Optional<String> actual = getRawValue(value, String.class);
    assertNotNull(actual);
    assertTrue(actual.isPresent());
    assertEquals(value.getValue(), actual.get());
  }


  /*
   * getRawValues method tests
   */

  @Test
  void givenNullArguments_ShouldThrowExceptionOfGetRawValues() {
    assertAll(
        () -> assertThrows(NullPointerException.class,
            () -> getRawValues(null, Number.class)),
        () -> assertThrows(NullPointerException.class,
            () -> getRawValues(Collections.emptyList(), null))
    );
  }

  @Test
  void givenSingleValue_ShouldReturnEmptyCollectionWhenForInvalidValueType() {
    List<SingleValue> values = new ArrayList<>();

    SingleValue value = new SingleValue();
    value.setType(ContentType.STRING);
    value.setValue("value");

    values.add(value);

    Collection<Number> actual = getRawValues(values, Number.class);
    assertNotNull(actual);
    assertTrue(actual.isEmpty());
  }

  @Test
  void givenSingleValue_ShouldReturnCollectionOfRawValue() {
    List<SingleValue> values = new ArrayList<>();

    SingleValue v1 = new SingleValue();
    v1.setType(ContentType.STRING);
    v1.setValue("value-1");

    SingleValue v2 = new SingleValue();
    v2.setType(ContentType.STRING);
    v2.setValue("value-2");

    values.add(v1);
    values.add(v2);

    Collection<String> actual = getRawValues(values, String.class);
    assertNotNull(actual);
    assertEquals(2, actual.size());

    assertCollectionContains(actual, (String) v1.getValue(), (String) v2.getValue());
  }


  @SafeVarargs
  private static <T> void assertCollectionContains(
      Collection<T> source,
      T... expected) {
    boolean contains = true;
    for (T val : expected) {
      contains &= source.contains(val);
    }
    assertTrue(contains);
  }

}