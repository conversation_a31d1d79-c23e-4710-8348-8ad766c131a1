package com.fathom.lib.com.fathom.lib.director.util.models;

import lombok.Data;

@Data
public class Endpoint {
  String url;
  String method;
  String function;

  public Endpoint(String url, String method) {
    this.url = url;
    this.method = method;

    switch (this.method) {
      case "POST":
      case "PUT":
        this.function = "WRITE";
        break;
      case "GET":
        this.function = "READ";
        break;
      case "DELETE":
        this.function = "DELETE";
        break;
      default:
        break;
    }
  }

  public Endpoint() {}
}