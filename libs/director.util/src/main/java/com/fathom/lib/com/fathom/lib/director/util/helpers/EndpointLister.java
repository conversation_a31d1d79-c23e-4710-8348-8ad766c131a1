package com.fathom.lib.com.fathom.lib.director.util.helpers;

import com.fathom.lib.com.fathom.lib.director.util.models.Endpoint;
import com.fathom.lib.com.fathom.lib.director.util.models.ServiceInformationOutput;
import com.google.gson.Gson;
import java.io.FileWriter;
import java.io.Writer;
import java.util.*;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.condition.RequestMethodsRequestCondition;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

@Slf4j
public class EndpointLister {
  ServiceInformationOutput serviceInformationOutput;

  public EndpointLister(ApplicationContext context) {
    serviceInformationOutput = getServiceInformation(context);
  }

  public void toJsonFile() {
    saveObjectToJsonFile(this.serviceInformationOutput);
  }

  private void saveObjectToJsonFile(Object object) {
    try {
      Writer writer =
          new FileWriter(String.format("%s.json", this.serviceInformationOutput.getServiceName()));
      Gson gson = new Gson();
      gson.toJson(object, writer);
      writer.flush();
      writer.close();
    } catch (Exception e) {
      log.error(e.getMessage());
    }
  }

  public ServiceInformationOutput getServiceInformation(ApplicationContext context) {
    RequestMappingHandlerMapping mapping = context.getBean(RequestMappingHandlerMapping.class);
    Map<RequestMappingInfo, HandlerMethod> map = mapping.getHandlerMethods();

    Map<String, String> endpoints = new HashMap<>();
    List<Endpoint> result = new ArrayList<>();

    for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : map.entrySet()) {
      RequestMappingInfo info = entry.getKey();
      HandlerMethod method = entry.getValue();

      for (String pattern : info.getPatternsCondition().getPatterns()) {
        RequestMethodsRequestCondition methodsCondition = info.getMethodsCondition();

        StringBuilder sb = new StringBuilder();
        sb.append("Pattern: ").append(pattern);

        if (!methodsCondition.isEmpty()) {
          String[] methods =
              methodsCondition.getMethods().stream().map(Enum::name).toArray(String[]::new);

          result.addAll(
              Arrays.stream(methods)
                  .map(x -> new Endpoint(pattern, x))
                  .collect(Collectors.toList()));

          log.info(String.valueOf(methods.length));

          sb.append(", Methods: [").append(String.join(", ", methods)).append("]");
        } else {
          sb.append(", Methods: [ALL]");
        }
        endpoints.put(method.getMethod().getName(), sb.toString());
      }
    }

    log.info("List of endpoints for application id: {}:", context.getId());
    for (Map.Entry<String, String> entry : endpoints.entrySet()) {
      log.info(entry.getKey() + " - " + entry.getValue());
    }

    return ServiceInformationOutput.builder()
        .name(context.getId())
        .serviceDisplayName(capitalizeEachWord(String.format("%s Service", context.getId())))
        .description(capitalizeEachWord(String.format("%s Service Description", context.getId())))
        .serviceName(context.getId())
        .endpoints(result)
        .build();
  }

  private String capitalizeEachWord(String sentence) {

    if (sentence != null && !sentence.isEmpty()) {
      String[] words = sentence.split("\\s+");

      StringBuilder result = new StringBuilder();
      for (String word : words) {
        if (!word.isEmpty()) {
          result
              .append(Character.toUpperCase(word.charAt(0)))
              .append(word.substring(1))
              .append(" ");
        }
      }

      return result.toString().trim();
    } else {
      return "";
    }
  }
}
