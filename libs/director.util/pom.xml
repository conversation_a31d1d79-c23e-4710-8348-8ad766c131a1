<?xml version="1.0" encoding="UTF-8" ?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <build>
    <plugins>
      <plugin>
        <groupId>com.coveo</groupId>
        <artifactId>fmt-maven-plugin</artifactId>
        <version>2.9</version>
        <configuration>
          <sourceDirectory>src/main/java</sourceDirectory>
          <testSourceDirectory>src/test/java</testSourceDirectory>
          <verbose>false</verbose>
          <filesNamePattern>.*\.java</filesNamePattern>
          <skip>false</skip>
          <skipSortingImports>false</skipSortingImports>
          <style>google</style>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <annotationProcessorPaths>
            <path>
              <artifactId>lombok</artifactId>
              <groupId>org.projectlombok</groupId>
              <version>${org.projectlombok.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
        <groupId>org.apache.maven.plugins</groupId>
        <version>${maven.compiler.version}</version>
      </plugin>
      <plugin>
        <artifactId>maven-failsafe-plugin</artifactId>
        <groupId>org.apache.maven.plugins</groupId>
      </plugin>
      <plugin>
        <artifactId>maven-deploy-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <configuration>
          <nexusUrl>https://nexus.fathom-solutions.com</nexusUrl>
          <serverId>nexus</serverId>
          <skipStaging>true</skipStaging>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>deploy</goal>
            </goals>
            <id>default-deploy</id>
            <phase>deploy</phase>
          </execution>
        </executions>
        <groupId>org.sonatype.plugins</groupId>
        <version>${nexus-staging-maven-plugin.version}</version>
      </plugin>
      <plugin>
        <artifactId>sonar-maven-plugin</artifactId>
        <groupId>org.sonarsource.scanner.maven</groupId>
        <version>${sonar.maven.plugin.version}</version>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <argLine>${jacocoArgLine}</argLine>
          <reportsDirectory>${project.test.result.directory}/surefire</reportsDirectory>
        </configuration>
        <groupId>org.apache.maven.plugins</groupId>
        <version>${maven.surefire.plugin.version}</version>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <!--Spring -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>${lombok.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.9.0</version>
    </dependency>
  </dependencies>
  <distributionManagement>
    <repository>
      <id>nexus</id>
      <url>https://nexus.fathom-solutions.com/repository/maven-mixed</url>
    </repository>
  </distributionManagement>

  <modelVersion>4.0.0</modelVersion>

  <groupId>com.fathom.lib</groupId>
  <artifactId>director.util</artifactId>
  <version>1.0.0</version>

  <parent>
    <groupId>com.fathom</groupId>
    <artifactId>libs</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>

  <properties>
    <com.fasterxml.jackson.core.version>2.11.1</com.fasterxml.jackson.core.version>
    <com.fasterxml.jackson.datatype.version>2.11.1</com.fasterxml.jackson.datatype.version>
    <common.next.major.version>5.0</common.next.major.version>
    <commons-lang3.version>3.12.0</commons-lang3.version>
    <java.version>11</java.version>
    <maven.compiler.version>3.8.1</maven.compiler.version>
    <maven.surefire.plugin.version>3.0.0-M5</maven.surefire.plugin.version>
    <nexus-staging-maven-plugin.version>1.6.8</nexus-staging-maven-plugin.version>
    <org.projectlombok.version>1.18.30</org.projectlombok.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <sonar.maven.plugin.version>3.9.1.2184</sonar.maven.plugin.version>
    <spring.data.mongodb.version>3.2.3</spring.data.mongodb.version>
  </properties>
  <repositories>
    <repository>
      <id>nexus</id>
      <name>Nexus repository</name>
      <url>https://nexus.fathom-solutions.com/repository/maven-public/</url>
    </repository>
  </repositories>
</project>
