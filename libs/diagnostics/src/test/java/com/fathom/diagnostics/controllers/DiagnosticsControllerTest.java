package com.fathom.diagnostics.controllers;

import com.fathom.diagnostics.About;
import com.fathom.diagnostics.Health;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import com.fathom.diagnostics.configuration.DiagnosticProperties;

import java.time.Instant;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK)
@AutoConfigureMockMvc
class DiagnosticsControllerTest {
    @Autowired
    private MockMvc mockMvc;

    static final String SERVICE_NAME = "test-service";
    static final String VERSION = "1.2.3";
    static final String BUILD_TIMESTAMP = "2024-06-01T12:00:00Z";
    static final String GIT_HASH = "abc123";
    static final String GIT_BRANCH = "main";
    static final String GIT_COMMIT_TIMESTAMP = "2024-06-01T12:34:56Z";

    @DynamicPropertySource
    static void diagnosticsProperties(DynamicPropertyRegistry registry) {
        registry.add("diagnostics.service-name", () -> SERVICE_NAME);
        registry.add("diagnostics.version", () -> VERSION);
        registry.add("diagnostics.build-timestamp", () -> BUILD_TIMESTAMP);
        registry.add("diagnostics.git-hash", () -> GIT_HASH);
        registry.add("diagnostics.git-branch", () -> GIT_BRANCH);
        registry.add("diagnostics.git-commit-timestamp", () -> GIT_COMMIT_TIMESTAMP);
    }

    @Test
    void aboutEndpointReturnsConfiguredProperties() throws Exception {
        mockMvc.perform(get("/about"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.service_name").value(SERVICE_NAME))
            .andExpect(jsonPath("$.version").value(VERSION))
            .andExpect(jsonPath("$.build_timestamp").value(BUILD_TIMESTAMP))
            .andExpect(jsonPath("$.git_info.hash").value(GIT_HASH))
            .andExpect(jsonPath("$.git_info.branch").value(GIT_BRANCH))
            .andExpect(jsonPath("$.git_info.commit_timestamp").value(GIT_COMMIT_TIMESTAMP));
    }

    @Test
    void healthEndpointReturnsOk() throws Exception {
        mockMvc.perform(get("/health"))
            .andExpect(status().isOk());
    }
}



