package com.fathom.diagnostics.configuration;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.time.Instant;

@ConfigurationProperties(prefix = "diagnostics")
public class DiagnosticProperties {
    private final String serviceName;
    private final String version;
    private final Instant buildTimestamp;
    private final String gitHash;
    private final String gitBranch;
    private final Instant gitCommitTimestamp;


    public DiagnosticProperties(String serviceName, String version, Instant buildTimestamp, String gitHash, String gitBranch, Instant gitCommitTimestamp) {
        this.serviceName = serviceName;
        this.version = version;
        this.buildTimestamp = buildTimestamp;
        this.gitHash = gitHash;
        this.gitBranch = gitBranch;
        this.gitCommitTimestamp = gitCommitTimestamp;
    }

    public String getServiceName() {
        return serviceName;
    }

    public String getVersion() {
        return version;
    }

    public Instant getBuildTimestamp() {
        return buildTimestamp;
    }

    public String getGitHash() {
        return gitHash;
    }

    public String getGitBranch() {
        return gitBranch;
    }

    public Instant getGitCommitTimestamp() {
        return gitCommitTimestamp;
    }
}
