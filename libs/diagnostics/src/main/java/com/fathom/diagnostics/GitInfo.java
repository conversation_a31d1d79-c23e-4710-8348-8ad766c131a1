package com.fathom.diagnostics;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;

public class GitInfo{
        private final String hash;
        private final String branch;
        private final Instant commitTimestamp;

        public GitInfo(String hash, String branch, Instant commitTimestamp) {
                this.hash = hash;
                this.branch = branch;
                this.commitTimestamp = commitTimestamp;
        }

        public String getHash() {
                return hash;
        }

        public String getBranch() {
                return branch;
        }

        // <PERSON> will serialize the Instant property using its default serializer. For java.time.Instant, <PERSON> (with the JavaTimeModule registered, as in Spring Boot) serializes it as an ISO-8601 string (e.g., 2024-06-01T12:00:00Z), which is the result of Instant.toString().
        // This format is compatible with RFC3339 for UTC instants, as RFC3339 is a profile of ISO-8601.
        @JsonProperty("commit_timestamp")
        public Instant getCommitTimestamp() {
                return commitTimestamp;
        }

}
