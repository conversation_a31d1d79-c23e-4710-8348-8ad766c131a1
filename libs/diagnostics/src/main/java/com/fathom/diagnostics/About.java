package com.fathom.diagnostics;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fathom.diagnostics.configuration.DiagnosticProperties;

import java.time.Instant;

public record About(String serviceName, String version, Instant buildTimestamp, Instant requestTimestamp,
                    GitInfo gitInfo) {


    public static About fromDiagnosticProperties(DiagnosticProperties diag) {
        return new About(
                diag.getServiceName(),
                diag.getVersion(),
                diag.getBuildTimestamp(),
                Instant.now(),
                new GitInfo(
                        diag.getGitHash(),
                        diag.getGitBranch(),
                        diag.getGitCommitTimestamp()
                )
        );
    }


    @Override
    @JsonProperty("service_name")
    public String serviceName() {
        return serviceName;
    }

    @Override
    @JsonProperty("version")
    public String version() {
        return version;
    }

    // <PERSON> will serialize the Instant property using its default serializer. For java.time.Instant, <PERSON> (with the JavaTimeModule registered, as in Spring Boot) serializes it as an ISO-8601 string (e.g., 2024-06-01T12:00:00Z), which is the result of Instant.toString().
    // This format is compatible with RFC3339 for UTC instants, as RFC3339 is a profile of ISO-8601.
    @Override
    @JsonProperty("build_timestamp")
    public Instant buildTimestamp() {
        return buildTimestamp;
    }

    // Jackson will serialize the Instant property using its default serializer. For java.time.Instant, Jackson (with the JavaTimeModule registered, as in Spring Boot) serializes it as an ISO-8601 string (e.g., 2024-06-01T12:00:00Z), which is the result of Instant.toString().
    // This format is compatible with RFC3339 for UTC instants, as RFC3339 is a profile of ISO-8601.
    @Override
    @JsonProperty("request_timestamp")
    public Instant requestTimestamp() {
        return requestTimestamp;
    }

    @Override
    @JsonProperty("git_info")
    public GitInfo gitInfo() {
        return gitInfo;
    }


}