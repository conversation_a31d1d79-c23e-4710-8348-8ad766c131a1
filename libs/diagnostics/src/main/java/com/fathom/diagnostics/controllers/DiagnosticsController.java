package com.fathom.diagnostics.controllers;


import com.fathom.diagnostics.About;
import com.fathom.diagnostics.Health;
import com.fathom.diagnostics.configuration.DiagnosticProperties;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class DiagnosticsController {
    public DiagnosticsController(DiagnosticProperties diag) {
        this.diag = diag;
    }

    private final DiagnosticProperties diag;

    @GetMapping("/about")
    public ResponseEntity<About> about() {
        return ResponseEntity.ok(About.fromDiagnosticProperties(diag));
    }

    @GetMapping("/health")
    public ResponseEntity<Health> health() {
        return ResponseEntity.ok(new Health());
    }
}



