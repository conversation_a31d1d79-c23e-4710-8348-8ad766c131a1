# Diagnostics Library

This package provides diagnostics utilities for Java services in the Fathom platform. It is designed to be used as a shared library to enable diagnostics endpoints and health checks across microservices.

## Features
- Health check endpoints
- Diagnostics endpoints for service status
- Easy integration with Spring Boot applications

## Installation

Add the `libs/diagnostics` module as a dependency in your service's `pom.xml`:

```xml
<dependency>
    <groupId>com.fathom</groupId>
    <artifactId>diagnostics</artifactId>
    <version>1.0.0</version> <!-- Use the actual version -->
</dependency>
```

If using as a local module, ensure your parent `pom.xml` includes the `libs/diagnostics` module:

```xml
<module>libs/diagnostics</module>
```

## Usage

1. **configure diagnostics in your application.yaml:**

```yaml
diagnostics:
  service-name: "diagnostics"
  version: ${VERSION:0.0.1-dev}
  build-timestamp: ${BUILD_TIMESTAMP:1970-01-01T00:00:00Z}
  git-hash: ${GIT_HASH:unknown}
  git-branch: ${GIT_BRANCH:main}
  git-commit-timestamp: ${GIT_COMMIT_TIMESTAMP:1970-01-01T00:00:00Z}

```

2. **Import Diagnostics Configuration:**

In your Spring Boot application, import the diagnostics configuration:

```java
// ...existing code...
import com.fathom.diagnostics.DiagnosticsAutoConfiguration;
// ...existing code...
@SpringBootApplication(
    scanBasePackages = {"com.fathom", "your.package"}
)
public class YourApplication {
    // ...existing code...
}
```

3. **Access Diagnostics Endpoints:**

Once enabled, Fathom diagnostics endpoints will be available in your service.

## Development

To build the diagnostics library:

```sh
cd libs/diagnostics
./mvnw clean install
```

## NOTES:

This is not meant to be run as standalone application. The main is there to initialize context for testing
This should resemble closely how rust setup is now working. 
You need to worry yourself about injecting the versions and build timestamp, git hash, branch and commit timestamp.

