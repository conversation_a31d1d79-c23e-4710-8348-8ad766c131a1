package com.fathom.lib.composition.rabbitmq;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fathom.lib.composition.config.RabbitMQProperties;
import com.fathom.lib.composition.model.CompositionCreateMessage;
import java.util.Objects;

import com.fathom.lib.composition.model.CompositionResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class GenericRabbitMQListener {

  private final RabbitTemplate rabbitTemplate;
  private final ObjectMapper objectMapper = new ObjectMapper();

  // Autowired event handler
  @Autowired private ResourceEventHandler resourceEventHandler;
  @Autowired private RabbitMQProperties rabbitMQProperties;

  // Inject the event names for creation and rollback

  @Value("${spring.rabbitmq.exchange-name}")
  private String exchangeName;

  @Value("${spring.rabbitmq.create-resource-command}")
  private String createResourceCommand;

  @Value("${spring.rabbitmq.rollback-resource-command}")
  private String rollbackResourceCommand;

  @Value("${spring.rabbitmq.response-queue-name}")
  private String responseQueueName;

  @Value("${spring.rabbitmq.response-routing-key}")
  private String responseRoutingKey;

  @RabbitListener(queues = "#{rabbitMQProperties.queueName}")
  public void receiveMessage(String message) {
    try {
      CompositionCreateMessage command =
          objectMapper.readValue(message, CompositionCreateMessage.class);

      String resourceName = command.getCommand().getCommand();

      // Process create event
      if (Objects.equals(resourceName, createResourceCommand)) {
        CompositionResponse object = resourceEventHandler.onCreate(command);
        sendResponseMessage(object);
      }
      // Process rollback event
      else if (Objects.equals(resourceName, rollbackResourceCommand)) {
        CompositionResponse object = resourceEventHandler.onRollback(command);
        sendResponseMessage(object);
      } else {
        log.info("Unknown command: {}", resourceName);
      }
    } catch (JsonProcessingException e) {
      log.error("Failed to parse message JSON from RabbitMQ: {}", e.getMessage(), e);
    }
  }

  private void sendResponseMessage(CompositionResponse compositionResponse) {
    try {
      String responseMessage = objectMapper.writeValueAsString(compositionResponse);
      log.info(
          "Sending RabbitMQ message to Exchange: '{}' | Routing Key: '{}'",
          exchangeName,
          responseRoutingKey);

      rabbitTemplate.convertAndSend(exchangeName, responseRoutingKey, responseMessage);

      log.info("Sent response message: {}", responseMessage);
    } catch (Exception e) {
      log.error("Failed to send response message: {}", e.getMessage(), e);
    }
  }
}
