package com.fathom.lib.composition.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitMQConfig {

  @Value("${spring.rabbitmq.queue-name}")
  private String queueName;

  @Value("${spring.rabbitmq.exchange-name}")
  private String exchangeName;

  @Value("${spring.rabbitmq.response-queue-name}")
  private String responseQueueName;

  @Value("${spring.rabbitmq.request-routing-key}")
  private String requestRoutingKey;

  @Value("${spring.rabbitmq.response-routing-key}")
  private String responseRoutingKey;

  @Bean
  public TopicExchange exchange() {
    return new TopicExchange(exchangeName);
  }

  @Bean
  public Queue requestQueue() {
    return new Queue(queueName);
  }

  @Bean
  public Queue responseQueue() {
    return new Queue(responseQueueName);
  }

  @Bean
  public Binding requestBinding(Queue requestQueue, TopicExchange exchange) {
    return BindingBuilder.bind(requestQueue).to(exchange).with(requestRoutingKey);
  }

  @Bean
  public Binding responseBinding(Queue responseQueue, TopicExchange exchange) {
    return BindingBuilder.bind(responseQueue).to(exchange).with(responseRoutingKey);
  }
}
