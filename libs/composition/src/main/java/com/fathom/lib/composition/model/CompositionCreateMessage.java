package com.fathom.lib.composition.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompositionCreateMessage {
  @JsonProperty("command")
  private CompositionCreateMessageCommand command;

  @JsonProperty("templateId")
  private String templateId;

  @Data
  public static class CompositionCreateMessageCommand {
    @JsonProperty("command")
    private String command;

    @JsonProperty("id")
    private String id;

    @JsonProperty("params")
    private Map<String, Object> params;
  }
}
