
# Fathom Composition Library

This README provides instructions for integrating and using the **Fathom Composition Handler** library in your Spring Boot project. The library facilitates event handling through RabbitMQ for composition creation and rollback events, allowing you to define event handlers, process commands, and send responses.

## Overview

The **Fathom Composition Handler** library enables your Spring Boot application to interact with RabbitMQ to process composition events such as creation and rollback. You can define custom event handlers that respond to various commands and ensure smooth communication through RabbitMQ messaging.

In this guide, **organization creation** is used as an example of how to apply this library, but it can be easily adapted to any other resource or event.

## Project Structure

```
src/
  └── com/
      └── fathom/
          └── services/
              └── organization/
                  ├── config/
                  │   └── CompositionQueueHandlerConfig.java
                  ├── service/
                  │   └── OrganizationEventHandler.java
                  ├── model/
                  │   └── entity/
                  │       └── Organization.java
                  └── repositories/
                      └── OrganizationRepository.java
```

## Prerequisites

1. **Spring Boot Project**: An existing Spring Boot project.
2. **RabbitMQ**: RabbitMQ should be running locally or remotely.
3. **Spring AMQP**: Ensure that `spring-boot-starter-amqp` is included as a dependency.

### Maven Dependency:

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-amqp</artifactId>
</dependency>
```

### Gradle Dependency:

```gradle
implementation 'org.springframework.boot:spring-boot-starter-amqp'
```

## Setup

### 1. **Include the Library in Your Project**

Add the **Fathom Composition Handler** library to your project using the following Maven coordinates:

```xml
<dependency>
    <groupId>com.fathom.lib</groupId>
    <artifactId>composition</artifactId>
    <version>0.0.1</version>
</dependency>
```

Or, if you're using Gradle:

```gradle
implementation 'com.fathom.lib:composition:0.0.1'
```

### 2. **Configuration Class (`CompositionQueueHandlerConfig`)**

This configuration class sets up RabbitMQ beans and the necessary listener.

```java
@Configuration
public class CompositionQueueHandlerConfig {

  @Bean
  public GenericRabbitMQListener genericRabbitMQListener(RabbitTemplate rabbitTemplate) {
    return new GenericRabbitMQListener(rabbitTemplate);
  }

  @Bean
  public RabbitMQConfig rabbitMQConfig() {
    return new RabbitMQConfig();
  }

  @Bean
  public RabbitMQProperties rabbitMQProperties() {
    return new RabbitMQProperties();
  }
}
```

### 3. **Event Handler (`OrganizationEventHandler`)**

The `OrganizationEventHandler` class implements the `ResourceEventHandler` interface to handle `create` and `rollback` events for the organization resource.

```java
@Service
@Slf4j
@RequiredArgsConstructor
public class OrganizationEventHandler implements ResourceEventHandler {
  private final OrganizationRepository repository;

  @Override
  public CompositionResponse onCreate(CompositionCreateMessage command) {
    log.info("Creating organization with parameters: {}", command.getCommand().getParams());

    try {
      Organization newOrganization = mapToOrganization(command.getCommand().getParams());
      newOrganization.setId(UUID.fromString(command.getCommand().getId()));

      Organization organization = repository.save(newOrganization);

      return CompositionResponse.builder()
              .command(command.getCommand().getCommand())
              .id(organization.getId().toString())
              .templateId(command.getTemplateId())
              .status("SUCCESS")
              .notes("Organization created successfully.")
              .build();

    } catch (Exception e) {
      log.error("Failed to process organization creation: {}", e.getMessage(), e);

      return CompositionResponse.builder()
              .command(command.getCommand().getCommand())
              .id(command.getCommand().getId())
              .templateId(command.getTemplateId())
              .status("FAILED")
              .notes(String.format("Organization creation failed: %s", e.getMessage()))
              .build();
    }
  }

  @Override
  public CompositionResponse onRollback(CompositionCreateMessage command) {
    log.info("Rolling back organization creation for: {}", command.getCommand().getParams());
    return CompositionResponse.builder()
            .command(command.getCommand().getCommand())
            .id(command.getCommand().getId())
            .templateId(command.getTemplateId())
            .status("SUCCESS")
            .notes("Rollback successful.")
            .build();
  }

  private Organization mapToOrganization(Map<String, Object> params) {
    Organization organization = new Organization();
    organization.setName((String) params.get("name"));
    organization.setDisplayName((String) params.get("displayName"));
    organization.setOwnerEmail((String) params.get("ownerEmail"));
    organization.setDescription((String) params.get("description"));
    organization.setLogoUrl((String) params.get("logoUrl"));
    organization.setIndustry((String) params.get("industry"));
    organization.setHeadquarters((String) params.get("headquarters"));

    organization.setLongitude(convertToDouble(params.get("longitude")));
    organization.setLatitude(convertToDouble(params.get("latitude")));

    return organization;
  }

  private Double convertToDouble(Object value) {
    if (value instanceof Double) {
      return (Double) value;
    } else if (value instanceof Number) {
      return ((Number) value).doubleValue();
    } else if (value instanceof String) {
      try {
        return Double.parseDouble((String) value);
      } catch (NumberFormatException e) {
        log.warn("Failed to parse double value: {}", value);
      }
    }
    return null;
  }
}
```

### 4. **Repository (`OrganizationRepository`)**

The `OrganizationRepository` interfaces with the database to store organization entities.

```java
@Repository
public interface OrganizationRepository extends JpaRepository<Organization, UUID> {
}
```

### 5. **Model (`Organization`)**

The `Organization` entity represents the data for each organization.

```java
@Entity
public class Organization {

  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private UUID id;

  private String name;
  private String displayName;
  private String ownerEmail;
  private String description;
  private String logoUrl;
  private String industry;  
  private String headquarters;
  private Double longitude;
  private Double latitude;

  // Getters and Setters
}
```

### 6. **Application Configuration (`application.properties`)**

Configure RabbitMQ settings by adding the following properties to your `application.properties` or `application.yml`.

```properties
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    queue-name: fathom_message_queue  # Requests Queue
    response-queue-name: fathom_message_queue_response  # Responses Queue
    exchange-name: my_exchange
    request-routing-key: my_routing_key
    response-routing-key: my_routing_key_response
    create-resource-command: create-organization
    rollback-resource-command: rollback-organization
```

### 7. **How to Test**

1. **Send a Create Command:**

   Send a `CompositionCreateMessage` to RabbitMQ with the parameters for creating an organization. The `OrganizationEventHandler` will process the event and create the organization in the database.

   Example message:

   ```json
   {
     "command": {
       "command": "createOrganization",
       "id": "123e4567-e89b-12d3-a456-************",
       "params": {
         "name": "My Organization",
         "displayName": "My Org",
         "ownerEmail": "<EMAIL>",
         "description": "A new organization",
         "logoUrl": "http://example.com/logo.png",
         "industry": "Technology",
         "headquarters": "New York",
         "longitude": "-73.935242",
         "latitude": "40.730610"
       }
     },
     "templateId": "template123"
   }
   ```

2. **Receive the Response:**

   After processing the create command, a response will be sent back to RabbitMQ indicating the result.

   Example response:

   ```json
   {
     "command": "createOrganization",
     "id": "123e4567-e89b-12d3-a456-************",
     "templateId": "template123",
     "status": "SUCCESS",
     "notes": "Organization created successfully."
   }
   ```

### 8. **Conclusion**

The **Fathom Composition Handler** library allows you to manage composition events in a Spring Boot application using RabbitMQ. The example with **organization creation** is just one way the library can be used—adapt it to suit other use cases and resources as needed.

You can extend this setup to accommodate more complex use cases in your project.
